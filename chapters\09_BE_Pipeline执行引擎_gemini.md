# 第九章：BE Pipeline执行引擎架构

## 1. 引言

当FE将一个Plan Fragment下发到BE后，BE的执行引擎（Execution Engine）就接管了查询执行的最后一棒。传统的MPP数据库，如Impala，大多采用经典的Volcano“拉”模型（Pull-based），即上层算子不断地从下层算子`GetNext()`拉取数据。然而，这种模型在线程调度、CPU缓存利用率等方面存在瓶颈。为了追求极致的执行性能，StarRocks 3.0引入了全新的**Pipeline执行引擎**。这是一种更现代化的“推拉结合”模型，它通过将Fragment进一步切分为更细粒度的Pipeline，并引入多级并发调度，极大地提升了CPU利用效率和查询性能。本章将深入BE的执行核心，剖析Pipeline模型的架构与实现。

## 2. 从Volcano到Pipeline：执行模型的演进

*   **Volcano模型 (Pull-based)**：整个Fragment的执行由一个线程负责。该线程从根算子开始，调用`GetNext()`。根算子会递归地调用其子算子的`GetNext()`，直到最底层的Scan算子。数据像火山喷发一样，被一层层地从下往上“拉”动。
    *   **优点**：模型简单，易于理解和实现。
    *   **缺点**：
        1.  **阻塞**：任何一个算子的阻塞（如等待I/O或网络数据）都会导致整个执行线程阻塞。
        2.  **CPU缓存不友好**：算子之间频繁的虚函数调用（`GetNext()`）和上下文切换，导致CPU指令缓存和数据缓存的命中率下降。
        3.  **并发度受限**：并发度等于Fragment实例的数量，难以充分利用多核CPU。

*   **Pipeline模型 (Push/Pull Hybrid)**：Pipeline模型将Volcano模型中的阻塞点（如`ExchangeNode`、`HashJoinNode`的Build端）作为“切点”，将一个Fragment的计划树切分成若干个线性的**Pipeline**。
    *   每个Pipeline内部的算子之间采用**Push模型**，数据由上游算子处理完后，直接推送给下游算子，减少了函数调用开销。
    *   Pipeline之间的数据传递，则通过共享状态或队列来解耦。
    *   **优点**：
        1.  **非阻塞**：一个Pipeline的阻塞不会影响其他不相关的Pipeline的执行。
        2.  **高并发**：并发度等于Pipeline的数量，可以远超CPU核心数，从而通过多级调度将CPU“喂饱”。
        3.  **CPU缓存友好**：Pipeline内部的数据以批（Chunk）为单位，在算子间传递，形成了紧凑的`for`循环，有利于CPU的预取和缓存。

## 3. 源码分析：Pipeline执行引擎的核心组件

核心参考源码：
*   `be/src/exec/pipeline/fragment_executor.cpp`
*   `be/src/exec/pipeline/pipeline_driver.cpp`
*   `be/src/exec/pipeline/pipeline.h`
*   `be/src/exec/pipeline/operator.h`
*   `be/src/exec/pipeline/pipeline_driver_executor.h`

### 3.1. `FragmentExecutor`：Fragment的管理者

当`BackendServiceImpl`接收到`exec_plan_fragment` RPC后，它会调用`FragmentMgr`，`FragmentMgr`则会创建一个`FragmentExecutor`实例来管理这个Fragment的生命周期。`FragmentExecutor`负责初始化Fragment的执行环境，并将计划树转换为Pipeline DAG。

### 3.2. `Pipeline`：线性的执行单元

`FragmentExecutor`会遍历Fragment的计划树，根据阻塞点（`is_blocking_operator`）将其切分成多个`Pipeline`。每个`Pipeline`包含一组线性的、非阻塞的`Operator`。

### 3.3. `Operator`：计算的执行者

`Operator`是Pipeline模型中对物理算子的封装。它与Volcano模型中的`ExecNode`相对应，但接口设计有所不同。每个`Operator`都有一组标准接口：
*   `add_chunk()`: 接收上游`Operator`推送来的数据块（Chunk）。
*   `pull_chunk()`: `SourceOperator`（如`OlapScanOperator`）用于从数据源拉取数据。
*   `has_output()`: 判断当前`Operator`是否有可输出的数据。
*   `push_chunk()`: 将处理完的数据块推送给下游`Operator`。

### 3.4. `PipelineDriver`：Pipeline的“司机”

每个`Pipeline`在运行时，会关联一个或多个`PipelineDriver`。`PipelineDriver`是Pipeline执行的实际驱动者和状态机。它负责驱动其管理的`Pipeline`中的数据流动。一个`PipelineDriver`在一个时间点只会被一个工作线程执行，从而避免了`Pipeline`内部的锁竞争。

`PipelineDriver`的核心工作循环可以概括为：
1.  从`SourceOperator`拉取一个数据块（`pull_chunk`）。
2.  将数据块在`Pipeline`的`Operator`链中依次向下推送（`add_chunk` -> `push_chunk`）。
3.  如果`Pipeline`的`SinkOperator`（如`ResultSinkOperator`）处理完数据，则循环结束。
4.  如果中途发生阻塞（如等待I/O），`PipelineDriver`会记录当前状态，并把自己放回调度队列，让出CPU。

### 3.5. `PipelineDriverExecutor`：全局的调度器

`PipelineDriverExecutor`是BE中全局的Pipeline调度器。它内部维护了多个线程池和不同状态的`PipelineDriver`队列（如`ready_queue`, `blocked_queue`）。

*   工作线程（Worker Thread）会不断地从`ready_queue`中取出可执行的`PipelineDriver`。
*   然后，它会执行这个`Driver`的`process()`方法，驱动`Pipeline`的计算。
*   如果`Driver`执行完毕或遇到阻塞，工作线程就会将其放回相应的队列，并去取下一个`Driver`。

这种两级调度模式（`PipelineDriverExecutor`调度`PipelineDriver`，`PipelineDriver`驱动`Operator`）使得BE能够以极高的效率利用CPU资源。即使有成千上万个并发的`Pipeline`，调度器也能保证CPU核心始终在执行有数据可处理的计算任务。

## 4. 总结

StarRocks的Pipeline执行引擎是其实现极致查询性能的秘密武器。通过将传统的Fragment执行模型，演进为更细粒度的、并发度更高的Pipeline模型，StarRocks实现了执行过程的全面非阻塞化。`FragmentExecutor`, `Pipeline`, `Operator`, `PipelineDriver`, 和 `PipelineDriverExecutor`这五个核心组件，共同构成了一个精巧而高效的两级调度体系。这个体系能够最大化地压榨CPU的并行处理能力，减少不必要的等待和上下文切换，从而在现代多核CPU架构下，将查询执行的效率推向了新的高度。
