-- TPC-H Query 1: Pricing Summary Report
SELECT
    l_returnflag,
    l_linestatus,
    SUM(l_quantity) AS sum_qty,
    SUM(l_extendedprice) AS sum_base_price,
    SUM(l_extendedprice * (1 - l_discount)) AS sum_disc_price,
    SUM(l_extendedprice * (1 - l_discount) * (1 + l_tax)) AS sum_charge,
    AVG(l_quantity) AS avg_qty,
    AVG(l_extendedprice) AS avg_price,
    AVG(l_discount) AS avg_disc,
    COUNT(*) AS count_order
FROM
    lineitem
WHERE
    l_shipdate <= DATE '1998-12-01' - INTERVAL '90' DAY
GROUP BY
    l_returnflag,
    l_linestatus
ORDER BY
    l_returnflag,
    l_linestatus;

-- TPC-H Query 3: Shipping Priority Query
SELECT
    l_orderkey,
    SUM(l_extendedprice * (1 - l_discount)) AS revenue,
    o_orderdate,
    o_shippriority
FROM
    customer,
    orders,
    lineitem
WHERE
    c_mktsegment = 'BUILDING'
    AND c_custkey = o_custkey
    AND l_orderkey = o_orderkey
    AND o_orderdate < DATE '1995-03-15'
    AND l_shipdate > DATE '1995-03-15'
GROUP BY
    l_orderkey,
    o_orderdate,
    o_shippriority
ORDER BY
    revenue DESC,
    o_orderdate
LIMIT 10;

-- TPC-H Query 5: Local Supplier Volume Query
SELECT
    n_name,
    SUM(l_extendedprice * (1 - l_discount)) AS revenue
FROM
    customer,
    orders,
    lineitem,
    supplier,
    nation,
    region
WHERE
    c_custkey = o_custkey
    AND l_orderkey = o_orderkey
    AND l_suppkey = s_suppkey
    AND c_nationkey = s_nationkey
    AND s_nationkey = n_nationkey
    AND n_regionkey = r_regionkey
    AND r_name = 'ASIA'
    AND o_orderdate >= DATE '1994-01-01'
    AND o_orderdate < DATE '1994-01-01' + INTERVAL '1' YEAR
GROUP BY
    n_name
ORDER BY
    revenue DESC;

-- Complex query with CTEs and Window Functions
WITH monthly_sales AS (
    SELECT
        strftime('%Y-%m', o_orderdate) AS sales_month,
        p_partkey,
        SUM(l_extendedprice * (1 - l_discount)) AS monthly_revenue
    FROM
        orders
    JOIN
        lineitem ON l_orderkey = o_orderkey
    JOIN
        part ON p_partkey = l_partkey
    WHERE
        p_brand = 'Brand#23'
    GROUP BY
        1, 2
),
ranked_sales AS (
    SELECT
        sales_month,
        partkey,
        monthly_revenue,
        RANK() OVER (PARTITION BY sales_month ORDER BY monthly_revenue DESC) as sales_rank
    FROM
        monthly_sales
)
SELECT
    sales_month,
    partkey,
    monthly_revenue
FROM
    ranked_sales
WHERE
    sales_rank <= 3
ORDER BY
    sales_month,
    sales_rank;