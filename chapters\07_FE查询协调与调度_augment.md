# 第七章：FE查询协调与调度

## 引言

查询协调与调度是分布式数据库系统的核心功能，负责将查询计划分发到各个BE节点并协调执行过程。StarRocks的FE采用了先进的协调器架构，通过`DefaultCoordinator`和`StmtExecutor`等核心组件，实现了高效的分布式查询执行协调。本章将深入分析StarRocks的查询协调与调度机制。

## 7.1 协调器架构概览

### 7.1.1 协调流程架构

StarRocks的查询协调采用中心化协调模式：

```
查询请求 → 计划分发 → 实例启动 → 执行监控 → 结果收集 → 响应返回
    ↓        ↓        ↓        ↓        ↓        ↓
  FE接收 → BE分发 → Fragment启动 → 状态跟踪 → 数据汇聚 → 客户端响应
```

### 7.1.2 核心组件分析

基于`DefaultCoordinator.java`的源码分析：

```java
public class DefaultCoordinator implements Coordinator {
    private final ConnectContext connectContext;
    private final ExecPlan execPlan;
    private final List<PlanFragment> fragments;
    private final Map<PlanFragmentId, FragmentExecParams> fragmentExecParamsMap;
    private final List<ScanNode> scanNodes;
    
    // 执行状态管理
    private final QueryStatisticsItem statisticsItem;
    private volatile boolean isCancel = false;
    private volatile boolean isDone = false;
    
    // 结果接收器
    private ResultReceiver receiver;
    
    public DefaultCoordinator(ConnectContext context, ExecPlan execPlan) {
        this.connectContext = context;
        this.execPlan = execPlan;
        this.fragments = execPlan.getFragments();
        this.fragmentExecParamsMap = new HashMap<>();
        this.scanNodes = execPlan.getScanNodes();
        this.statisticsItem = new QueryStatisticsItem();
    }
    
    @Override
    public void exec() throws Exception {
        try {
            // 1. 准备执行参数
            prepareExecution();
            
            // 2. 计算Fragment实例
            computeFragmentExecParams();
            
            // 3. 发送查询计划到BE
            sendFragments();
            
            // 4. 等待执行完成
            waitForCompletion();
            
        } catch (Exception e) {
            // 执行失败，取消所有Fragment
            cancel();
            throw e;
        } finally {
            // 清理资源
            cleanup();
        }
    }
    
    private void prepareExecution() throws Exception {
        // 1. 设置查询ID
        TUniqueId queryId = connectContext.getExecutionId();
        
        // 2. 创建结果接收器
        if (needsResultReceiver()) {
            receiver = new ResultReceiver(queryId, connectContext.getResultBufferSize());
        }
        
        // 3. 初始化统计信息
        statisticsItem.setQueryId(queryId);
        statisticsItem.setQueryStartTime(System.currentTimeMillis());
        
        // 4. 设置查询选项
        setQueryOptions();
    }
}
```

这个协调器架构体现了分布式查询执行的系统性方法：
- **中心化协调**: FE作为协调中心，统一管理查询执行
- **状态跟踪**: 实时监控各个Fragment的执行状态
- **错误处理**: 完善的异常处理和资源清理机制
- **性能监控**: 详细的执行统计和性能指标收集

## 7.2 Fragment实例计算

### 7.2.1 实例分配算法

Fragment实例的计算和分配：

```java
private void computeFragmentExecParams() throws Exception {
    // 1. 按照依赖关系排序Fragment
    List<PlanFragment> sortedFragments = topologicalSort(fragments);
    
    // 2. 为每个Fragment计算执行参数
    for (PlanFragment fragment : sortedFragments) {
        FragmentExecParams params = computeFragmentParams(fragment);
        fragmentExecParamsMap.put(fragment.getFragmentId(), params);
    }
    
    // 3. 设置Fragment间的数据流
    setupDataStreams();
}

private FragmentExecParams computeFragmentParams(PlanFragment fragment) throws Exception {
    FragmentExecParams params = new FragmentExecParams(fragment);
    
    // 1. 计算实例数量
    int instanceNum = fragment.getParallelism();
    
    // 2. 选择执行节点
    List<TNetworkAddress> hosts = selectExecutionHosts(fragment, instanceNum);
    
    // 3. 为每个实例分配参数
    for (int i = 0; i < instanceNum; i++) {
        FInstanceExecParam instanceParam = new FInstanceExecParam();
        instanceParam.instanceId = new TUniqueId(connectContext.getExecutionId().hi, 
                                                connectContext.getExecutionId().lo + i);
        instanceParam.host = hosts.get(i % hosts.size());
        
        // 4. 分配扫描范围
        if (fragment.getPlanRoot() instanceof ScanNode) {
            assignScanRanges(instanceParam, (ScanNode) fragment.getPlanRoot(), i, instanceNum);
        }
        
        params.instanceExecParams.add(instanceParam);
    }
    
    return params;
}

private List<TNetworkAddress> selectExecutionHosts(PlanFragment fragment, int instanceNum) {
    List<TNetworkAddress> selectedHosts = new ArrayList<>();
    
    if (fragment.getPlanRoot() instanceof ScanNode) {
        // 扫描Fragment：选择数据所在的BE节点
        selectedHosts = selectHostsForScan((ScanNode) fragment.getPlanRoot(), instanceNum);
    } else if (fragment.getPlanRoot() instanceof ExchangeNode) {
        // Exchange Fragment：基于负载均衡选择节点
        selectedHosts = selectHostsForExchange(instanceNum);
    } else {
        // 其他Fragment：随机选择可用节点
        selectedHosts = selectRandomHosts(instanceNum);
    }
    
    return selectedHosts;
}

private List<TNetworkAddress> selectHostsForScan(ScanNode scanNode, int instanceNum) {
    if (scanNode instanceof OlapScanNode) {
        OlapScanNode olapScan = (OlapScanNode) scanNode;
        
        // 1. 获取Tablet分布信息
        List<Long> tabletIds = olapScan.getSelectedTabletIds();
        Map<TNetworkAddress, List<Long>> hostToTablets = getTabletDistribution(tabletIds);
        
        // 2. 基于数据本地性选择节点
        List<TNetworkAddress> hosts = new ArrayList<>();
        for (Map.Entry<TNetworkAddress, List<Long>> entry : hostToTablets.entrySet()) {
            TNetworkAddress host = entry.getKey();
            int tabletCount = entry.getValue().size();
            
            // 根据Tablet数量决定实例数
            int hostInstanceNum = Math.max(1, tabletCount / (tabletIds.size() / instanceNum));
            for (int i = 0; i < hostInstanceNum; i++) {
                hosts.add(host);
            }
        }
        
        return hosts;
    }
    
    return selectRandomHosts(instanceNum);
}
```

### 7.2.2 扫描范围分配

扫描范围的分配是性能优化的关键：

```java
private void assignScanRanges(FInstanceExecParam instanceParam, 
                            ScanNode scanNode, 
                            int instanceIndex, 
                            int totalInstances) {
    
    if (scanNode instanceof OlapScanNode) {
        assignOlapScanRanges(instanceParam, (OlapScanNode) scanNode, instanceIndex, totalInstances);
    } else if (scanNode instanceof ExternalScanNode) {
        assignExternalScanRanges(instanceParam, (ExternalScanNode) scanNode, instanceIndex, totalInstances);
    }
}

private void assignOlapScanRanges(FInstanceExecParam instanceParam,
                                OlapScanNode olapScan,
                                int instanceIndex,
                                int totalInstances) {
    
    List<Long> allTabletIds = olapScan.getSelectedTabletIds();
    List<Long> assignedTabletIds = new ArrayList<>();
    
    // 1. 基于实例索引分配Tablet
    for (int i = instanceIndex; i < allTabletIds.size(); i += totalInstances) {
        assignedTabletIds.add(allTabletIds.get(i));
    }
    
    // 2. 为每个Tablet创建扫描范围
    for (Long tabletId : assignedTabletIds) {
        TScanRangeParams scanRangeParams = new TScanRangeParams();
        
        // 3. 设置Tablet信息
        TInternalScanRange internalScanRange = new TInternalScanRange();
        internalScanRange.tablet_id = tabletId;
        
        // 4. 选择副本
        Tablet tablet = GlobalStateMgr.getCurrentInvertedIndex().getTablet(tabletId);
        List<Replica> replicas = tablet.getReplicas();
        Replica selectedReplica = selectBestReplica(replicas, instanceParam.host);
        
        internalScanRange.hosts = Lists.newArrayList(
            new TNetworkAddress(selectedReplica.getBackendId()));
        
        scanRangeParams.scan_range = new TScanRange();
        scanRangeParams.scan_range.internal_scan_range = internalScanRange;
        
        instanceParam.scanRangeParams.add(scanRangeParams);
    }
}

private Replica selectBestReplica(List<Replica> replicas, TNetworkAddress preferredHost) {
    // 1. 优先选择本地副本
    for (Replica replica : replicas) {
        Backend backend = GlobalStateMgr.getCurrentSystemInfo().getBackend(replica.getBackendId());
        if (backend != null && backend.getHost().equals(preferredHost.hostname)) {
            return replica;
        }
    }
    
    // 2. 选择健康的副本
    for (Replica replica : replicas) {
        if (replica.isAlive() && replica.getState() == Replica.ReplicaState.NORMAL) {
            return replica;
        }
    }
    
    // 3. 返回第一个可用副本
    return replicas.get(0);
}
```

### 7.2.3 数据流设置

Fragment间数据流的设置：

```java
private void setupDataStreams() {
    for (PlanFragment fragment : fragments) {
        setupFragmentDataStreams(fragment);
    }
}

private void setupFragmentDataStreams(PlanFragment fragment) {
    PlanNode root = fragment.getPlanRoot();
    
    if (root instanceof ExchangeNode) {
        ExchangeNode exchangeNode = (ExchangeNode) root;
        setupExchangeDataStream(exchangeNode);
    }
    
    // 递归处理子节点
    for (PlanNode child : root.getChildren()) {
        if (child instanceof ExchangeNode) {
            setupExchangeDataStream((ExchangeNode) child);
        }
    }
}

private void setupExchangeDataStream(ExchangeNode exchangeNode) {
    PlanFragment inputFragment = exchangeNode.getInputFragment();
    FragmentExecParams inputParams = fragmentExecParamsMap.get(inputFragment.getFragmentId());
    
    // 1. 设置数据发送端
    for (FInstanceExecParam inputInstance : inputParams.instanceExecParams) {
        TDataStreamSink sink = new TDataStreamSink();
        sink.dest_node_id = exchangeNode.getId().asInt();
        sink.output_partition = exchangeNode.getOutputPartition().toThrift();
        
        // 2. 设置接收端信息
        FragmentExecParams outputParams = fragmentExecParamsMap.get(
            exchangeNode.getFragment().getFragmentId());
        
        for (FInstanceExecParam outputInstance : outputParams.instanceExecParams) {
            TNetworkAddress dest = new TNetworkAddress();
            dest.hostname = outputInstance.host.hostname;
            dest.port = outputInstance.host.port;
            sink.destinations.add(dest);
        }
        
        inputInstance.dataSink = sink;
    }
    
    // 3. 设置数据接收端
    FragmentExecParams outputParams = fragmentExecParamsMap.get(
        exchangeNode.getFragment().getFragmentId());
    
    for (FInstanceExecParam outputInstance : outputParams.instanceExecParams) {
        TDataStreamSource source = new TDataStreamSource();
        source.sender_id = exchangeNode.getId().asInt();
        source.plan_node_id = exchangeNode.getId().asInt();
        
        outputInstance.dataStreamSources.add(source);
    }
}
```

## 7.3 查询执行监控

### 7.3.1 执行状态跟踪

查询执行状态的实时跟踪：

```java
public class QueryExecutionTracker {
    private final Map<TUniqueId, FragmentInstanceInfo> instanceInfoMap = new ConcurrentHashMap<>();
    private final AtomicInteger completedInstances = new AtomicInteger(0);
    private final AtomicInteger failedInstances = new AtomicInteger(0);
    
    public void trackFragmentInstance(TUniqueId instanceId, FragmentInstanceInfo info) {
        instanceInfoMap.put(instanceId, info);
    }
    
    public void updateInstanceStatus(TUniqueId instanceId, TFragmentInstanceExecStatus status) {
        FragmentInstanceInfo info = instanceInfoMap.get(instanceId);
        if (info != null) {
            info.updateStatus(status);
            
            // 更新全局计数器
            if (status.status == TStatusCode.OK) {
                if (info.isCompleted()) {
                    completedInstances.incrementAndGet();
                }
            } else {
                failedInstances.incrementAndGet();
            }
        }
    }
    
    public boolean isQueryCompleted() {
        return completedInstances.get() == instanceInfoMap.size();
    }
    
    public boolean hasFailedInstances() {
        return failedInstances.get() > 0;
    }
    
    public List<FragmentInstanceInfo> getFailedInstances() {
        return instanceInfoMap.values().stream()
                             .filter(FragmentInstanceInfo::isFailed)
                             .collect(Collectors.toList());
    }
}

public class FragmentInstanceInfo {
    private final TUniqueId instanceId;
    private final TNetworkAddress host;
    private volatile TFragmentInstanceExecStatus lastStatus;
    private volatile long startTime;
    private volatile long endTime;
    private volatile boolean completed = false;
    private volatile boolean failed = false;
    
    public void updateStatus(TFragmentInstanceExecStatus status) {
        this.lastStatus = status;
        
        if (status.status == TStatusCode.OK) {
            if (status.done) {
                this.completed = true;
                this.endTime = System.currentTimeMillis();
            }
        } else {
            this.failed = true;
            this.endTime = System.currentTimeMillis();
        }
    }
    
    public long getExecutionTime() {
        if (endTime > 0) {
            return endTime - startTime;
        } else {
            return System.currentTimeMillis() - startTime;
        }
    }
}
```

### 7.3.2 性能指标收集

查询执行性能指标的收集：

```java
public class QueryMetricsCollector {
    private final QueryStatisticsItem statisticsItem;
    private final Map<TUniqueId, RuntimeProfile> instanceProfiles = new ConcurrentHashMap<>();
    
    public void collectMetrics(TUniqueId instanceId, TReportExecStatusParams params) {
        // 1. 收集基本执行信息
        if (params.isSetProfile()) {
            RuntimeProfile profile = RuntimeProfile.parseFrom(params.profile);
            instanceProfiles.put(instanceId, profile);
        }
        
        // 2. 更新统计信息
        updateStatistics(params);
        
        // 3. 收集资源使用信息
        collectResourceUsage(params);
    }
    
    private void updateStatistics(TReportExecStatusParams params) {
        if (params.isSetLoad_counters()) {
            TLoadCounters counters = params.load_counters;
            
            // 更新扫描统计
            statisticsItem.scanRows += counters.dpp_normal_all;
            statisticsItem.scanBytes += counters.loaded_bytes;
            
            // 更新过滤统计
            statisticsItem.filteredRows += counters.dpp_abnormal_all;
        }
        
        if (params.isSetDelta_urls()) {
            // 更新增量数据信息
            statisticsItem.deltaUrls.addAll(params.delta_urls);
        }
    }
    
    private void collectResourceUsage(TReportExecStatusParams params) {
        if (params.isSetRuntime_profile()) {
            RuntimeProfile profile = RuntimeProfile.parseFrom(params.runtime_profile);
            
            // 收集CPU使用率
            Counter cpuCounter = profile.getCounter("TotalCpuTime");
            if (cpuCounter != null) {
                statisticsItem.cpuTimeMs += cpuCounter.getValue();
            }
            
            // 收集内存使用
            Counter memoryCounter = profile.getCounter("PeakMemoryUsage");
            if (memoryCounter != null) {
                statisticsItem.peakMemoryBytes = Math.max(statisticsItem.peakMemoryBytes, 
                                                        memoryCounter.getValue());
            }
            
            // 收集IO统计
            Counter ioCounter = profile.getCounter("BytesRead");
            if (ioCounter != null) {
                statisticsItem.ioBytes += ioCounter.getValue();
            }
        }
    }
    
    public RuntimeProfile mergeProfiles() {
        RuntimeProfile mergedProfile = new RuntimeProfile("Query");
        
        for (RuntimeProfile instanceProfile : instanceProfiles.values()) {
            mergedProfile.merge(instanceProfile);
        }
        
        return mergedProfile;
    }
}
```

### 7.3.3 错误处理机制

查询执行的错误处理：

```java
public class QueryErrorHandler {
    private final DefaultCoordinator coordinator;
    private final List<String> errorMessages = new ArrayList<>();
    private volatile boolean hasError = false;
    
    public void handleInstanceError(TUniqueId instanceId, String errorMessage) {
        synchronized (errorMessages) {
            errorMessages.add(String.format("Instance %s: %s", instanceId.toString(), errorMessage));
            hasError = true;
        }
        
        // 根据错误策略决定是否取消查询
        if (shouldCancelQuery(errorMessage)) {
            coordinator.cancel();
        }
    }
    
    private boolean shouldCancelQuery(String errorMessage) {
        // 1. 致命错误立即取消
        if (errorMessage.contains("OutOfMemory") || 
            errorMessage.contains("DiskSpaceExhausted") ||
            errorMessage.contains("NetworkError")) {
            return true;
        }
        
        // 2. 检查错误实例比例
        int totalInstances = coordinator.getTotalInstanceCount();
        int errorInstances = getErrorInstanceCount();
        
        double errorRate = (double) errorInstances / totalInstances;
        if (errorRate > coordinator.getConnectContext().getSessionVariable().getQueryErrorTolerance()) {
            return true;
        }
        
        return false;
    }
    
    public void handleTimeout() {
        String timeoutMessage = String.format("Query timeout after %d seconds", 
                                            coordinator.getConnectContext().getSessionVariable().getQueryTimeoutS());
        
        synchronized (errorMessages) {
            errorMessages.add(timeoutMessage);
            hasError = true;
        }
        
        coordinator.cancel();
    }
    
    public String getErrorSummary() {
        if (!hasError) {
            return null;
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("Query execution failed with following errors:\n");
        
        synchronized (errorMessages) {
            for (String error : errorMessages) {
                summary.append("- ").append(error).append("\n");
            }
        }
        
        return summary.toString();
    }
}
```

## 7.4 结果处理机制

### 7.4.1 结果接收器

查询结果的接收和处理：

```java
public class ResultReceiver {
    private final TUniqueId queryId;
    private final BlockingQueue<RowBatch> resultQueue;
    private final AtomicBoolean isFinished = new AtomicBoolean(false);
    private final AtomicBoolean isCancelled = new AtomicBoolean(false);
    
    public ResultReceiver(TUniqueId queryId, int bufferSize) {
        this.queryId = queryId;
        this.resultQueue = new LinkedBlockingQueue<>(bufferSize);
    }
    
    public void addBatch(RowBatch batch) throws InterruptedException {
        if (isCancelled.get()) {
            return;
        }
        
        // 将结果批次添加到队列
        resultQueue.put(batch);
        
        // 检查是否是最后一个批次
        if (batch.isEos()) {
            isFinished.set(true);
        }
    }
    
    public RowBatch getNext() throws InterruptedException {
        return resultQueue.take();
    }
    
    public boolean hasNext() {
        return !isFinished.get() || !resultQueue.isEmpty();
    }
    
    public void cancel() {
        isCancelled.set(true);
        
        // 清空队列
        resultQueue.clear();
        
        // 添加取消标记
        try {
            resultQueue.put(RowBatch.createCancelledBatch());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

public class RowBatch {
    private final List<List<Object>> rows;
    private final boolean isEos;
    private final boolean isCancelled;
    
    public RowBatch(List<List<Object>> rows, boolean isEos) {
        this.rows = rows;
        this.isEos = isEos;
        this.isCancelled = false;
    }
    
    private RowBatch(boolean isCancelled) {
        this.rows = Collections.emptyList();
        this.isEos = true;
        this.isCancelled = isCancelled;
    }
    
    public static RowBatch createCancelledBatch() {
        return new RowBatch(true);
    }
    
    public int getRowCount() {
        return rows.size();
    }
    
    public List<Object> getRow(int index) {
        return rows.get(index);
    }
}
```

### 7.4.2 结果流式处理

大结果集的流式处理：

```java
public class StreamingResultProcessor {
    private final ResultReceiver receiver;
    private final ConnectContext connectContext;
    private final ResultSetMetaData metaData;
    
    public void processResults(MysqlChannel channel) throws Exception {
        try {
            // 1. 发送结果集元数据
            sendMetaData(channel);
            
            // 2. 流式发送结果数据
            streamResultData(channel);
            
            // 3. 发送结束标记
            sendEofPacket(channel);
            
        } catch (Exception e) {
            // 发送错误信息
            sendErrorPacket(channel, e.getMessage());
            throw e;
        }
    }
    
    private void streamResultData(MysqlChannel channel) throws Exception {
        long totalRows = 0;
        long startTime = System.currentTimeMillis();
        
        while (receiver.hasNext()) {
            RowBatch batch = receiver.getNext();
            
            if (batch.isCancelled()) {
                break;
            }
            
            // 发送批次数据
            for (int i = 0; i < batch.getRowCount(); i++) {
                List<Object> row = batch.getRow(i);
                sendRow(channel, row);
                totalRows++;
                
                // 检查是否需要限流
                if (shouldThrottle(totalRows, startTime)) {
                    Thread.sleep(getThrottleDelayMs());
                }
            }
            
            // 检查客户端连接状态
            if (channel.isClosed()) {
                receiver.cancel();
                break;
            }
        }
    }
    
    private boolean shouldThrottle(long totalRows, long startTime) {
        // 基于发送速率决定是否需要限流
        long elapsedTime = System.currentTimeMillis() - startTime;
        if (elapsedTime == 0) {
            return false;
        }
        
        long rowsPerSecond = totalRows * 1000 / elapsedTime;
        long maxRowsPerSecond = connectContext.getSessionVariable().getMaxResultRowsPerSecond();
        
        return rowsPerSecond > maxRowsPerSecond;
    }
    
    private void sendRow(MysqlChannel channel, List<Object> row) throws Exception {
        // 将行数据转换为MySQL协议格式
        MysqlRowPacket rowPacket = new MysqlRowPacket();
        
        for (Object value : row) {
            if (value == null) {
                rowPacket.addNullValue();
            } else {
                rowPacket.addValue(value.toString().getBytes(StandardCharsets.UTF_8));
            }
        }
        
        channel.sendPacket(rowPacket);
    }
}
```

## 7.5 查询取消机制

### 7.5.1 取消信号传播

查询取消信号的传播机制：

```java
public class QueryCancellationManager {
    private final Map<TUniqueId, CancellationToken> queryTokens = new ConcurrentHashMap<>();
    
    public void registerQuery(TUniqueId queryId, DefaultCoordinator coordinator) {
        CancellationToken token = new CancellationToken(queryId, coordinator);
        queryTokens.put(queryId, token);
    }
    
    public void cancelQuery(TUniqueId queryId, String reason) {
        CancellationToken token = queryTokens.get(queryId);
        if (token != null) {
            token.cancel(reason);
        }
    }
    
    public void unregisterQuery(TUniqueId queryId) {
        queryTokens.remove(queryId);
    }
    
    private static class CancellationToken {
        private final TUniqueId queryId;
        private final DefaultCoordinator coordinator;
        private volatile boolean isCancelled = false;
        private volatile String cancellationReason;
        
        public CancellationToken(TUniqueId queryId, DefaultCoordinator coordinator) {
            this.queryId = queryId;
            this.coordinator = coordinator;
        }
        
        public void cancel(String reason) {
            if (!isCancelled) {
                this.isCancelled = true;
                this.cancellationReason = reason;
                
                // 异步执行取消操作
                CompletableFuture.runAsync(() -> {
                    try {
                        coordinator.cancel();
                    } catch (Exception e) {
                        LOG.warn("Failed to cancel query " + queryId, e);
                    }
                });
            }
        }
    }
}
```

### 7.5.2 资源清理

查询取消后的资源清理：

```java
private void cleanup() {
    try {
        // 1. 取消所有Fragment实例
        cancelAllInstances();
        
        // 2. 清理结果接收器
        if (receiver != null) {
            receiver.cancel();
        }
        
        // 3. 清理临时资源
        cleanupTempResources();
        
        // 4. 更新统计信息
        updateFinalStatistics();
        
        // 5. 注销查询
        QueryCancellationManager.getInstance().unregisterQuery(connectContext.getExecutionId());
        
    } catch (Exception e) {
        LOG.warn("Error during query cleanup", e);
    }
}

private void cancelAllInstances() {
    List<Future<Void>> cancelFutures = new ArrayList<>();
    
    for (FragmentExecParams params : fragmentExecParamsMap.values()) {
        for (FInstanceExecParam instanceParam : params.instanceExecParams) {
            Future<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    cancelInstance(instanceParam);
                } catch (Exception e) {
                    LOG.warn("Failed to cancel instance " + instanceParam.instanceId, e);
                }
            });
            cancelFutures.add(future);
        }
    }
    
    // 等待所有取消操作完成
    for (Future<Void> future : cancelFutures) {
        try {
            future.get(5, TimeUnit.SECONDS); // 5秒超时
        } catch (Exception e) {
            LOG.warn("Cancel operation timeout or failed", e);
        }
    }
}

private void cancelInstance(FInstanceExecParam instanceParam) throws Exception {
    Backend backend = GlobalStateMgr.getCurrentSystemInfo()
                                   .getBackendWithBePort(instanceParam.host.hostname, 
                                                       instanceParam.host.port);
    
    if (backend != null && backend.isAlive()) {
        TCancelPlanFragmentParams cancelParams = new TCancelPlanFragmentParams();
        cancelParams.fragment_instance_id = instanceParam.instanceId;
        cancelParams.cancel_reason = "Query cancelled by user";
        
        BackendServiceClient client = new BackendServiceClient();
        client.cancelPlanFragment(backend.getBeRpcAddress(), cancelParams);
    }
}
```

## 小结

StarRocks的FE查询协调与调度系统实现了高效的分布式查询执行管理，通过智能的实例分配、实时的状态监控和完善的错误处理机制，确保了查询的可靠执行。其设计特点包括：

1. **中心化协调**: FE统一协调所有BE节点的查询执行
2. **智能实例分配**: 基于数据本地性和负载均衡的实例分配算法
3. **实时状态监控**: 全面的执行状态跟踪和性能指标收集
4. **完善的错误处理**: 多层次的错误检测和恢复机制
5. **高效的结果处理**: 流式结果处理和资源管理

在下一章中，我们将深入分析FE-BE通信协议与接口设计，了解StarRocks分布式组件间的通信机制。
