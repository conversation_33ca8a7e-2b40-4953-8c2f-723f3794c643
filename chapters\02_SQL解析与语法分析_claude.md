# 第二章：SQL解析与语法分析

## 引言

SQL解析是数据库查询处理的第一步，也是最为关键的基础环节。StarRocks采用了基于ANTLR4的高性能SQL解析器，不仅支持标准SQL语法，还扩展了大量适用于OLAP场景的特殊语法。本章将深入分析StarRocks SQL解析器的设计原理、实现机制以及扩展策略，为读者全面解析从SQL文本到抽象语法树的完整转换过程。

## 2.1 SQL解析器整体架构

### 2.1.1 解析器架构设计

StarRocks的SQL解析器采用了经典的词法分析 → 语法分析 → AST构建的三阶段解析模型：

```mermaid
graph TD
    A[SQL Text] --> B[Lexical Analysis]
    B --> C[Token Stream]
    C --> D[Syntax Analysis]
    D --> E[Parse Tree]
    E --> F[AST Builder]
    F --> G[Abstract Syntax Tree]
    
    subgraph "ANTLR Framework"
        B
        D
    end
    
    subgraph "StarRocks Components"
        F
        G
    end
```

这种设计的优势在于：
- **清晰的职责分离**: 每个阶段专注于特定的处理逻辑
- **良好的扩展性**: 可以独立扩展语法规则和AST节点
- **强大的错误处理**: ANTLR提供了完善的错误恢复机制

### 2.1.2 核心组件类图

```java
// 核心解析器类的关系结构
SqlParser 
    ├── StarRocksLexer (词法分析器)
    ├── StarRocksParser (语法分析器) 
    ├── AstBuilder (AST构建器)
    └── ErrorStrategy (错误处理策略)

AstBuilder
    ├── StatementVisitor (语句访问器)
    ├── ExpressionVisitor (表达式访问器)
    └── RelationVisitor (关系访问器)

ParseTreeVisitor<T>
    ├── AstBuilder
    └── CustomVisitors...
```

## 2.2 SqlParser核心实现分析

### 2.2.1 SqlParser主类设计

让我们深入分析`SqlParser.java`的核心实现：

```java
// fe/fe-core/src/main/java/com/starrocks/sql/parser/SqlParser.java
public class SqlParser {
    // ANTLR解析器缓存，提升性能
    private static final ThreadLocal<StarRocksParser> PARSER_CACHE = 
        ThreadLocal.withInitial(SqlParser::createParser);
    
    // 词法分析器缓存
    private static final ThreadLocal<StarRocksLexer> LEXER_CACHE = 
        ThreadLocal.withInitial(SqlParser::createLexer);
    
    // AST构建器
    private static final AstBuilder AST_BUILDER = new AstBuilder();
    
    // 主要解析方法
    public static StatementBase parse(String sql, SessionVariable sessionVariable) {
        try {
            // 1. 预处理SQL文本
            String normalizedSql = preprocessSql(sql);
            
            // 2. 词法分析
            StarRocksLexer lexer = LEXER_CACHE.get();
            lexer.setInputStream(CharStreams.fromString(normalizedSql));
            
            // 3. 语法分析
            StarRocksParser parser = PARSER_CACHE.get();
            parser.setTokenStream(new CommonTokenStream(lexer));
            
            // 4. 设置错误处理策略
            parser.setErrorHandler(new StarRocksErrorStrategy());
            
            // 5. 解析生成Parse Tree
            ParseTree tree;
            if (sessionVariable.getSqlMode() == SqlModeHelper.MODE_PIPES_AS_CONCAT) {
                tree = parser.singleStatementWithPipes();
            } else {
                tree = parser.singleStatement();
            }
            
            // 6. 构建AST
            return (StatementBase) AST_BUILDER.visit(tree);
            
        } catch (Exception e) {
            throw new ParsingException("Failed to parse SQL: " + sql, e);
        }
    }
    
    // 创建ANTLR解析器
    private static StarRocksParser createParser() {
        StarRocksParser parser = new StarRocksParser(null);
        parser.addParseListener(new PostProcessor());
        parser.removeErrorListeners();
        parser.addErrorListener(ParsingErrorListener.INSTANCE);
        return parser;
    }
    
    // 创建ANTLR词法分析器
    private static StarRocksLexer createLexer() {
        StarRocksLexer lexer = new StarRocksLexer(null);
        lexer.removeErrorListeners();
        lexer.addErrorListener(ParsingErrorListener.INSTANCE);
        return lexer;
    }
}
```

#### 解析器设计的关键特性

1. **线程安全的缓存机制**: 使用ThreadLocal缓存解析器实例，避免频繁创建的性能开销
2. **灵活的SQL模式支持**: 支持不同的SQL方言和兼容模式
3. **完善的错误处理**: 自定义错误策略，提供准确的错误定位信息
4. **预处理优化**: 在解析前对SQL进行规范化处理

### 2.2.2 词法分析器深度解析

StarRocks使用ANTLR4生成的词法分析器来识别SQL中的各种Token：

```antlr
// StarRocksLexer.g4 (部分核心词法规则)
lexer grammar StarRocksLexer;

// 关键字定义
SELECT: 'SELECT';
FROM: 'FROM';
WHERE: 'WHERE';
GROUP: 'GROUP';
BY: 'BY';
ORDER: 'ORDER';
HAVING: 'HAVING';
LIMIT: 'LIMIT';

// StarRocks特有关键字
PROPERTIES: 'PROPERTIES';
DISTRIBUTED: 'DISTRIBUTED';
BUCKETS: 'BUCKETS';
DUPLICATE: 'DUPLICATE';
AGGREGATE: 'AGGREGATE';
UNIQUE: 'UNIQUE';
PRIMARY: 'PRIMARY';

// 标识符和字面量
IDENTIFIER: [a-zA-Z_][a-zA-Z0-9_]*;
INTEGER_VALUE: [0-9]+;
DECIMAL_VALUE: [0-9]+ '.' [0-9]*;
STRING: '\'' (~'\'' | '\'\'')* '\'';

// 操作符
EQ: '=';
NEQ: '!=' | '<>';
LT: '<';
LTE: '<=';
GT: '>';
GTE: '>=';
```

#### 词法分析的高级特性

1. **关键字优先级处理**: 确保关键字能够正确识别，避免与标识符混淆
2. **多种字符串格式支持**: 支持单引号、双引号、反引号等多种字符串格式
3. **数值类型智能识别**: 自动区分整数、小数、科学计数法等数值格式
4. **注释处理**: 支持SQL标准的注释语法（-- 和 /* */）

```java
// 词法分析器的具体实现细节
public class StarRocksLexer extends Lexer {
    
    // Token类型常量
    public static final int SELECT = 1;
    public static final int FROM = 2;
    public static final int WHERE = 3;
    // ... 更多Token类型
    
    // 关键字映射表
    private static final Map<String, Integer> KEYWORDS = new HashMap<>();
    static {
        KEYWORDS.put("SELECT", SELECT);
        KEYWORDS.put("FROM", FROM);
        KEYWORDS.put("WHERE", WHERE);
        // ... 初始化所有关键字
    }
    
    @Override
    public Token nextToken() {
        Token token = super.nextToken();
        
        // 处理标识符和关键字的区分
        if (token.getType() == IDENTIFIER) {
            String text = token.getText().toUpperCase();
            Integer keywordType = KEYWORDS.get(text);
            if (keywordType != null) {
                // 将标识符转换为关键字Token
                return new CommonToken(keywordType, text);
            }
        }
        
        return token;
    }
}
```

### 2.2.3 语法分析器核心机制

StarRocks的语法分析器基于ANTLR4的LL(*)算法，具有强大的错误恢复能力：

```antlr
// StarRocksParser.g4 (部分核心语法规则)
parser grammar StarRocksParser;

// 顶层语句
singleStatement
    : statement EOF
    ;

statement
    : query                                                    #queryStatement
    | USE qualifiedName                                        #useStatement
    | CREATE TABLE qualifiedName '(' columnDefinition (',' columnDefinition)* ')' 
      tableProperties?                                         #createTableStatement
    | INSERT INTO qualifiedName ('(' identifier (',' identifier)* ')')? 
      query                                                    #insertStatement
    | UPDATE qualifiedName SET assignment (',' assignment)* 
      whereClause?                                             #updateStatement
    | DELETE FROM qualifiedName whereClause?                  #deleteStatement
    | SHOW TABLES                                              #showTablesStatement
    // ... 更多语句类型
    ;

// 查询语句
query
    : with? queryNoWith
    ;

queryNoWith
    : queryTerm (ORDER BY sortItem (',' sortItem)*)? 
      (LIMIT limit)?
    ;

queryTerm
    : queryPrimary                                             #queryTermDefault
    | left=queryTerm operator=INTERSECT setQuantifier? 
      right=queryTerm                                          #setOperation
    | left=queryTerm operator=(UNION | EXCEPT) setQuantifier? 
      right=queryTerm                                          #setOperation
    ;

queryPrimary
    : querySpecification                                       #queryPrimaryDefault
    | TABLE qualifiedName                                      #table
    | VALUES expression (',' expression)*                      #inlineTable
    | '(' queryNoWith ')'                                      #subquery
    ;

// 查询规范
querySpecification
    : SELECT setQuantifier? selectItem (',' selectItem)*
      fromClause?
      whereClause?
      groupByClause?
      havingClause?
    ;
```

#### 语法分析的设计特点

1. **递归下降解析**: 支持嵌套查询和复杂表达式
2. **左递归消除**: ANTLR4自动处理左递归，简化语法定义
3. **优先级处理**: 通过语法规则的层次结构自然表达操作符优先级
4. **错误恢复**: 支持语法错误的自动恢复和继续解析

```java
// 语法分析器的错误恢复策略
public class StarRocksErrorStrategy extends DefaultErrorStrategy {
    
    @Override
    public void recover(Parser recognizer, RecognitionException e) {
        // 记录错误位置和上下文
        ParsingErrorListener.reportError(recognizer, e);
        
        // 尝试恢复到已知的同步点
        if (inErrorRecoveryMode(recognizer)) {
            // 如果已经在错误恢复模式，跳过当前token
            recognizer.consume();
            return;
        }
        
        // 进入错误恢复模式
        beginErrorCondition(recognizer);
        
        // 查找合适的恢复点
        IntervalSet followSet = getErrorRecoverySet(recognizer);
        consumeUntil(recognizer, followSet);
    }
    
    @Override
    public Token recoverInline(Parser recognizer) throws RecognitionException {
        // 处理缺失token的情况
        Token matchedSymbol = singleTokenDeletion(recognizer);
        if (matchedSymbol != null) {
            return matchedSymbol;
        }
        
        // 尝试插入预期的token
        return singleTokenInsertion(recognizer);
    }
}
```

## 2.3 AstBuilder深度实现分析

### 2.3.1 AstBuilder核心架构

`AstBuilder`是将ANTLR生成的Parse Tree转换为StarRocks AST的核心组件：

```java
// fe/fe-core/src/main/java/com/starrocks/sql/parser/AstBuilder.java
public class AstBuilder extends StarRocksParserBaseVisitor<ParseNode> {
    
    // 访问者模式的核心实现
    @Override
    public ParseNode visitSingleStatement(StarRocksParser.SingleStatementContext ctx) {
        return visit(ctx.statement());
    }
    
    // 查询语句的构建
    @Override
    public ParseNode visitQueryStatement(StarRocksParser.QueryStatementContext ctx) {
        QueryRelation query = (QueryRelation) visit(ctx.query());
        return new QueryStatement(query);
    }
    
    // SELECT语句的构建
    @Override
    public ParseNode visitQuerySpecification(StarRocksParser.QuerySpecificationContext ctx) {
        // 1. 构建SELECT列表
        List<SelectListItem> selectItems = ctx.selectItem().stream()
            .map(this::visit)
            .map(SelectListItem.class::cast)
            .collect(Collectors.toList());
        
        // 2. 构建FROM子句
        List<TableRelation> fromRelations = new ArrayList<>();
        if (ctx.fromClause() != null) {
            fromRelations = visitFromClause(ctx.fromClause());
        }
        
        // 3. 构建WHERE条件
        Expr whereExpr = null;
        if (ctx.whereClause() != null) {
            whereExpr = (Expr) visit(ctx.whereClause().booleanExpression());
        }
        
        // 4. 构建GROUP BY子句
        GroupByClause groupByClause = null;
        if (ctx.groupByClause() != null) {
            groupByClause = visitGroupByClause(ctx.groupByClause());
        }
        
        // 5. 构建HAVING子句
        Expr havingExpr = null;
        if (ctx.havingClause() != null) {
            havingExpr = (Expr) visit(ctx.havingClause().booleanExpression());
        }
        
        // 6. 构建SELECT关系
        SelectRelation selectRelation = new SelectRelation(
            selectItems,
            fromRelations.isEmpty() ? null : fromRelations.get(0),
            whereExpr,
            groupByClause,
            havingExpr
        );
        
        return selectRelation;
    }
    
    // 表达式构建的核心方法
    @Override
    public ParseNode visitBinaryExpression(StarRocksParser.BinaryExpressionContext ctx) {
        Expr left = (Expr) visit(ctx.left);
        Expr right = (Expr) visit(ctx.right);
        String operator = ctx.operator.getText();
        
        return createBinaryExpression(operator, left, right);
    }
    
    private Expr createBinaryExpression(String operator, Expr left, Expr right) {
        switch (operator.toUpperCase()) {
            case "AND":
                return new CompoundPredicate(CompoundPredicate.Operator.AND, left, right);
            case "OR":
                return new CompoundPredicate(CompoundPredicate.Operator.OR, left, right);
            case "=":
                return new BinaryPredicate(BinaryPredicate.Operator.EQ, left, right);
            case "!=":
            case "<>":
                return new BinaryPredicate(BinaryPredicate.Operator.NE, left, right);
            case "<":
                return new BinaryPredicate(BinaryPredicate.Operator.LT, left, right);
            case "<=":
                return new BinaryPredicate(BinaryPredicate.Operator.LE, left, right);
            case ">":
                return new BinaryPredicate(BinaryPredicate.Operator.GT, left, right);
            case ">=":
                return new BinaryPredicate(BinaryPredicate.Operator.GE, left, right);
            case "+":
                return new ArithmeticExpr(ArithmeticExpr.Operator.ADD, left, right);
            case "-":
                return new ArithmeticExpr(ArithmeticExpr.Operator.SUBTRACT, left, right);
            case "*":
                return new ArithmeticExpr(ArithmeticExpr.Operator.MULTIPLY, left, right);
            case "/":
                return new ArithmeticExpr(ArithmeticExpr.Operator.DIVIDE, left, right);
            default:
                throw new UnsupportedOperationException("Unsupported operator: " + operator);
        }
    }
}
```

### 2.3.2 AST节点层次结构

StarRocks的AST采用了完整的面向对象层次结构：

```java
// AST节点的基类定义
public abstract class ParseNode {
    protected NodePosition pos;
    
    // 访问者模式支持
    public abstract <R, C> R accept(AstVisitor<R, C> visitor, C context);
    
    // 源码位置信息
    public NodePosition getPos() { return pos; }
    public void setPos(NodePosition pos) { this.pos = pos; }
}

// 语句基类
public abstract class StatementBase extends ParseNode {
    // 重定向表信息（用于物化视图等场景）
    protected RedirectStatus redirectStatus = RedirectStatus.NO_FORWARD;
    
    public RedirectStatus getRedirectStatus() { return redirectStatus; }
    public void setRedirectStatus(RedirectStatus redirectStatus) { 
        this.redirectStatus = redirectStatus; 
    }
}

// 查询语句
public class QueryStatement extends StatementBase {
    private final QueryRelation queryRelation;
    private List<String> colLabels;
    private OutFileClause outFileClause;
    
    public QueryStatement(QueryRelation queryRelation) {
        this.queryRelation = queryRelation;
    }
    
    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitQueryStatement(this, context);
    }
}

// 关系表达式基类
public abstract class Relation extends ParseNode {
    protected List<String> columnOutputNames;
    protected Scope scope;
    
    // 获取输出列名
    public List<String> getColumnOutputNames() { return columnOutputNames; }
    
    // 设置作用域
    public void setScope(Scope scope) { this.scope = scope; }
    public Scope getScope() { return scope; }
}

// SELECT关系
public class SelectRelation extends QueryRelation {
    private final SelectList selectList;
    private final FromRelation fromRelation;
    private final Expr whereClause;
    private final GroupByClause groupByClause;
    private final Expr havingClause;
    
    public SelectRelation(SelectList selectList, 
                         FromRelation fromRelation,
                         Expr whereClause,
                         GroupByClause groupByClause,
                         Expr havingClause) {
        this.selectList = selectList;
        this.fromRelation = fromRelation;
        this.whereClause = whereClause;
        this.groupByClause = groupByClause;
        this.havingClause = havingClause;
    }
    
    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitSelect(this, context);
    }
}
```

#### AST节点设计的关键特性

1. **类型安全**: 每种语法结构都有对应的强类型AST节点
2. **位置信息**: 保留源码位置，便于错误报告和调试
3. **访问者模式**: 支持多种遍历和变换操作
4. **不可变性**: AST节点创建后不可修改，保证线程安全

### 2.3.3 表达式AST的构建

表达式是SQL中最复杂的部分，StarRocks为各种表达式类型设计了专门的AST节点：

```java
// 表达式基类
public abstract class Expr extends ParseNode {
    protected Type type;
    protected boolean isAnalyzed = false;
    
    // 表达式求值（编译时常量折叠）
    public abstract Expr clone();
    
    // 类型推导
    public Type getType() { return type; }
    public void setType(Type type) { this.type = type; }
    
    // 是否为常量表达式
    public boolean isConstant() { return false; }
    
    // 获取表达式中引用的列
    public void getColumnRefs(List<SlotRef> refs) {}
}

// 二元表达式
public class BinaryPredicate extends Predicate {
    public enum Operator {
        EQ("="), NE("!="), LE("<="), GE(">="), LT("<"), GT(">"),
        EQ_FOR_NULL("<=>");
        
        private final String description;
        
        Operator(String description) {
            this.description = description;
        }
        
        @Override
        public String toString() { return description; }
    }
    
    private final Operator op;
    private final Expr left;
    private final Expr right;
    
    public BinaryPredicate(Operator op, Expr left, Expr right) {
        this.op = op;
        this.left = left;
        this.right = right;
    }
    
    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitBinaryPredicate(this, context);
    }
    
    @Override
    public boolean isConstant() {
        return left.isConstant() && right.isConstant();
    }
}

// 函数调用表达式
public class FunctionCallExpr extends Expr {
    private final FunctionName fnName;
    private final FunctionParams fnParams;
    private Function fn;
    
    public FunctionCallExpr(FunctionName fnName, FunctionParams fnParams) {
        this.fnName = fnName;
        this.fnParams = fnParams;
    }
    
    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitFunctionCall(this, context);
    }
    
    // 函数解析（在语义分析阶段完成）
    public void setFn(Function fn) {
        this.fn = fn;
    }
    
    public Function getFn() { return fn; }
}

// 列引用表达式
public class SlotRef extends Expr {
    private final TableName tblName;
    private final String col;
    private SlotDescriptor desc;
    
    public SlotRef(TableName tblName, String col) {
        this.tblName = tblName;
        this.col = col;
    }
    
    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitSlotRef(this, context);
    }
    
    // 绑定到具体的列描述符（在语义分析阶段完成）
    public void setDesc(SlotDescriptor desc) {
        this.desc = desc;
        this.type = desc.getType();
    }
}
```

## 2.4 SQL方言与扩展语法支持

### 2.4.1 多SQL方言兼容性

StarRocks支持多种SQL方言，以适应不同的迁移场景：

```java
// SQL方言枚举
public enum SqlDialect {
    STARROCKS,    // StarRocks原生方言
    MYSQL,        // MySQL兼容模式
    POSTGRESQL,   // PostgreSQL兼容模式
    HIVE,         // Hive兼容模式
    SPARK_SQL     // Spark SQL兼容模式
}

// 方言相关的解析配置
public class SqlParserConfig {
    private SqlDialect dialect = SqlDialect.STARROCKS;
    private boolean caseSensitive = false;
    private boolean allowReservedKeywords = false;
    private Set<String> enabledFeatures = new HashSet<>();
    
    // 方言特定的关键字处理
    public boolean isKeyword(String word) {
        switch (dialect) {
            case MYSQL:
                return MYSQL_KEYWORDS.contains(word.toUpperCase());
            case POSTGRESQL:
                return POSTGRESQL_KEYWORDS.contains(word.toUpperCase());
            default:
                return STARROCKS_KEYWORDS.contains(word.toUpperCase());
        }
    }
    
    // 方言特定的函数名解析
    public String resolveFunctionName(String name) {
        switch (dialect) {
            case MYSQL:
                return MYSQL_FUNCTION_MAPPING.getOrDefault(name, name);
            case POSTGRESQL:
                return POSTGRESQL_FUNCTION_MAPPING.getOrDefault(name, name);
            default:
                return name;
        }
    }
}
```

#### MySQL兼容模式的特殊处理

```java
// MySQL方言的特殊语法处理
public class MySQLDialectHandler {
    
    // MySQL风格的字符串转义
    public String unescapeString(String mysqlString) {
        return mysqlString
            .replace("\\'", "'")
            .replace("\\\"", "\"")
            .replace("\\n", "\n")
            .replace("\\r", "\r")
            .replace("\\t", "\t")
            .replace("\\\\", "\\");
    }
    
    // MySQL风格的标识符引用
    public String unquoteIdentifier(String identifier) {
        if (identifier.startsWith("`") && identifier.endsWith("`")) {
            return identifier.substring(1, identifier.length() - 1)
                .replace("``", "`");
        }
        return identifier;
    }
    
    // MySQL特有的数据类型映射
    public Type mapMySQLType(String mysqlType) {
        switch (mysqlType.toUpperCase()) {
            case "TINYINT":
                return Type.TINYINT;
            case "SMALLINT":
                return Type.SMALLINT;
            case "MEDIUMINT":
            case "INT":
            case "INTEGER":
                return Type.INT;
            case "BIGINT":
                return Type.BIGINT;
            case "FLOAT":
                return Type.FLOAT;
            case "DOUBLE":
                return Type.DOUBLE;
            case "VARCHAR":
                return Type.VARCHAR;
            case "TEXT":
                return Type.STRING;
            default:
                throw new AnalysisException("Unsupported MySQL type: " + mysqlType);
        }
    }
}
```

### 2.4.2 StarRocks特有语法扩展

StarRocks为OLAP场景设计了许多特有的语法扩展：

#### 表创建语法扩展

```antlr
// 创建表的扩展语法
createTableStatement
    : CREATE TABLE ifNotExists? qualifiedName 
      '(' columnDefinition (',' columnDefinition)* ')'
      ENGINE '=' identifier
      keyDesc?
      partitionDesc?
      distributionDesc?
      orderByDesc?
      properties?
    ;

// 键类型定义（OLAP表特有）
keyDesc
    : DUPLICATE KEY '(' identifierList ')'     #duplicateKeyDesc
    | AGGREGATE KEY '(' identifierList ')'     #aggregateKeyDesc
    | UNIQUE KEY '(' identifierList ')'        #uniqueKeyDesc
    | PRIMARY KEY '(' identifierList ')'       #primaryKeyDesc
    ;

// 分区定义（支持动态分区）
partitionDesc
    : PARTITION BY RANGE '(' identifier ')' 
      '(' partitionRangeDesc (',' partitionRangeDesc)* ')'    #rangePartition
    | PARTITION BY LIST '(' identifier ')' 
      '(' partitionListDesc (',' partitionListDesc)* ')'     #listPartition
    ;

// 分桶定义
distributionDesc
    : DISTRIBUTED BY HASH '(' identifierList ')' BUCKETS INTEGER_VALUE
    ;
```

#### 物化视图语法扩展

```antlr
// 物化视图创建语法
createMaterializedViewStatement
    : CREATE MATERIALIZED VIEW ifNotExists? qualifiedName
      buildMode?
      refreshScheme?
      AS query
    ;

buildMode
    : BUILD IMMEDIATE     #immediateBuild
    | BUILD DEFERRED      #deferredBuild
    ;

refreshScheme
    : REFRESH MANUAL                           #manualRefresh
    | REFRESH ASYNC EVERY interval             #asyncRefresh
    | REFRESH ASYNC START '(' expression ')' EVERY interval    #scheduledRefresh
    ;
```

#### 数据导入语法扩展

```antlr
// STREAM LOAD语法
streamLoadStatement
    : LOAD LABEL qualifiedName
      '(' DATA INFILE '(' string (',' string)* ')' 
          INTO TABLE identifier
          columns?
          columnSeparator?
          rowDelimiter?
      ')'
      properties?
    ;

// BROKER LOAD语法  
brokerLoadStatement
    : LOAD LABEL qualifiedName
      '(' DATA INFILE '(' string (',' string)* ')'
          INTO TABLE identifier
          columns?
          BROKER string properties?
      ')'
      properties?
    ;
```

### 2.4.3 语法扩展的实现机制

StarRocks通过以下机制实现语法扩展：

#### 可插拔的语法规则

```java
// 语法规则注册器
public class SyntaxRuleRegistry {
    private final Map<String, SyntaxRule> rules = new HashMap<>();
    
    // 注册自定义语法规则
    public void registerRule(String name, SyntaxRule rule) {
        rules.put(name, rule);
    }
    
    // 查找匹配的语法规则
    public SyntaxRule findRule(ParserRuleContext context) {
        for (SyntaxRule rule : rules.values()) {
            if (rule.matches(context)) {
                return rule;
            }
        }
        return null;
    }
}

// 语法规则接口
public interface SyntaxRule {
    boolean matches(ParserRuleContext context);
    ParseNode build(ParserRuleContext context, AstBuilder builder);
}

// 具体的扩展语法实现
public class MaterializedViewSyntaxRule implements SyntaxRule {
    @Override
    public boolean matches(ParserRuleContext context) {
        return context instanceof CreateMaterializedViewStatementContext;
    }
    
    @Override
    public ParseNode build(ParserRuleContext context, AstBuilder builder) {
        CreateMaterializedViewStatementContext ctx = 
            (CreateMaterializedViewStatementContext) context;
        
        // 构建物化视图AST节点
        String mvName = ctx.qualifiedName().getText();
        QueryStatement query = (QueryStatement) builder.visit(ctx.query());
        
        CreateMaterializedViewStmt stmt = new CreateMaterializedViewStmt(mvName, query);
        
        // 处理构建模式
        if (ctx.buildMode() != null) {
            BuildMode mode = parseBuildMode(ctx.buildMode());
            stmt.setBuildMode(mode);
        }
        
        // 处理刷新策略
        if (ctx.refreshScheme() != null) {
            RefreshScheme scheme = parseRefreshScheme(ctx.refreshScheme());
            stmt.setRefreshScheme(scheme);
        }
        
        return stmt;
    }
}
```

## 2.5 错误处理与诊断机制

### 2.5.1 多层错误处理架构

StarRocks的错误处理系统采用了分层的设计：

```java
// 解析错误监听器
public class ParsingErrorListener extends BaseErrorListener {
    public static final ParsingErrorListener INSTANCE = new ParsingErrorListener();
    
    @Override
    public void syntaxError(Recognizer<?, ?> recognizer,
                           Object offendingSymbol,
                           int line,
                           int charPositionInLine,
                           String msg,
                           RecognitionException e) {
        
        // 构造详细的错误信息
        String errorMsg = buildErrorMessage(recognizer, offendingSymbol, 
                                           line, charPositionInLine, msg, e);
        
        // 尝试提供修复建议
        List<String> suggestions = generateSuggestions(recognizer, e);
        
        throw new ParsingException(errorMsg, suggestions, line, charPositionInLine);
    }
    
    private String buildErrorMessage(Recognizer<?, ?> recognizer,
                                    Object offendingSymbol,
                                    int line, int charPositionInLine,
                                    String msg, RecognitionException e) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("Syntax error at line ").append(line)
          .append(", column ").append(charPositionInLine).append(": ");
        
        if (e instanceof NoViableAltException) {
            sb.append("Unexpected token '").append(getTokenText(offendingSymbol))
              .append("'. Expected one of: ")
              .append(getExpectedTokens(recognizer, e));
        } else if (e instanceof InputMismatchException) {
            sb.append("Expected '").append(getExpectedTokenText(e))
              .append("' but found '").append(getTokenText(offendingSymbol))
              .append("'");
        } else {
            sb.append(msg);
        }
        
        return sb.toString();
    }
    
    private List<String> generateSuggestions(Recognizer<?, ?> recognizer, 
                                           RecognitionException e) {
        List<String> suggestions = new ArrayList<>();
        
        if (e instanceof NoViableAltException) {
            // 基于预期token生成建议
            IntervalSet expectedTokens = e.getExpectedTokens();
            for (int tokenType : expectedTokens.toList()) {
                String tokenName = recognizer.getVocabulary().getDisplayName(tokenType);
                suggestions.add("Did you mean '" + tokenName + "'?");
            }
        }
        
        // 基于编辑距离的拼写纠错
        if (e.getOffendingToken() != null) {
            String tokenText = e.getOffendingToken().getText();
            suggestions.addAll(getSpellingSuggestions(tokenText));
        }
        
        return suggestions;
    }
}
```

### 2.5.2 智能错误恢复策略

```java
// 自定义错误恢复策略
public class StarRocksErrorStrategy extends DefaultErrorStrategy {
    
    // 错误恢复的同步点
    private static final Set<Integer> SYNC_POINTS = Set.of(
        StarRocksParser.SELECT,
        StarRocksParser.FROM,
        StarRocksParser.WHERE,
        StarRocksParser.GROUP,
        StarRocksParser.ORDER,
        StarRocksParser.LIMIT
    );
    
    @Override
    public void recover(Parser recognizer, RecognitionException e) {
        // 如果已经在错误恢复模式，继续消费token
        if (inErrorRecoveryMode(recognizer)) {
            consumeUntilSyncPoint(recognizer);
            return;
        }
        
        // 尝试单个token的删除恢复
        if (singleTokenDeletion(recognizer) != null) {
            return;
        }
        
        // 尝试单个token的插入恢复
        if (singleTokenInsertion(recognizer) != null) {
            return;
        }
        
        // 进入错误恢复模式
        beginErrorCondition(recognizer);
        consumeUntilSyncPoint(recognizer);
    }
    
    private void consumeUntilSyncPoint(Parser recognizer) {
        while (recognizer.getCurrentToken().getType() != Token.EOF) {
            int tokenType = recognizer.getCurrentToken().getType();
            if (SYNC_POINTS.contains(tokenType)) {
                break;
            }
            recognizer.consume();
        }
        endErrorCondition(recognizer);
    }
    
    @Override
    protected void reportUnwantedToken(Parser recognizer) {
        if (inErrorRecoveryMode(recognizer)) {
            return;
        }
        
        beginErrorCondition(recognizer);
        
        Token currentToken = recognizer.getCurrentToken();
        String tokenName = getTokenErrorDisplay(currentToken);
        IntervalSet expecting = getExpectedTokens(recognizer);
        
        String msg = "Unexpected token " + tokenName + 
                    ", expecting " + expecting.toString(recognizer.getVocabulary());
        
        recognizer.notifyErrorListeners(currentToken, msg, null);
    }
}
```

### 2.5.3 语法错误的智能提示

```java
// 智能提示生成器
public class SyntaxSuggestionGenerator {
    
    // 常见错误模式和修复建议
    private static final Map<Pattern, String> ERROR_PATTERNS = Map.of(
        Pattern.compile("Expected.*but found.*"), 
        "Check for missing commas, parentheses, or keywords",
        
        Pattern.compile("Unexpected token.*"),
        "Verify the syntax of the current statement",
        
        Pattern.compile(".*table.*not found.*"),
        "Check table name spelling and database context"
    );
    
    public List<String> generateSuggestions(String sql, ParsingException error) {
        List<String> suggestions = new ArrayList<>();
        
        // 1. 基于错误模式的建议
        for (Map.Entry<Pattern, String> entry : ERROR_PATTERNS.entrySet()) {
            if (entry.getKey().matcher(error.getMessage()).find()) {
                suggestions.add(entry.getValue());
            }
        }
        
        // 2. 基于上下文的建议
        suggestions.addAll(generateContextualSuggestions(sql, error));
        
        // 3. 基于相似SQL的建议
        suggestions.addAll(generateSimilarQuerySuggestions(sql));
        
        return suggestions;
    }
    
    private List<String> generateContextualSuggestions(String sql, ParsingException error) {
        List<String> suggestions = new ArrayList<>();
        
        // 分析SQL结构
        SqlStructureAnalyzer analyzer = new SqlStructureAnalyzer(sql);
        
        // 检查常见的语法问题
        if (analyzer.hasMismatchedParentheses()) {
            suggestions.add("Check for mismatched parentheses");
        }
        
        if (analyzer.hasMismatchedQuotes()) {
            suggestions.add("Check for mismatched string quotes");
        }
        
        if (analyzer.hasIncompleteStatement()) {
            suggestions.add("Statement appears to be incomplete");
        }
        
        return suggestions;
    }
}
```

## 2.6 解析性能优化策略

### 2.6.1 解析器缓存机制

```java
// 解析器实例池
public class ParserPool {
    private final BlockingQueue<StarRocksParser> parsers;
    private final BlockingQueue<StarRocksLexer> lexers;
    private final int maxPoolSize;
    
    public ParserPool(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
        this.parsers = new ArrayBlockingQueue<>(maxPoolSize);
        this.lexers = new ArrayBlockingQueue<>(maxPoolSize);
        
        // 预热解析器实例
        for (int i = 0; i < maxPoolSize; i++) {
            parsers.offer(createParser());
            lexers.offer(createLexer());
        }
    }
    
    public StarRocksParser borrowParser() {
        StarRocksParser parser = parsers.poll();
        if (parser == null) {
            // 池中无可用实例，创建新的
            parser = createParser();
        }
        return parser;
    }
    
    public void returnParser(StarRocksParser parser) {
        // 重置解析器状态
        resetParser(parser);
        
        // 归还到池中
        if (!parsers.offer(parser)) {
            // 池已满，丢弃实例
        }
    }
    
    private void resetParser(StarRocksParser parser) {
        parser.reset();
        parser.removeErrorListeners();
        parser.addErrorListener(ParsingErrorListener.INSTANCE);
    }
}
```

### 2.6.2 AST缓存策略

```java
// AST缓存管理器
public class AstCacheManager {
    private final ConcurrentHashMap<String, CachedAst> cache = new ConcurrentHashMap<>();
    private final int maxCacheSize = 10000;
    private final long ttlMillis = 300000; // 5分钟TTL
    
    public StatementBase getCachedAst(String sql) {
        CachedAst cached = cache.get(normalizeSql(sql));
        if (cached != null && !cached.isExpired()) {
            return cached.ast.clone();
        }
        return null;
    }
    
    public void putAst(String sql, StatementBase ast) {
        if (cache.size() >= maxCacheSize) {
            evictExpired();
        }
        
        String normalizedSql = normalizeSql(sql);
        cache.put(normalizedSql, new CachedAst(ast, System.currentTimeMillis()));
    }
    
    private String normalizeSql(String sql) {
        // SQL标准化：移除注释、多余空格、统一大小写等
        return sql.replaceAll("/\\*.*?\\*/", "")
                 .replaceAll("--.*?\n", "")
                 .replaceAll("\\s+", " ")
                 .trim()
                 .toUpperCase();
    }
    
    private void evictExpired() {
        long now = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> 
            now - entry.getValue().timestamp > ttlMillis);
    }
    
    private static class CachedAst {
        final StatementBase ast;
        final long timestamp;
        
        CachedAst(StatementBase ast, long timestamp) {
            this.ast = ast;
            this.timestamp = timestamp;
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > ttlMillis;
        }
    }
}
```

### 2.6.3 增量解析优化

```java
// 增量解析器
public class IncrementalParser {
    
    // 解析上下文缓存
    private final Map<String, ParseContext> contextCache = new HashMap<>();
    
    public StatementBase parseIncremental(String sql, String lastSql) {
        // 计算SQL差异
        List<SqlDiff> diffs = computeSqlDiff(lastSql, sql);
        
        // 如果差异很小，尝试增量解析
        if (diffs.size() <= 3 && canIncrementalParse(diffs)) {
            return performIncrementalParse(sql, lastSql, diffs);
        }
        
        // 否则进行完整解析
        return SqlParser.parse(sql, SessionVariable.getDefault());
    }
    
    private boolean canIncrementalParse(List<SqlDiff> diffs) {
        // 判断是否适合增量解析
        for (SqlDiff diff : diffs) {
            if (diff.getType() == SqlDiff.Type.STRUCTURE_CHANGE) {
                return false; // 结构性变化不支持增量解析
            }
        }
        return true;
    }
    
    private StatementBase performIncrementalParse(String sql, String lastSql, 
                                                 List<SqlDiff> diffs) {
        // 获取上次的解析上下文
        ParseContext lastContext = contextCache.get(lastSql);
        if (lastContext == null) {
            return SqlParser.parse(sql, SessionVariable.getDefault());
        }
        
        // 基于差异进行增量更新
        ParseContext newContext = lastContext.clone();
        for (SqlDiff diff : diffs) {
            applyDiff(newContext, diff);
        }
        
        // 生成新的AST
        StatementBase ast = newContext.buildAst();
        
        // 缓存新的解析上下文
        contextCache.put(sql, newContext);
        
        return ast;
    }
}
```

## 2.7 SQL解析的扩展点设计

### 2.7.1 自定义函数语法支持

```java
// 自定义函数解析器
public class CustomFunctionParser {
    private final Map<String, FunctionSyntaxRule> customFunctions = new HashMap<>();
    
    // 注册自定义函数语法
    public void registerFunction(String name, FunctionSyntaxRule rule) {
        customFunctions.put(name.toUpperCase(), rule);
    }
    
    // 解析函数调用
    public Expr parseFunction(String functionName, List<Expr> args, 
                             ParserRuleContext context) {
        FunctionSyntaxRule rule = customFunctions.get(functionName.toUpperCase());
        if (rule != null) {
            return rule.parseFunction(functionName, args, context);
        }
        
        // 使用默认的函数解析逻辑
        return new FunctionCallExpr(new FunctionName(functionName), 
                                   new FunctionParams(false, args));
    }
}

// 函数语法规则接口
public interface FunctionSyntaxRule {
    Expr parseFunction(String name, List<Expr> args, ParserRuleContext context);
    boolean validateArguments(List<Expr> args);
}

// 聚合函数语法规则示例
public class AggregateFunctionRule implements FunctionSyntaxRule {
    @Override
    public Expr parseFunction(String name, List<Expr> args, ParserRuleContext context) {
        // 解析聚合函数特有的语法，如DISTINCT、ORDER BY等
        boolean isDistinct = hasDistinctModifier(context);
        List<OrderByElement> orderBy = parseOrderByClause(context);
        
        return new FunctionCallExpr(new FunctionName(name), 
                                   new FunctionParams(isDistinct, args), orderBy);
    }
    
    @Override
    public boolean validateArguments(List<Expr> args) {
        // 验证聚合函数参数
        return !args.isEmpty();
    }
}
```

### 2.7.2 SQL宏系统

```java
// SQL宏定义
public class SqlMacro {
    private final String name;
    private final List<String> parameters;
    private final String template;
    private final MacroExpander expander;
    
    public SqlMacro(String name, List<String> parameters, String template) {
        this.name = name;
        this.parameters = parameters;
        this.template = template;
        this.expander = new MacroExpander(template, parameters);
    }
    
    // 展开宏
    public String expand(List<String> arguments) {
        if (arguments.size() != parameters.size()) {
            throw new IllegalArgumentException("Macro argument count mismatch");
        }
        
        return expander.expand(arguments);
    }
}

// 宏展开器
public class MacroExpander {
    private final String template;
    private final List<String> parameters;
    private final Pattern placeholderPattern;
    
    public MacroExpander(String template, List<String> parameters) {
        this.template = template;
        this.parameters = parameters;
        this.placeholderPattern = Pattern.compile("\\$\\{(\\w+)\\}");
    }
    
    public String expand(List<String> arguments) {
        Map<String, String> substitutions = new HashMap<>();
        for (int i = 0; i < parameters.size(); i++) {
            substitutions.put(parameters.get(i), arguments.get(i));
        }
        
        return placeholderPattern.matcher(template).replaceAll(match -> {
            String paramName = match.group(1);
            String substitution = substitutions.get(paramName);
            if (substitution == null) {
                throw new IllegalArgumentException("Unknown parameter: " + paramName);
            }
            return Matcher.quoteReplacement(substitution);
        });
    }
}

// 宏预处理器
public class SqlMacroPreprocessor {
    private final Map<String, SqlMacro> macros = new HashMap<>();
    
    public void defineMacro(String name, List<String> parameters, String template) {
        macros.put(name, new SqlMacro(name, parameters, template));
    }
    
    public String preprocess(String sql) {
        // 查找宏调用
        Pattern macroCallPattern = Pattern.compile("(\\w+)\\s*\\(([^)]*)\\)");
        Matcher matcher = macroCallPattern.matcher(sql);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String macroName = matcher.group(1);
            String argsStr = matcher.group(2);
            
            SqlMacro macro = macros.get(macroName);
            if (macro != null) {
                List<String> args = parseArguments(argsStr);
                String expanded = macro.expand(args);
                matcher.appendReplacement(result, Matcher.quoteReplacement(expanded));
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
}
```

## 2.8 解析器的调试与监控

### 2.8.1 解析过程可视化

```java
// 解析树可视化器
public class ParseTreeVisualizer {
    
    public String generateDotGraph(ParseTree tree, Parser parser) {
        StringBuilder dot = new StringBuilder();
        dot.append("digraph ParseTree {\n");
        dot.append("  rankdir=TB;\n");
        dot.append("  node [shape=box];\n");
        
        Map<ParseTree, Integer> nodeIds = new HashMap<>();
        int nodeCounter = 0;
        
        // 递归遍历解析树
        nodeCounter = assignNodeIds(tree, nodeIds, nodeCounter);
        generateDotNodes(tree, parser, nodeIds, dot);
        generateDotEdges(tree, nodeIds, dot);
        
        dot.append("}\n");
        return dot.toString();
    }
    
    private void generateDotNodes(ParseTree tree, Parser parser, 
                                 Map<ParseTree, Integer> nodeIds, StringBuilder dot) {
        int nodeId = nodeIds.get(tree);
        String label = getNodeLabel(tree, parser);
        
        dot.append("  ").append(nodeId)
           .append(" [label=\"").append(escape(label)).append("\"];\n");
        
        // 递归处理子节点
        for (int i = 0; i < tree.getChildCount(); i++) {
            generateDotNodes(tree.getChild(i), parser, nodeIds, dot);
        }
    }
    
    private String getNodeLabel(ParseTree tree, Parser parser) {
        if (tree instanceof RuleNode) {
            RuleNode ruleNode = (RuleNode) tree;
            int ruleIndex = ruleNode.getRuleContext().getRuleIndex();
            return parser.getRuleNames()[ruleIndex];
        } else if (tree instanceof TerminalNode) {
            return tree.getText();
        }
        return tree.getClass().getSimpleName();
    }
}
```

### 2.8.2 解析性能监控

```java
// 解析性能监控器
public class ParsePerformanceMonitor {
    private final MetricRegistry metrics = new MetricRegistry();
    private final Timer parseTimer = metrics.timer("sql.parse.time");
    private final Counter parseCount = metrics.counter("sql.parse.count");
    private final Counter parseErrors = metrics.counter("sql.parse.errors");
    private final Histogram sqlLength = metrics.histogram("sql.length");
    
    public StatementBase monitoredParse(String sql, SessionVariable sessionVariable) {
        Timer.Context timerContext = parseTimer.time();
        parseCount.inc();
        sqlLength.update(sql.length());
        
        try {
            StatementBase result = SqlParser.parse(sql, sessionVariable);
            recordParseSuccess(sql, result);
            return result;
        } catch (Exception e) {
            parseErrors.inc();
            recordParseError(sql, e);
            throw e;
        } finally {
            timerContext.stop();
        }
    }
    
    private void recordParseSuccess(String sql, StatementBase ast) {
        // 记录成功解析的统计信息
        String statementType = ast.getClass().getSimpleName();
        metrics.counter("sql.parse.success." + statementType).inc();
        
        // 记录AST复杂度指标
        AstComplexityAnalyzer analyzer = new AstComplexityAnalyzer();
        int complexity = analyzer.calculateComplexity(ast);
        metrics.histogram("sql.ast.complexity").update(complexity);
    }
    
    private void recordParseError(String sql, Exception error) {
        // 记录错误类型统计
        String errorType = error.getClass().getSimpleName();
        metrics.counter("sql.parse.error." + errorType).inc();
        
        // 记录错误详情（用于调试）
        Logger.error("SQL parse failed", 
                    Map.of("sql", sql, "error", error.getMessage()));
    }
}
```

## 2.9 未来发展方向

### 2.9.1 基于机器学习的智能解析

```java
// ML辅助的语法错误修复
public class MLSyntaxFixer {
    private final NeuralNetworkModel model;
    
    public String fixSyntaxError(String brokenSql, ParsingException error) {
        // 提取特征向量
        FeatureVector features = extractFeatures(brokenSql, error);
        
        // 使用模型预测修复建议
        FixSuggestion suggestion = model.predict(features);
        
        // 应用修复建议
        return applySuggestion(brokenSql, suggestion);
    }
    
    private FeatureVector extractFeatures(String sql, ParsingException error) {
        return FeatureVector.builder()
            .addTextFeature("sql", sql)
            .addNumericFeature("error_position", error.getCharPositionInLine())
            .addCategoricalFeature("error_type", error.getClass().getSimpleName())
            .addTextFeature("error_message", error.getMessage())
            .build();
    }
}
```

### 2.9.2 增量式解析优化

```java
// 基于AST差分的增量解析
public class AstDifferentialParser {
    
    public StatementBase parseWithDiff(String newSql, String oldSql, 
                                      StatementBase oldAst) {
        // 计算SQL文本差异
        List<TextDiff> textDiffs = DiffUtils.computeTextDiff(oldSql, newSql);
        
        // 将文本差异映射到AST差异
        List<AstDiff> astDiffs = mapToAstDiffs(textDiffs, oldAst);
        
        // 增量更新AST
        return applyAstDiffs(oldAst.clone(), astDiffs);
    }
    
    private List<AstDiff> mapToAstDiffs(List<TextDiff> textDiffs, StatementBase ast) {
        List<AstDiff> astDiffs = new ArrayList<>();
        
        for (TextDiff textDiff : textDiffs) {
            // 找到文本变化对应的AST节点
            ParseNode affectedNode = findAffectedNode(ast, textDiff.getPosition());
            
            if (affectedNode != null) {
                AstDiff astDiff = new AstDiff(affectedNode, textDiff);
                astDiffs.add(astDiff);
            }
        }
        
        return astDiffs;
    }
}
```

## 总结

StarRocks的SQL解析器是一个精心设计的复杂系统，它不仅支持标准SQL语法，还为OLAP场景提供了丰富的语法扩展。通过基于ANTLR4的现代化解析架构、完善的错误处理机制、智能的性能优化策略以及灵活的扩展点设计，StarRocks的解析器能够高效、准确地处理各种复杂的SQL查询。

解析器的设计充分体现了软件工程的最佳实践：
- **模块化设计**: 词法、语法、AST构建各司其职
- **可扩展性**: 支持自定义语法和函数扩展
- **性能优化**: 多层缓存和增量解析机制
- **错误友好**: 智能错误恢复和详细诊断信息

在下一章中，我们将深入分析语义分析模块，看看StarRocks是如何基于AST进行语义检查、类型推导和元数据绑定的。