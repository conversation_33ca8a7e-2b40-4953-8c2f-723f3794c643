# 第四章：查询优化器核心原理

## 引言

查询优化器是现代数据库系统的核心组件，它决定了SQL查询的执行效率。StarRocks采用了先进的基于代价的优化器（Cost-Based Optimizer, CBO），结合规则化优化（Rule-Based Optimization, RBO），能够为复杂的OLAP查询生成高效的执行计划。本章将深入分析StarRocks查询优化器的设计原理、核心算法和实现细节。

## 4.1 查询优化器整体架构

### 4.1.1 优化器架构设计

StarRocks的查询优化器采用了现代化的两阶段优化架构：

```mermaid
graph TD
    A[Logical Plan] --> B[Rule-Based Optimization]
    B --> C[Memo Structure]
    C --> D[Cost-Based Optimization]
    D --> E[Physical Plan]
    
    subgraph "RBO Phase"
        B
        F[Predicate Pushdown]
        G[Projection Pruning]
        H[Constant Folding]
        I[Expression Rewrite]
    end
    
    subgraph "CBO Phase"  
        D
        J[Join Enumeration]
        K[Join Order Selection]
        L[Operator Selection]
        M[Cost Estimation]
    end
    
    subgraph "Support Components"
        N[Statistics Manager]
        O[Cost Model]
        P[Property Enforcer]
        Q[Rule Engine]
    end
    
    B --> F
    B --> G
    B --> H
    B --> I
    
    D --> J
    D --> K
    D --> L
    D --> M
    
    M -.-> N
    M -.-> O
    D -.-> P
    B -.-> Q
```

### 4.1.2 核心优化器类结构

```java
// 优化器核心类的层次结构
Optimizer
    ├── RuleBasedOptimizer (规则化优化器)
    ├── CostBasedOptimizer (代价优化器)
    └── OptimizerConfig (优化器配置)

Memo
    ├── Group (等价类组)
    ├── GroupExpression (组表达式)
    └── OperatorProperty (算子属性)

CostModel
    ├── StatisticsCalculator (统计信息计算器)
    ├── CostEstimator (代价估算器)
    └── CardinalityEstimator (基数估算器)

RuleEngine
    ├── TransformationRule (变换规则)
    ├── ImplementationRule (实现规则)
    └── RuleSet (规则集合)
```

## 4.2 Optimizer核心实现

### 4.2.1 Optimizer主类分析

让我们深入分析`Optimizer.java`的核心实现：

```java
// fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Optimizer.java
public class Optimizer {
    private final OptimizerConfig config;
    private final RuleEngine ruleEngine;
    private final CostModel costModel;
    private final StatisticsManager statisticsManager;
    
    public Optimizer(OptimizerConfig config) {
        this.config = config;
        this.ruleEngine = new RuleEngine();
        this.costModel = new CostModel();
        this.statisticsManager = new StatisticsManager();
        
        // 初始化规则集
        initializeRules();
    }
    
    // 主优化入口
    public OptExpression optimize(OptExpression logicalPlan, 
                                 ConnectContext connectContext,
                                 ColumnRefFactory columnRefFactory) {
        
        OptimizerContext context = new OptimizerContext(
            connectContext, columnRefFactory, config);
        
        // 第一阶段：规则化优化
        OptExpression rewrittenPlan = ruleBasedOptimize(logicalPlan, context);
        
        // 第二阶段：代价化优化
        OptExpression physicalPlan = costBasedOptimize(rewrittenPlan, context);
        
        return physicalPlan;
    }
    
    // 规则化优化阶段
    private OptExpression ruleBasedOptimize(OptExpression plan, OptimizerContext context) {
        // 1. 逻辑重写规则
        plan = applyLogicalRewriteRules(plan, context);
        
        // 2. 表达式重写规则
        plan = applyExpressionRewriteRules(plan, context);
        
        // 3. 谓词下推规则
        plan = applyPredicatePushdownRules(plan, context);
        
        // 4. 投影剪枝规则
        plan = applyProjectionPruningRules(plan, context);
        
        // 5. 列剪枝规则
        plan = applyColumnPruningRules(plan, context);
        
        return plan;
    }
    
    // 代价化优化阶段
    private OptExpression costBasedOptimize(OptExpression plan, OptimizerContext context) {
        // 1. 构建Memo结构
        Memo memo = new Memo();
        Group rootGroup = memo.copyIn(plan, null);
        
        // 2. 应用探索规则
        exploreSpace(memo, context);
        
        // 3. 应用实现规则
        implementOperators(memo, context);
        
        // 4. 计算最优计划
        OptExpression bestPlan = extractBestPlan(memo, rootGroup, context);
        
        return bestPlan;
    }
    
    // 逻辑重写规则应用
    private OptExpression applyLogicalRewriteRules(OptExpression plan, OptimizerContext context) {
        List<Rule> logicalRules = ruleEngine.getLogicalRewriteRules();
        
        for (Rule rule : logicalRules) {
            if (!rule.check(plan, context)) {
                continue;
            }
            
            List<OptExpression> candidates = rule.transform(plan, context);
            
            // 选择最佳候选
            for (OptExpression candidate : candidates) {
                if (isBetterPlan(candidate, plan, context)) {
                    plan = candidate;
                }
            }
        }
        
        return plan;
    }
    
    // 谓词下推优化
    private OptExpression applyPredicatePushdownRules(OptExpression plan, OptimizerContext context) {
        PredicatePushdownOptimizer predicateOptimizer = new PredicatePushdownOptimizer();
        return predicateOptimizer.optimize(plan, context);
    }
    
    // 投影剪枝优化
    private OptExpression applyProjectionPruningRules(OptExpression plan, OptimizerContext context) {
        ProjectionPruner projectionPruner = new ProjectionPruner();
        return projectionPruner.prune(plan, context);
    }
    
    // 探索搜索空间
    private void exploreSpace(Memo memo, OptimizerContext context) {
        List<Rule> explorationRules = ruleEngine.getExplorationRules();
        
        TaskStack taskStack = new TaskStack();
        taskStack.pushTask(new OptimizeGroupTask(memo.getRootGroup()));
        
        while (!taskStack.isEmpty()) {
            Task task = taskStack.popTask();
            task.execute(memo, context, taskStack);
        }
    }
    
    // 算子实现
    private void implementOperators(Memo memo, OptimizerContext context) {
        List<Rule> implementationRules = ruleEngine.getImplementationRules();
        
        for (Group group : memo.getGroups()) {
            for (Rule rule : implementationRules) {
                for (GroupExpression groupExpr : group.getLogicalExpressions()) {
                    if (rule.check(groupExpr, context)) {
                        List<GroupExpression> implementations = 
                            rule.implement(groupExpr, context);
                        
                        for (GroupExpression impl : implementations) {
                            group.addExpression(impl);
                        }
                    }
                }
            }
        }
    }
    
    // 提取最优计划
    private OptExpression extractBestPlan(Memo memo, Group rootGroup, OptimizerContext context) {
        CostBasedPlanSelector selector = new CostBasedPlanSelector(costModel);
        return selector.selectBestPlan(memo, rootGroup, context);
    }
    
    // 初始化规则集
    private void initializeRules() {
        // 逻辑重写规则
        ruleEngine.addLogicalRule(new MergeProjectRule());
        ruleEngine.addLogicalRule(new PushDownPredicateRule());
        ruleEngine.addLogicalRule(new EliminateProjectRule());
        ruleEngine.addLogicalRule(new SimplifyPredicateRule());
        
        // 探索规则
        ruleEngine.addExplorationRule(new JoinCommutativityRule());
        ruleEngine.addExplorationRule(new JoinAssociativityRule());
        ruleEngine.addExplorationRule(new JoinReorderRule());
        
        // 实现规则
        ruleEngine.addImplementationRule(new HashJoinImplementationRule());
        ruleEngine.addImplementationRule(new NestLoopJoinImplementationRule());
        ruleEngine.addImplementationRule(new MergeJoinImplementationRule());
        ruleEngine.addImplementationRule(new HashAggregateImplementationRule());
        ruleEngine.addImplementationRule(new StreamAggregateImplementationRule());
    }
}
```

### 4.2.2 OptimizerContext设计

```java
// 优化器上下文
public class OptimizerContext {
    private final ConnectContext connectContext;
    private final ColumnRefFactory columnRefFactory;
    private final OptimizerConfig config;
    private final SessionVariable sessionVariable;
    private final Map<String, Object> properties;
    
    // 统计信息缓存
    private final Map<String, Statistics> statisticsCache = new HashMap<>();
    
    // 代价缓存
    private final Map<GroupExpression, Double> costCache = new HashMap<>();
    
    // 属性传播缓存
    private final Map<GroupExpression, PhysicalPropertySet> propertyCache = new HashMap<>();
    
    public OptimizerContext(ConnectContext connectContext,
                           ColumnRefFactory columnRefFactory,
                           OptimizerConfig config) {
        this.connectContext = connectContext;
        this.columnRefFactory = columnRefFactory;
        this.config = config;
        this.sessionVariable = connectContext.getSessionVariable();
        this.properties = new HashMap<>();
    }
    
    // 获取表统计信息
    public Statistics getTableStatistics(Table table) {
        String tableKey = table.getId() + "";
        Statistics stats = statisticsCache.get(tableKey);
        
        if (stats == null) {
            stats = StatisticsManager.getInstance().getTableStatistics(table);
            statisticsCache.put(tableKey, stats);
        }
        
        return stats;
    }
    
    // 获取列统计信息
    public ColumnStatistics getColumnStatistics(Table table, Column column) {
        String columnKey = table.getId() + "." + column.getName();
        ColumnStatistics stats = (ColumnStatistics) statisticsCache.get(columnKey);
        
        if (stats == null) {
            stats = StatisticsManager.getInstance().getColumnStatistics(table, column);
            statisticsCache.put(columnKey, stats);
        }
        
        return stats;
    }
    
    // 缓存代价信息
    public void cacheCost(GroupExpression expression, double cost) {
        costCache.put(expression, cost);
    }
    
    public Double getCachedCost(GroupExpression expression) {
        return costCache.get(expression);
    }
    
    // 优化器配置访问
    public boolean isEnableRBO() {
        return config.isEnableRuleBasedOptimize();
    }
    
    public boolean isEnableCBO() {
        return config.isEnableCostBasedOptimize();
    }
    
    public int getMaxJoinReorderSize() {
        return config.getMaxJoinReorderSize();
    }
    
    public boolean isEnableJoinReorder() {
        return sessionVariable.isCboEnable() && 
               sessionVariable.isEnableJoinReorder();
    }
    
    // 会话变量访问
    public boolean isEnableMaterializedViewRewrite() {
        return sessionVariable.isEnableMaterializedViewRewrite();
    }
    
    public boolean isEnableGlobalRuntimeFilter() {
        return sessionVariable.isEnableGlobalRuntimeFilter();
    }
    
    // 属性管理
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, Class<T> clazz) {
        Object value = properties.get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
}
```

## 4.3 Memo结构深度解析

### 4.3.1 Memo核心设计

Memo是StarRocks优化器的核心数据结构，用于紧凑地表示搜索空间中的所有等价表达式：

```java
// fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Memo.java
public class Memo {
    // 组ID计数器
    private int nextGroupId = 0;
    
    // 所有组的映射
    private final Map<Integer, Group> groups = new HashMap<>();
    
    // 根组引用
    private Group rootGroup;
    
    // 组表达式到组的映射
    private final Map<GroupExpression, Group> groupExprToGroup = new HashMap<>();
    
    // 统计信息管理
    private final StatisticsManager statisticsManager = new StatisticsManager();
    
    // 将逻辑计划复制到Memo中
    public Group copyIn(OptExpression expression, Group targetGroup) {
        // 1. 检查是否已存在等价的组表达式
        GroupExpression existingExpr = findEquivalentExpression(expression);
        if (existingExpr != null) {
            return groupExprToGroup.get(existingExpr);
        }
        
        // 2. 递归处理子节点
        List<Group> childGroups = new ArrayList<>();
        for (OptExpression child : expression.getInputs()) {
            Group childGroup = copyIn(child, null);
            childGroups.add(childGroup);
        }
        
        // 3. 创建组表达式
        GroupExpression groupExpr = new GroupExpression(
            expression.getOp(), childGroups);
        
        // 4. 决定目标组
        Group group = targetGroup;
        if (group == null) {
            group = createNewGroup(groupExpr);
        } else {
            group.addExpression(groupExpr);
        }
        
        // 5. 建立映射关系
        groupExprToGroup.put(groupExpr, group);
        
        // 6. 设置根组
        if (targetGroup == null && rootGroup == null) {
            rootGroup = group;
        }
        
        return group;
    }
    
    // 创建新组
    private Group createNewGroup(GroupExpression expression) {
        int groupId = nextGroupId++;
        Group group = new Group(groupId);
        group.addExpression(expression);
        groups.put(groupId, group);
        return group;
    }
    
    // 查找等价表达式
    private GroupExpression findEquivalentExpression(OptExpression expression) {
        for (GroupExpression groupExpr : groupExprToGroup.keySet()) {
            if (isEquivalent(expression, groupExpr)) {
                return groupExpr;
            }
        }
        return null;
    }
    
    // 检查表达式等价性
    private boolean isEquivalent(OptExpression expr, GroupExpression groupExpr) {
        // 1. 算子类型必须相同
        if (!expr.getOp().equals(groupExpr.getOp())) {
            return false;
        }
        
        // 2. 子节点数量必须相同
        if (expr.getInputs().size() != groupExpr.getInputs().size()) {
            return false;
        }
        
        // 3. 子节点必须在相同的组中
        for (int i = 0; i < expr.getInputs().size(); i++) {
            OptExpression childExpr = expr.getInputs().get(i);
            Group childGroup = groupExpr.getInputs().get(i);
            
            if (!isInGroup(childExpr, childGroup)) {
                return false;
            }
        }
        
        return true;
    }
    
    // 检查表达式是否在指定组中
    private boolean isInGroup(OptExpression expr, Group group) {
        for (GroupExpression groupExpr : group.getGroupExpressions()) {
            if (isEquivalent(expr, groupExpr)) {
                return true;
            }
        }
        return false;
    }
    
    // 合并两个组
    public void mergeGroup(Group source, Group target) {
        // 1. 移动所有表达式
        for (GroupExpression expr : source.getGroupExpressions()) {
            target.addExpression(expr);
            groupExprToGroup.put(expr, target);
        }
        
        // 2. 更新引用
        updateGroupReferences(source, target);
        
        // 3. 移除源组
        groups.remove(source.getId());
        
        // 4. 更新根组引用
        if (rootGroup == source) {
            rootGroup = target;
        }
    }
    
    // 更新组引用
    private void updateGroupReferences(Group oldGroup, Group newGroup) {
        for (Group group : groups.values()) {
            for (GroupExpression expr : group.getGroupExpressions()) {
                List<Group> inputs = expr.getInputs();
                for (int i = 0; i < inputs.size(); i++) {
                    if (inputs.get(i) == oldGroup) {
                        inputs.set(i, newGroup);
                    }
                }
            }
        }
    }
    
    // 获取组统计信息
    public Statistics getGroupStatistics(Group group) {
        Statistics stats = group.getStatistics();
        if (stats == null) {
            stats = computeGroupStatistics(group);
            group.setStatistics(stats);
        }
        return stats;
    }
    
    // 计算组统计信息
    private Statistics computeGroupStatistics(Group group) {
        // 选择一个代表性的表达式来计算统计信息
        GroupExpression representative = group.getFirstLogicalExpression();
        if (representative == null) {
            representative = group.getFirstPhysicalExpression();
        }
        
        if (representative != null) {
            return statisticsManager.computeStatistics(representative, this);
        }
        
        return Statistics.empty();
    }
    
    // 获取所有组
    public Collection<Group> getGroups() {
        return groups.values();
    }
    
    // 获取根组
    public Group getRootGroup() {
        return rootGroup;
    }
    
    // 获取组表达式的所属组
    public Group getGroupByExpression(GroupExpression expression) {
        return groupExprToGroup.get(expression);
    }
}
```

### 4.3.2 Group类实现

```java
// 组类，表示等价表达式的集合
public class Group {
    private final int id;
    private final List<GroupExpression> logicalExpressions = new ArrayList<>();
    private final List<GroupExpression> physicalExpressions = new ArrayList<>();
    
    // 统计信息
    private Statistics statistics;
    
    // 物理属性
    private final Set<PhysicalPropertySet> physicalProperties = new HashSet<>();
    
    // 最佳表达式缓存
    private final Map<PhysicalPropertySet, GroupExpression> bestExpressions = new HashMap<>();
    private final Map<PhysicalPropertySet, Double> bestCosts = new HashMap<>();
    
    // 优化状态
    private boolean explored = false;
    private boolean implemented = false;
    
    public Group(int id) {
        this.id = id;
    }
    
    // 添加组表达式
    public void addExpression(GroupExpression expression) {
        if (expression.getOp().isLogical()) {
            logicalExpressions.add(expression);
        } else {
            physicalExpressions.add(expression);
        }
        
        expression.setGroup(this);
    }
    
    // 移除组表达式
    public void removeExpression(GroupExpression expression) {
        if (expression.getOp().isLogical()) {
            logicalExpressions.remove(expression);
        } else {
            physicalExpressions.remove(expression);
        }
    }
    
    // 获取第一个逻辑表达式
    public GroupExpression getFirstLogicalExpression() {
        return logicalExpressions.isEmpty() ? null : logicalExpressions.get(0);
    }
    
    // 获取第一个物理表达式
    public GroupExpression getFirstPhysicalExpression() {
        return physicalExpressions.isEmpty() ? null : physicalExpressions.get(0);
    }
    
    // 获取所有表达式
    public List<GroupExpression> getGroupExpressions() {
        List<GroupExpression> all = new ArrayList<>();
        all.addAll(logicalExpressions);
        all.addAll(physicalExpressions);
        return all;
    }
    
    // 设置最佳表达式
    public void setBestExpression(PhysicalPropertySet properties, 
                                 GroupExpression expression, 
                                 double cost) {
        bestExpressions.put(properties, expression);
        bestCosts.put(properties, cost);
    }
    
    // 获取最佳表达式
    public GroupExpression getBestExpression(PhysicalPropertySet properties) {
        // 首先查找精确匹配
        GroupExpression exact = bestExpressions.get(properties);
        if (exact != null) {
            return exact;
        }
        
        // 查找兼容的属性
        for (Map.Entry<PhysicalPropertySet, GroupExpression> entry : bestExpressions.entrySet()) {
            if (entry.getKey().satisfies(properties)) {
                return entry.getValue();
            }
        }
        
        return null;
    }
    
    // 获取最佳代价
    public double getBestCost(PhysicalPropertySet properties) {
        Double cost = bestCosts.get(properties);
        return cost != null ? cost : Double.MAX_VALUE;
    }
    
    // 检查是否有满足属性的实现
    public boolean hasImplementation(PhysicalPropertySet properties) {
        return getBestExpression(properties) != null;
    }
    
    // 标记为已探索
    public void setExplored(boolean explored) {
        this.explored = explored;
    }
    
    public boolean isExplored() {
        return explored;
    }
    
    // 标记为已实现
    public void setImplemented(boolean implemented) {
        this.implemented = implemented;
    }
    
    public boolean isImplemented() {
        return implemented;
    }
    
    // 统计信息管理
    public void setStatistics(Statistics statistics) {
        this.statistics = statistics;
    }
    
    public Statistics getStatistics() {
        return statistics;
    }
    
    public int getId() {
        return id;
    }
    
    @Override
    public String toString() {
        return "Group{id=" + id + ", logical=" + logicalExpressions.size() + 
               ", physical=" + physicalExpressions.size() + "}";
    }
}
```

### 4.3.3 GroupExpression实现

```java
// 组表达式，表示算子及其子组的组合
public class GroupExpression {
    private final Operator op;
    private final List<Group> inputs;
    private Group group;
    
    // 代价信息
    private final Map<PhysicalPropertySet, Double> costs = new HashMap<>();
    
    // 子节点需要的属性
    private final Map<PhysicalPropertySet, List<PhysicalPropertySet>> childrenProperties = new HashMap<>();
    
    // 统计信息
    private Statistics statistics;
    
    // 优化状态
    private boolean explored = false;
    private final Set<Rule> appliedRules = new HashSet<>();
    
    public GroupExpression(Operator op, List<Group> inputs) {
        this.op = op;
        this.inputs = new ArrayList<>(inputs);
    }
    
    // 计算代价
    public double computeCost(PhysicalPropertySet properties, CostModel costModel) {
        Double cachedCost = costs.get(properties);
        if (cachedCost != null) {
            return cachedCost;
        }
        
        double cost = costModel.computeCost(this, properties);
        costs.put(properties, cost);
        return cost;
    }
    
    // 获取子节点需要的属性
    public List<PhysicalPropertySet> getChildrenRequiredProperties(PhysicalPropertySet properties) {
        List<PhysicalPropertySet> cached = childrenProperties.get(properties);
        if (cached != null) {
            return cached;
        }
        
        List<PhysicalPropertySet> required = op.getRequiredChildProperties(properties);
        childrenProperties.put(properties, required);
        return required;
    }
    
    // 检查是否满足属性
    public boolean satisfies(PhysicalPropertySet properties) {
        PhysicalPropertySet provided = op.getProvidedProperties();
        return provided.satisfies(properties);
    }
    
    // 应用规则
    public void markRuleApplied(Rule rule) {
        appliedRules.add(rule);
    }
    
    public boolean isRuleApplied(Rule rule) {
        return appliedRules.contains(rule);
    }
    
    // 获取操作符
    public Operator getOp() {
        return op;
    }
    
    // 获取输入组
    public List<Group> getInputs() {
        return inputs;
    }
    
    // 设置所属组
    public void setGroup(Group group) {
        this.group = group;
    }
    
    public Group getGroup() {
        return group;
    }
    
    // 统计信息
    public void setStatistics(Statistics statistics) {
        this.statistics = statistics;
    }
    
    public Statistics getStatistics() {
        return statistics;
    }
    
    // 探索状态
    public void setExplored(boolean explored) {
        this.explored = explored;
    }
    
    public boolean isExplored() {
        return explored;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof GroupExpression)) return false;
        
        GroupExpression other = (GroupExpression) obj;
        return op.equals(other.op) && inputs.equals(other.inputs);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(op, inputs);
    }
    
    @Override
    public String toString() {
        return "GroupExpr{op=" + op.getClass().getSimpleName() + 
               ", inputs=" + inputs.stream().map(g -> "G" + g.getId()).collect(Collectors.toList()) + "}";
    }
}
```

## 4.4 代价模型设计

### 4.4.1 CostModel核心实现

```java
// 代价模型
public class CostModel {
    private final StatisticsCalculator statisticsCalculator;
    private final CardinalityEstimator cardinalityEstimator;
    
    // CPU代价权重
    private static final double CPU_COST_WEIGHT = 1.0;
    // 内存代价权重
    private static final double MEMORY_COST_WEIGHT = 1.5;
    // IO代价权重
    private static final double IO_COST_WEIGHT = 3.0;
    // 网络代价权重
    private static final double NETWORK_COST_WEIGHT = 5.0;
    
    public CostModel() {
        this.statisticsCalculator = new StatisticsCalculator();
        this.cardinalityEstimator = new CardinalityEstimator();
    }
    
    // 计算组表达式的代价
    public double computeCost(GroupExpression expression, PhysicalPropertySet properties) {
        Operator op = expression.getOp();
        
        // 根据算子类型计算不同的代价
        if (op instanceof PhysicalOlapScanOperator) {
            return computeScanCost((PhysicalOlapScanOperator) op, expression);
        } else if (op instanceof PhysicalHashJoinOperator) {
            return computeHashJoinCost((PhysicalHashJoinOperator) op, expression, properties);
        } else if (op instanceof PhysicalNestedLoopJoinOperator) {
            return computeNestedLoopJoinCost((PhysicalNestedLoopJoinOperator) op, expression);
        } else if (op instanceof PhysicalHashAggregateOperator) {
            return computeHashAggregateCost((PhysicalHashAggregateOperator) op, expression);
        } else if (op instanceof PhysicalTopNOperator) {
            return computeTopNCost((PhysicalTopNOperator) op, expression);
        } else if (op instanceof PhysicalProjectOperator) {
            return computeProjectCost((PhysicalProjectOperator) op, expression);
        }
        
        // 默认代价计算
        return computeDefaultCost(op, expression);
    }
    
    // 扫描算子代价计算
    private double computeScanCost(PhysicalOlapScanOperator scanOp, GroupExpression expression) {
        Statistics stats = expression.getStatistics();
        if (stats == null) {
            stats = statisticsCalculator.computeStatistics(expression, null);
        }
        
        double rowCount = stats.getRowCount();
        double avgRowSize = stats.getAverageRowSize();
        
        // IO代价：需要读取的数据量
        double ioDataSize = rowCount * avgRowSize;
        double ioCost = ioDataSize * IO_COST_WEIGHT;
        
        // CPU代价：行数处理
        double cpuCost = rowCount * CPU_COST_WEIGHT;
        
        // 谓词过滤代价
        double predicateCost = computePredicateFilterCost(scanOp.getPredicate(), rowCount);
        
        return ioCost + cpuCost + predicateCost;
    }
    
    // 哈希连接代价计算
    private double computeHashJoinCost(PhysicalHashJoinOperator joinOp, 
                                     GroupExpression expression,
                                     PhysicalPropertySet properties) {
        
        Group leftGroup = expression.getInputs().get(0);
        Group rightGroup = expression.getInputs().get(1);
        
        Statistics leftStats = leftGroup.getStatistics();
        Statistics rightStats = rightGroup.getStatistics();
        
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        // 构建哈希表的代价
        double buildCost = rightRowCount * CPU_COST_WEIGHT;
        
        // 内存代价：存储哈希表
        double memorySize = rightRowCount * rightStats.getAverageRowSize();
        double memoryCost = memorySize * MEMORY_COST_WEIGHT;
        
        // 探测代价
        double probeCost = leftRowCount * CPU_COST_WEIGHT;
        
        // 输出代价
        double outputRowCount = estimateJoinOutputSize(leftStats, rightStats, joinOp.getJoinType());
        double outputCost = outputRowCount * CPU_COST_WEIGHT * 0.1;
        
        // 网络代价（如果需要重分区）
        double networkCost = 0;
        if (needsRedistribution(joinOp, properties)) {
            networkCost = (leftRowCount + rightRowCount) * 
                         leftStats.getAverageRowSize() * NETWORK_COST_WEIGHT;
        }
        
        return buildCost + memoryCost + probeCost + outputCost + networkCost;
    }
    
    // 嵌套循环连接代价计算
    private double computeNestedLoopJoinCost(PhysicalNestedLoopJoinOperator joinOp, 
                                           GroupExpression expression) {
        
        Group leftGroup = expression.getInputs().get(0);
        Group rightGroup = expression.getInputs().get(1);
        
        Statistics leftStats = leftGroup.getStatistics();
        Statistics rightStats = rightGroup.getStatistics();
        
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        // 嵌套循环的代价是左表行数 * 右表行数
        double joinCost = leftRowCount * rightRowCount * CPU_COST_WEIGHT;
        
        // 输出代价
        double outputRowCount = estimateJoinOutputSize(leftStats, rightStats, joinOp.getJoinType());
        double outputCost = outputRowCount * CPU_COST_WEIGHT * 0.1;
        
        return joinCost + outputCost;
    }
    
    // 哈希聚合代价计算
    private double computeHashAggregateCost(PhysicalHashAggregateOperator aggOp, 
                                          GroupExpression expression) {
        
        Group inputGroup = expression.getInputs().get(0);
        Statistics inputStats = inputGroup.getStatistics();
        
        double inputRowCount = inputStats.getRowCount();
        
        // 估算聚合后的行数
        double outputRowCount = estimateAggregateOutputSize(inputStats, aggOp.getGroupBys());
        
        // 构建哈希表代价
        double buildCost = inputRowCount * CPU_COST_WEIGHT;
        
        // 内存代价
        double memorySize = outputRowCount * estimateAggregateRowSize(aggOp);
        double memoryCost = memorySize * MEMORY_COST_WEIGHT;
        
        // 聚合计算代价
        double aggregateComputeCost = inputRowCount * aggOp.getAggregations().size() * CPU_COST_WEIGHT * 0.5;
        
        return buildCost + memoryCost + aggregateComputeCost;
    }
    
    // TopN代价计算
    private double computeTopNCost(PhysicalTopNOperator topNOp, GroupExpression expression) {
        Group inputGroup = expression.getInputs().get(0);
        Statistics inputStats = inputGroup.getStatistics();
        
        double inputRowCount = inputStats.getRowCount();
        long limit = topNOp.getLimit();
        
        // 排序代价，近似为 N * log(K)，其中K是TopN的大小
        double sortCost = inputRowCount * Math.log(Math.min(limit, inputRowCount)) * CPU_COST_WEIGHT;
        
        // 内存代价：存储TopN结果
        double memoryCost = Math.min(limit, inputRowCount) * 
                           inputStats.getAverageRowSize() * MEMORY_COST_WEIGHT;
        
        return sortCost + memoryCost;
    }
    
    // 投影代价计算
    private double computeProjectCost(PhysicalProjectOperator projectOp, GroupExpression expression) {
        Group inputGroup = expression.getInputs().get(0);
        Statistics inputStats = inputGroup.getStatistics();
        
        double inputRowCount = inputStats.getRowCount();
        
        // 表达式计算代价
        double expressionCost = inputRowCount * projectOp.getColumnRefMap().size() * CPU_COST_WEIGHT * 0.1;
        
        return expressionCost;
    }
    
    // 默认代价计算
    private double computeDefaultCost(Operator op, GroupExpression expression) {
        double childrenCost = 0;
        
        for (Group child : expression.getInputs()) {
            Statistics childStats = child.getStatistics();
            if (childStats != null) {
                childrenCost += childStats.getRowCount() * CPU_COST_WEIGHT * 0.1;
            }
        }
        
        return childrenCost;
    }
    
    // 估算连接输出大小
    private double estimateJoinOutputSize(Statistics leftStats, Statistics rightStats, JoinOperator.JoinType joinType) {
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        switch (joinType) {
            case INNER_JOIN:
                // 内连接：通常是较小表的行数
                return Math.min(leftRowCount, rightRowCount);
            case LEFT_OUTER_JOIN:
                return leftRowCount;
            case RIGHT_OUTER_JOIN:
                return rightRowCount;
            case FULL_OUTER_JOIN:
                return Math.max(leftRowCount, rightRowCount);
            case CROSS_JOIN:
                return leftRowCount * rightRowCount;
            default:
                return Math.max(leftRowCount, rightRowCount);
        }
    }
    
    // 估算聚合输出大小
    private double estimateAggregateOutputSize(Statistics inputStats, List<ColumnRefOperator> groupBys) {
        if (groupBys.isEmpty()) {
            // 没有GROUP BY，只有一行结果
            return 1.0;
        }
        
        double inputRowCount = inputStats.getRowCount();
        
        // 估算去重后的行数（基于不同值的数量）
        double selectivity = 1.0;
        for (ColumnRefOperator groupBy : groupBys) {
            ColumnStatistics colStats = inputStats.getColumnStatistics(groupBy);
            if (colStats != null) {
                double distinctValues = colStats.getDistinctValuesCount();
                selectivity *= distinctValues / inputRowCount;
            } else {
                // 默认选择性
                selectivity *= 0.1;
            }
        }
        
        return Math.max(1.0, inputRowCount * selectivity);
    }
    
    // 估算聚合行大小
    private double estimateAggregateRowSize(PhysicalHashAggregateOperator aggOp) {
        double size = 0;
        
        // GROUP BY列的大小
        for (ColumnRefOperator groupBy : aggOp.getGroupBys()) {
            size += groupBy.getType().getSlotSize();
        }
        
        // 聚合函数结果的大小
        for (CallOperator agg : aggOp.getAggregations().values()) {
            size += agg.getType().getSlotSize();
        }
        
        return size;
    }
    
    // 谓词过滤代价
    private double computePredicateFilterCost(ScalarOperator predicate, double rowCount) {
        if (predicate == null) {
            return 0;
        }
        
        // 谓词复杂度评估
        PredicateComplexityVisitor visitor = new PredicateComplexityVisitor();
        double complexity = predicate.accept(visitor, null);
        
        return rowCount * complexity * CPU_COST_WEIGHT * 0.1;
    }
    
    // 检查是否需要重分区
    private boolean needsRedistribution(PhysicalHashJoinOperator joinOp, PhysicalPropertySet properties) {
        // 简化实现：检查分布属性是否匹配
        DistributionProperty requiredDistribution = properties.getDistributionProperty();
        DistributionProperty providedDistribution = joinOp.getProvidedProperties().getDistributionProperty();
        
        return !requiredDistribution.equals(providedDistribution);
    }
}
```

### 4.4.2 统计信息管理

```java
// 统计信息管理器
public class StatisticsManager {
    private static final StatisticsManager INSTANCE = new StatisticsManager();
    
    // 表统计信息缓存
    private final Map<Long, TableStatistics> tableStatsCache = new ConcurrentHashMap<>();
    
    // 列统计信息缓存
    private final Map<String, ColumnStatistics> columnStatsCache = new ConcurrentHashMap<>();
    
    public static StatisticsManager getInstance() {
        return INSTANCE;
    }
    
    // 获取表统计信息
    public TableStatistics getTableStatistics(Table table) {
        Long tableId = table.getId();
        TableStatistics stats = tableStatsCache.get(tableId);
        
        if (stats == null) {
            stats = loadTableStatistics(table);
            tableStatsCache.put(tableId, stats);
        }
        
        return stats;
    }
    
    // 获取列统计信息
    public ColumnStatistics getColumnStatistics(Table table, Column column) {
        String key = table.getId() + "." + column.getName();
        ColumnStatistics stats = columnStatsCache.get(key);
        
        if (stats == null) {
            stats = loadColumnStatistics(table, column);
            columnStatsCache.put(key, stats);
        }
        
        return stats;
    }
    
    // 计算表达式的统计信息
    public Statistics computeStatistics(GroupExpression expression, Memo memo) {
        Operator op = expression.getOp();
        
        if (op instanceof LogicalOlapScanOperator) {
            return computeScanStatistics((LogicalOlapScanOperator) op);
        } else if (op instanceof LogicalJoinOperator) {
            return computeJoinStatistics((LogicalJoinOperator) op, expression, memo);
        } else if (op instanceof LogicalAggregationOperator) {
            return computeAggregateStatistics((LogicalAggregationOperator) op, expression, memo);
        } else if (op instanceof LogicalProjectOperator) {
            return computeProjectStatistics((LogicalProjectOperator) op, expression, memo);
        } else if (op instanceof LogicalFilterOperator) {
            return computeFilterStatistics((LogicalFilterOperator) op, expression, memo);
        }
        
        return computeDefaultStatistics(expression, memo);
    }
    
    // 扫描算子统计信息
    private Statistics computeScanStatistics(LogicalOlapScanOperator scanOp) {
        Table table = scanOp.getTable();
        TableStatistics tableStats = getTableStatistics(table);
        
        double rowCount = tableStats.getRowCount();
        double dataSize = tableStats.getDataSize();
        
        // 应用谓词选择性
        if (scanOp.getPredicate() != null) {
            double selectivity = estimateSelectivity(scanOp.getPredicate(), table);
            rowCount *= selectivity;
            dataSize *= selectivity;
        }
        
        Statistics.Builder builder = Statistics.builder();
        builder.setRowCount(rowCount);
        builder.setDataSize(dataSize);
        
        // 添加列统计信息
        for (ColumnRefOperator column : scanOp.getColRefToColumnMetaMap().keySet()) {
            ColumnMetadata columnMeta = scanOp.getColRefToColumnMetaMap().get(column);
            ColumnStatistics colStats = getColumnStatistics(table, columnMeta.getColumn());
            builder.addColumnStatistics(column, colStats);
        }
        
        return builder.build();
    }
    
    // 连接算子统计信息
    private Statistics computeJoinStatistics(LogicalJoinOperator joinOp, 
                                           GroupExpression expression, 
                                           Memo memo) {
        
        Group leftGroup = expression.getInputs().get(0);
        Group rightGroup = expression.getInputs().get(1);
        
        Statistics leftStats = memo.getGroupStatistics(leftGroup);
        Statistics rightStats = memo.getGroupStatistics(rightGroup);
        
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        // 估算连接结果行数
        double outputRowCount = estimateJoinCardinality(
            leftStats, rightStats, joinOp.getOnPredicate(), joinOp.getJoinType());
        
        // 估算数据大小
        double outputDataSize = outputRowCount * 
            (leftStats.getAverageRowSize() + rightStats.getAverageRowSize());
        
        Statistics.Builder builder = Statistics.builder();
        builder.setRowCount(outputRowCount);
        builder.setDataSize(outputDataSize);
        
        // 合并列统计信息
        mergeColumnStatistics(builder, leftStats, rightStats, joinOp.getJoinType());
        
        return builder.build();
    }
    
    // 聚合算子统计信息
    private Statistics computeAggregateStatistics(LogicalAggregationOperator aggOp, 
                                                 GroupExpression expression, 
                                                 Memo memo) {
        
        Group inputGroup = expression.getInputs().get(0);
        Statistics inputStats = memo.getGroupStatistics(inputGroup);
        
        double inputRowCount = inputStats.getRowCount();
        
        // 估算聚合后的行数
        double outputRowCount;
        if (aggOp.getGroupingKeys().isEmpty()) {
            // 没有GROUP BY，只有一行
            outputRowCount = 1.0;
        } else {
            // 基于GROUP BY列的不同值数量估算
            outputRowCount = estimateGroupByCardinality(inputStats, aggOp.getGroupingKeys());
        }
        
        // 估算行大小
        double avgRowSize = estimateAggregateRowSize(aggOp.getGroupingKeys(), aggOp.getAggregations());
        double outputDataSize = outputRowCount * avgRowSize;
        
        Statistics.Builder builder = Statistics.builder();
        builder.setRowCount(outputRowCount);
        builder.setDataSize(outputDataSize);
        
        // 添加GROUP BY列的统计信息
        for (ColumnRefOperator groupBy : aggOp.getGroupingKeys()) {
            ColumnStatistics inputColStats = inputStats.getColumnStatistics(groupBy);
            if (inputColStats != null) {
                ColumnStatistics outputColStats = inputColStats.copy();
                outputColStats.setDistinctValuesCount(outputRowCount);
                builder.addColumnStatistics(groupBy, outputColStats);
            }
        }
        
        return builder.build();
    }
    
    // 估算选择性
    private double estimateSelectivity(ScalarOperator predicate, Table table) {
        SelectivityEstimator estimator = new SelectivityEstimator(this, table);
        return predicate.accept(estimator, null);
    }
    
    // 估算连接基数
    private double estimateJoinCardinality(Statistics leftStats, Statistics rightStats, 
                                         ScalarOperator joinCondition, JoinOperator.JoinType joinType) {
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        // 基于连接条件估算选择性
        double selectivity = 0.1; // 默认选择性
        
        if (joinCondition != null) {
            JoinSelectivityEstimator estimator = new JoinSelectivityEstimator(leftStats, rightStats);
            selectivity = joinCondition.accept(estimator, null);
        }
        
        switch (joinType) {
            case INNER_JOIN:
                return leftRowCount * rightRowCount * selectivity;
            case LEFT_OUTER_JOIN:
                return Math.max(leftRowCount, leftRowCount * rightRowCount * selectivity);
            case RIGHT_OUTER_JOIN:
                return Math.max(rightRowCount, leftRowCount * rightRowCount * selectivity);
            case FULL_OUTER_JOIN:
                return leftRowCount + rightRowCount + leftRowCount * rightRowCount * selectivity;
            case CROSS_JOIN:
                return leftRowCount * rightRowCount;
            default:
                return Math.max(leftRowCount, rightRowCount);
        }
    }
    
    // 从存储加载表统计信息
    private TableStatistics loadTableStatistics(Table table) {
        // 实际实现会从统计信息存储中加载
        // 这里提供一个简化的实现
        
        long rowCount = 1000000; // 默认行数
        long dataSize = rowCount * 100; // 默认数据大小
        
        if (table instanceof OlapTable) {
            OlapTable olapTable = (OlapTable) table;
            // 从分区信息中获取更准确的统计信息
            rowCount = olapTable.getPartitions().stream()
                .mapToLong(Partition::getVisibleVersion)
                .sum();
        }
        
        return new TableStatistics(rowCount, dataSize);
    }
    
    // 从存储加载列统计信息
    private ColumnStatistics loadColumnStatistics(Table table, Column column) {
        // 实际实现会从统计信息存储中加载
        // 这里提供一个简化的实现
        
        Type columnType = column.getType();
        double distinctCount = 1000; // 默认不同值数量
        double nullCount = 0; // 默认空值数量
        
        Object minValue = null;
        Object maxValue = null;
        
        // 根据类型设置默认值
        if (columnType.isNumericType()) {
            minValue = 0;
            maxValue = 1000000;
        } else if (columnType.isStringType()) {
            minValue = "";
            maxValue = "zzz";
        }
        
        return new ColumnStatistics(distinctCount, nullCount, minValue, maxValue);
    }
}
```

## 4.5 规则引擎设计

### 4.5.1 Rule基类和规则类型

```java
// 规则基类
public abstract class Rule {
    protected final RuleType type;
    protected final String name;
    protected final Pattern pattern;
    
    public enum RuleType {
        LOGICAL_REWRITE,    // 逻辑重写规则
        EXPLORATION,        // 探索规则
        IMPLEMENTATION     // 实现规则
    }
    
    public Rule(RuleType type, String name, Pattern pattern) {
        this.type = type;
        this.name = name;
        this.pattern = pattern;
    }
    
    // 检查规则是否适用
    public boolean check(OptExpression expression, OptimizerContext context) {
        return pattern.match(expression);
    }
    
    public boolean check(GroupExpression expression, OptimizerContext context) {
        return pattern.match(expression);
    }
    
    // 应用规则进行变换
    public abstract List<OptExpression> transform(OptExpression expression, OptimizerContext context);
    
    // 应用规则进行实现
    public List<GroupExpression> implement(GroupExpression expression, OptimizerContext context) {
        throw new UnsupportedOperationException("This rule does not support implementation");
    }
    
    public RuleType getType() { return type; }
    public String getName() { return name; }
    public Pattern getPattern() { return pattern; }
}

// 规则模式匹配
public class Pattern {
    private final OperatorType operatorType;
    private final List<Pattern> children;
    private final Predicate<Operator> predicate;
    
    public Pattern(OperatorType operatorType) {
        this(operatorType, Collections.emptyList(), op -> true);
    }
    
    public Pattern(OperatorType operatorType, List<Pattern> children) {
        this(operatorType, children, op -> true);
    }
    
    public Pattern(OperatorType operatorType, List<Pattern> children, Predicate<Operator> predicate) {
        this.operatorType = operatorType;
        this.children = children;
        this.predicate = predicate;
    }
    
    // 匹配表达式
    public boolean match(OptExpression expression) {
        // 检查算子类型
        if (expression.getOp().getOpType() != operatorType) {
            return false;
        }
        
        // 检查谓词条件
        if (!predicate.test(expression.getOp())) {
            return false;
        }
        
        // 检查子节点数量
        if (expression.getInputs().size() != children.size()) {
            return false;
        }
        
        // 递归检查子节点
        for (int i = 0; i < children.size(); i++) {
            if (!children.get(i).match(expression.getInputs().get(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    // 匹配组表达式
    public boolean match(GroupExpression expression) {
        // 检查算子类型
        if (expression.getOp().getOpType() != operatorType) {
            return false;
        }
        
        // 检查谓词条件
        if (!predicate.test(expression.getOp())) {
            return false;
        }
        
        // 检查子节点数量
        if (expression.getInputs().size() != children.size()) {
            return false;
        }
        
        // 对于组表达式，子节点是组，需要检查组中是否有匹配的表达式
        for (int i = 0; i < children.size(); i++) {
            Group childGroup = expression.getInputs().get(i);
            boolean hasMatch = false;
            
            for (GroupExpression childExpr : childGroup.getGroupExpressions()) {
                if (children.get(i).match(childExpr)) {
                    hasMatch = true;
                    break;
                }
            }
            
            if (!hasMatch) {
                return false;
            }
        }
        
        return true;
    }
}
```

### 4.5.2 具体规则实现示例

```java
// 谓词下推规则
public class PredicatePushdownRule extends Rule {
    
    public PredicatePushdownRule() {
        super(RuleType.LOGICAL_REWRITE, "PredicatePushdown", 
              new Pattern(OperatorType.LOGICAL_FILTER,
                         Arrays.asList(new Pattern(OperatorType.LOGICAL_JOIN))));
    }
    
    @Override
    public List<OptExpression> transform(OptExpression expression, OptimizerContext context) {
        LogicalFilterOperator filter = (LogicalFilterOperator) expression.getOp();
        OptExpression joinExpr = expression.getInputs().get(0);
        LogicalJoinOperator join = (LogicalJoinOperator) joinExpr.getOp();
        
        // 分析谓词，确定哪些可以下推
        ScalarOperator predicate = filter.getPredicate();
        List<ScalarOperator> leftPredicates = new ArrayList<>();
        List<ScalarOperator> rightPredicates = new ArrayList<>();
        List<ScalarOperator> remainingPredicates = new ArrayList<>();
        
        classifyPredicates(predicate, joinExpr.getInputs().get(0), joinExpr.getInputs().get(1),
                          leftPredicates, rightPredicates, remainingPredicates);
        
        // 构建新的表达式树
        OptExpression leftChild = joinExpr.getInputs().get(0);
        OptExpression rightChild = joinExpr.getInputs().get(1);
        
        // 对左子树应用谓词
        if (!leftPredicates.isEmpty()) {
            ScalarOperator leftPredicate = Utils.compoundAnd(leftPredicates);
            leftChild = OptExpression.create(new LogicalFilterOperator(leftPredicate),
                                           Arrays.asList(leftChild));
        }
        
        // 对右子树应用谓词
        if (!rightPredicates.isEmpty()) {
            ScalarOperator rightPredicate = Utils.compoundAnd(rightPredicates);
            rightChild = OptExpression.create(new LogicalFilterOperator(rightPredicate),
                                            Arrays.asList(rightChild));
        }
        
        // 重构连接
        OptExpression newJoin = OptExpression.create(join, Arrays.asList(leftChild, rightChild));
        
        // 如果还有剩余谓词，添加过滤器
        if (!remainingPredicates.isEmpty()) {
            ScalarOperator remainingPredicate = Utils.compoundAnd(remainingPredicates);
            newJoin = OptExpression.create(new LogicalFilterOperator(remainingPredicate),
                                         Arrays.asList(newJoin));
        }
        
        return Arrays.asList(newJoin);
    }
    
    // 分类谓词
    private void classifyPredicates(ScalarOperator predicate,
                                   OptExpression leftChild,
                                   OptExpression rightChild,
                                   List<ScalarOperator> leftPredicates,
                                   List<ScalarOperator> rightPredicates,
                                   List<ScalarOperator> remainingPredicates) {
        
        if (predicate instanceof BinaryPredicateOperator) {
            BinaryPredicateOperator binary = (BinaryPredicateOperator) predicate;
            
            if (binary.getBinaryType() == BinaryPredicateOperator.BinaryType.AND) {
                // 递归处理AND的两边
                classifyPredicates(binary.getChild(0), leftChild, rightChild,
                                 leftPredicates, rightPredicates, remainingPredicates);
                classifyPredicates(binary.getChild(1), leftChild, rightChild,
                                 leftPredicates, rightPredicates, remainingPredicates);
            } else {
                // 分析谓词涉及的列
                Set<ColumnRefOperator> predicateColumns = extractColumns(predicate);
                Set<ColumnRefOperator> leftColumns = extractOutputColumns(leftChild);
                Set<ColumnRefOperator> rightColumns = extractOutputColumns(rightChild);
                
                if (leftColumns.containsAll(predicateColumns)) {
                    // 只涉及左子树的列
                    leftPredicates.add(predicate);
                } else if (rightColumns.containsAll(predicateColumns)) {
                    // 只涉及右子树的列
                    rightPredicates.add(predicate);
                } else {
                    // 涉及两个子树的列，不能下推
                    remainingPredicates.add(predicate);
                }
            }
        } else {
            // 其他类型的谓词
            Set<ColumnRefOperator> predicateColumns = extractColumns(predicate);
            Set<ColumnRefOperator> leftColumns = extractOutputColumns(leftChild);
            Set<ColumnRefOperator> rightColumns = extractOutputColumns(rightChild);
            
            if (leftColumns.containsAll(predicateColumns)) {
                leftPredicates.add(predicate);
            } else if (rightColumns.containsAll(predicateColumns)) {
                rightPredicates.add(predicate);
            } else {
                remainingPredicates.add(predicate);
            }
        }
    }
}

// 连接交换律规则
public class JoinCommutativityRule extends Rule {
    
    public JoinCommutativityRule() {
        super(RuleType.EXPLORATION, "JoinCommutativity",
              new Pattern(OperatorType.LOGICAL_JOIN));
    }
    
    @Override
    public List<OptExpression> transform(OptExpression expression, OptimizerContext context) {
        LogicalJoinOperator join = (LogicalJoinOperator) expression.getOp();
        
        // 只有某些连接类型支持交换
        if (!isCommutable(join.getJoinType())) {
            return Collections.emptyList();
        }
        
        OptExpression leftChild = expression.getInputs().get(0);
        OptExpression rightChild = expression.getInputs().get(1);
        
        // 交换左右子树
        OptExpression swappedJoin = OptExpression.create(
            new LogicalJoinOperator(swapJoinType(join.getJoinType()),
                                   join.getOnPredicate()),
            Arrays.asList(rightChild, leftChild)
        );
        
        return Arrays.asList(swappedJoin);
    }
    
    private boolean isCommutable(JoinOperator.JoinType joinType) {
        switch (joinType) {
            case INNER_JOIN:
            case CROSS_JOIN:
                return true;
            case LEFT_OUTER_JOIN:
            case RIGHT_OUTER_JOIN:
            case FULL_OUTER_JOIN:
            case LEFT_SEMI_JOIN:
            case RIGHT_SEMI_JOIN:
            case LEFT_ANTI_JOIN:
            case RIGHT_ANTI_JOIN:
                return false;
            default:
                return false;
        }
    }
    
    private JoinOperator.JoinType swapJoinType(JoinOperator.JoinType joinType) {
        switch (joinType) {
            case LEFT_OUTER_JOIN:
                return JoinOperator.JoinType.RIGHT_OUTER_JOIN;
            case RIGHT_OUTER_JOIN:
                return JoinOperator.JoinType.LEFT_OUTER_JOIN;
            case LEFT_SEMI_JOIN:
                return JoinOperator.JoinType.RIGHT_SEMI_JOIN;
            case RIGHT_SEMI_JOIN:
                return JoinOperator.JoinType.LEFT_SEMI_JOIN;
            case LEFT_ANTI_JOIN:
                return JoinOperator.JoinType.RIGHT_ANTI_JOIN;
            case RIGHT_ANTI_JOIN:
                return JoinOperator.JoinType.LEFT_ANTI_JOIN;
            default:
                return joinType;
        }
    }
}

// 哈希连接实现规则
public class HashJoinImplementationRule extends Rule {
    
    public HashJoinImplementationRule() {
        super(RuleType.IMPLEMENTATION, "HashJoinImplementation",
              new Pattern(OperatorType.LOGICAL_JOIN));
    }
    
    @Override
    public List<GroupExpression> implement(GroupExpression expression, OptimizerContext context) {
        LogicalJoinOperator logicalJoin = (LogicalJoinOperator) expression.getOp();
        
        // 分析连接条件，提取等值条件
        List<BinaryPredicateOperator> eqPredicates = extractEqualityPredicates(logicalJoin.getOnPredicate());
        
        if (eqPredicates.isEmpty() && logicalJoin.getJoinType() != JoinOperator.JoinType.CROSS_JOIN) {
            // 没有等值条件，不适合哈希连接
            return Collections.emptyList();
        }
        
        // 创建哈希连接算子
        PhysicalHashJoinOperator hashJoin = new PhysicalHashJoinOperator(
            logicalJoin.getJoinType(),
            logicalJoin.getOnPredicate(),
            logicalJoin.getPredicate(),
            logicalJoin.getJoinHint()
        );
        
        GroupExpression physicalExpr = new GroupExpression(hashJoin, expression.getInputs());
        
        return Arrays.asList(physicalExpr);
    }
    
    private List<BinaryPredicateOperator> extractEqualityPredicates(ScalarOperator predicate) {
        List<BinaryPredicateOperator> eqPredicates = new ArrayList<>();
        
        if (predicate instanceof BinaryPredicateOperator) {
            BinaryPredicateOperator binary = (BinaryPredicateOperator) predicate;
            
            if (binary.getBinaryType() == BinaryPredicateOperator.BinaryType.EQ) {
                eqPredicates.add(binary);
            } else if (binary.getBinaryType() == BinaryPredicateOperator.BinaryType.AND) {
                eqPredicates.addAll(extractEqualityPredicates(binary.getChild(0)));
                eqPredicates.addAll(extractEqualityPredicates(binary.getChild(1)));
            }
        }
        
        return eqPredicates;
    }
}
```

## 4.6 优化任务调度

### 4.6.1 任务调度框架

```java
// 优化任务基类
public abstract class Task {
    protected final TaskType type;
    
    public enum TaskType {
        OPTIMIZE_GROUP,         // 优化组任务
        OPTIMIZE_EXPRESSION,    // 优化表达式任务
        EXPLORE_GROUP,          // 探索组任务
        IMPLEMENT_GROUP,        // 实现组任务
        DERIVE_STATS           // 推导统计信息任务
    }
    
    public Task(TaskType type) {
        this.type = type;
    }
    
    // 执行任务
    public abstract void execute(Memo memo, OptimizerContext context, TaskStack taskStack);
    
    public TaskType getType() { return type; }
}

// 任务栈
public class TaskStack {
    private final Stack<Task> tasks = new Stack<>();
    
    public void pushTask(Task task) {
        tasks.push(task);
    }
    
    public Task popTask() {
        return tasks.pop();
    }
    
    public boolean isEmpty() {
        return tasks.isEmpty();
    }
    
    public int size() {
        return tasks.size();
    }
}

// 优化组任务
public class OptimizeGroupTask extends Task {
    private final Group group;
    private final PhysicalPropertySet requiredProperties;
    
    public OptimizeGroupTask(Group group) {
        this(group, PhysicalPropertySet.EMPTY);
    }
    
    public OptimizeGroupTask(Group group, PhysicalPropertySet requiredProperties) {
        super(TaskType.OPTIMIZE_GROUP);
        this.group = group;
        this.requiredProperties = requiredProperties;
    }
    
    @Override
    public void execute(Memo memo, OptimizerContext context, TaskStack taskStack) {
        // 检查组是否已经优化过
        if (group.getBestExpression(requiredProperties) != null) {
            return;
        }
        
        // 如果组还没有探索过，先进行探索
        if (!group.isExplored()) {
            taskStack.pushTask(new ExploreGroupTask(group));
            taskStack.pushTask(this); // 重新安排自己
            return;
        }
        
        // 如果组还没有实现过，先进行实现
        if (!group.isImplemented()) {
            taskStack.pushTask(new ImplementGroupTask(group));
            taskStack.pushTask(this); // 重新安排自己
            return;
        }
        
        // 对组中的每个物理表达式进行优化
        double bestCost = Double.MAX_VALUE;
        GroupExpression bestExpression = null;
        
        for (GroupExpression expression : group.getPhysicalExpressions()) {
            taskStack.pushTask(new OptimizeExpressionTask(expression, requiredProperties));
            
            // 计算表达式的代价
            double cost = expression.computeCost(requiredProperties, context.getCostModel());
            
            if (cost < bestCost) {
                bestCost = cost;
                bestExpression = expression;
            }
        }
        
        // 设置最佳表达式
        if (bestExpression != null) {
            group.setBestExpression(requiredProperties, bestExpression, bestCost);
        }
    }
}

// 探索组任务
public class ExploreGroupTask extends Task {
    private final Group group;
    
    public ExploreGroupTask(Group group) {
        super(TaskType.EXPLORE_GROUP);
        this.group = group;
    }
    
    @Override
    public void execute(Memo memo, OptimizerContext context, TaskStack taskStack) {
        if (group.isExplored()) {
            return;
        }
        
        // 获取探索规则
        List<Rule> explorationRules = context.getRuleEngine().getExplorationRules();
        
        // 对组中的每个逻辑表达式应用探索规则
        for (GroupExpression expression : new ArrayList<>(group.getLogicalExpressions())) {
            for (Rule rule : explorationRules) {
                if (expression.isRuleApplied(rule)) {
                    continue;
                }
                
                if (rule.check(expression, context)) {
                    // 应用规则生成新的表达式
                    List<GroupExpression> newExpressions = rule.transform(expression, context);
                    
                    for (GroupExpression newExpr : newExpressions) {
                        // 检查是否已存在等价表达式
                        if (!group.containsEquivalentExpression(newExpr)) {
                            group.addExpression(newExpr);
                        }
                    }
                    
                    expression.markRuleApplied(rule);
                }
            }
        }
        
        // 递归探索子组
        for (GroupExpression expression : group.getLogicalExpressions()) {
            for (Group childGroup : expression.getInputs()) {
                if (!childGroup.isExplored()) {
                    taskStack.pushTask(new ExploreGroupTask(childGroup));
                }
            }
        }
        
        group.setExplored(true);
    }
}

// 实现组任务
public class ImplementGroupTask extends Task {
    private final Group group;
    
    public ImplementGroupTask(Group group) {
        super(TaskType.IMPLEMENT_GROUP);
        this.group = group;
    }
    
    @Override
    public void execute(Memo memo, OptimizerContext context, TaskStack taskStack) {
        if (group.isImplemented()) {
            return;
        }
        
        // 获取实现规则
        List<Rule> implementationRules = context.getRuleEngine().getImplementationRules();
        
        // 对组中的每个逻辑表达式应用实现规则
        for (GroupExpression expression : group.getLogicalExpressions()) {
            for (Rule rule : implementationRules) {
                if (rule.check(expression, context)) {
                    List<GroupExpression> implementations = rule.implement(expression, context);
                    
                    for (GroupExpression impl : implementations) {
                        group.addExpression(impl);
                    }
                }
            }
        }
        
        // 递归实现子组
        for (GroupExpression expression : group.getGroupExpressions()) {
            for (Group childGroup : expression.getInputs()) {
                if (!childGroup.isImplemented()) {
                    taskStack.pushTask(new ImplementGroupTask(childGroup));
                }
            }
        }
        
        group.setImplemented(true);
    }
}

// 优化表达式任务
public class OptimizeExpressionTask extends Task {
    private final GroupExpression expression;
    private final PhysicalPropertySet requiredProperties;
    
    public OptimizeExpressionTask(GroupExpression expression, PhysicalPropertySet requiredProperties) {
        super(TaskType.OPTIMIZE_EXPRESSION);
        this.expression = expression;
        this.requiredProperties = requiredProperties;
    }
    
    @Override
    public void execute(Memo memo, OptimizerContext context, TaskStack taskStack) {
        // 检查表达式是否已经优化过
        if (expression.getCachedCost(requiredProperties) != null) {
            return;
        }
        
        // 获取子节点需要的属性
        List<PhysicalPropertySet> childrenRequiredProperties = 
            expression.getChildrenRequiredProperties(requiredProperties);
        
        // 优化所有子组
        for (int i = 0; i < expression.getInputs().size(); i++) {
            Group childGroup = expression.getInputs().get(i);
            PhysicalPropertySet childProperties = childrenRequiredProperties.get(i);
            
            taskStack.pushTask(new OptimizeGroupTask(childGroup, childProperties));
        }
        
        // 计算表达式的总代价
        double totalCost = expression.computeCost(requiredProperties, context.getCostModel());
        
        // 添加子节点的代价
        for (int i = 0; i < expression.getInputs().size(); i++) {
            Group childGroup = expression.getInputs().get(i);
            PhysicalPropertySet childProperties = childrenRequiredProperties.get(i);
            
            double childCost = childGroup.getBestCost(childProperties);
            totalCost += childCost;
        }
        
        // 缓存代价
        expression.cacheCost(requiredProperties, totalCost);
    }
}
```

## 总结

StarRocks的查询优化器体现了现代数据库优化器的先进设计理念。通过规则化优化和代价化优化的结合，Memo结构的紧凑表示，以及基于任务的调度框架，StarRocks能够为复杂的OLAP查询生成高效的执行计划。

关键设计特点包括：

1. **两阶段优化架构**: RBO负责逻辑重写，CBO负责物理实现选择
2. **Memo紧凑表示**: 高效管理等价表达式的搜索空间
3. **精确的代价模型**: 综合考虑CPU、内存、IO、网络等多种代价因素
4. **灵活的规则引擎**: 支持多种类型规则的灵活组合和扩展
5. **任务调度框架**: 确保优化过程的正确性和效率

这些设计使StarRocks能够处理复杂的多表连接、聚合、排序等OLAP查询，并在大规模数据场景下保持优秀的性能。在下一章中，我们将深入分析StarRocks的物化视图自动改写机制，这是其在OLAP场景下的一个重要优化特性。