# 第三章：语义分析与元数据管理

## 1. 引言

如果说SQL解析是将用户的语言（SQL文本）翻译成计算机能够理解的结构（AST），那么语义分析就是赋予这个结构以确切“意义”的过程。在得到AST之后，系统只知道其语法结构，但并不知道`users`是不是一个真实存在的表，`name`是不是`users`表里的一个列，`+`操作符两边的类型是否匹配。本章将深入探讨StarRocks的语义分析模块，揭示它如何校验AST的合法性，并将其与底层的元数据进行绑定，最终生成一个初步的、合法的逻辑执行计划。

## 2. 语义分析的核心任务

语义分析（Semantic Analysis）的核心任务可以归结为**验证**和**注解**：

1.  **验证（Validation）**：
    *   **名称解析**：检查AST中引用的数据库、表、列、函数等是否存在。
    *   **类型检查**：检查操作符、函数调用的参数类型是否正确，例如，不能对字符串进行加法运算。
    *   **权限检查**：检查执行查询的用户是否拥有访问相关表和列的权限。
2.  **注解（Annotation）**：
    *   **绑定元数据**：将AST中的表名、列名等标识符，与元数据中心（Catalog）中具体的表对象、列对象进行绑定。
    *   **类型推导**：确定表达式的返回类型，例如`a + b`的类型取决于`a`和`b`的类型。
    *   **隐式类型转换**：在必要时，向AST中插入类型转换函数，以满足操作符或函数的类型要求。

经过语义分析后，AST就从一个纯粹的语法结构，演变成了一个带有丰富元数据信息、类型信息、并且语义上完全合法的逻辑计划。

## 3. 源码分析：赋予AST意义

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/sql/analyzer/Analyzer.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/analyzer/AnalyzerVisitor.java`
*   `fe/fe-core/src/main/java/com/starrocks/catalog/Catalog.java`

### 3.1. `Analyzer`与`AnalyzerVisitor`

与`AstBuilder`类似，StarRocks的语义分析器也采用了**访问者模式**。`AnalyzerVisitor`是其核心实现，它会自底向上地遍历AST。

`Analyzer.java`是驱动整个分析过程的入口。

```java
// Analyzer.java (Simplified)
public class Analyzer {
    public static void analyze(StatementBase statement, ConnectContext context) {
        new AnalyzerVisitor(context).visit(statement);
    }
}
```

`AnalyzerVisitor`中的`visitXxx`方法负责处理对应类型的AST节点。例如，`visitSelectRelation`会负责分析`SELECT`语句的各个子句。

### 3.2. 名称解析与元数据绑定

当`AnalyzerVisitor`访问到一个表名（`TableName`节点）时，它会查询`Catalog`来查找这个表。

```java
// AnalyzerVisitor.java - visitTable (Simplified)
@Override
public Void visitTable(TableRelation node, AnalyzeState state) {
    // 1. Get table name from AST node
    TableName tableName = node.getName();

    // 2. Look up table in the Catalog
    Table table = Catalog.getInstance().getTable(tableName.getDb(), tableName.getTbl());
    if (table == null) {
        throw new SemanticException("Table not found: " + tableName);
    }

    // 3. Bind the table object to the AST node
    node.setTable(table);

    // 4. Create a scope for this table, containing all its columns
    Scope scope = new Scope(RelationId.of(node), table.getBaseSchema());
    state.addScope(scope);
    return null;
}
```
在处理完`FROM`子句后，分析器就构建了一个**Scope**，它记录了当前可见的列。当分析器接着访问`SELECT`列表、`WHERE`子句中的列名时，它就会在这个`Scope`中进行查找和绑定。

### 3.3. 类型检查与推导

对于表达式节点（如`BinaryPredicate`、`ArithmeticExpr`），分析器会递归地分析其子节点，获取子表达式的返回类型，然后根据操作符（`+`, `-`, `=`, `>`等）的规则来检查类型兼容性，并推导出当前表达式的返回类型。

```java
// AnalyzerVisitor.java - visitArithmeticExpr (Simplified)
@Override
public Void visitArithmeticExpr(ArithmeticExpr node, AnalyzeState state) {
    // Recursively analyze children
    visit(node.getChild(0), state);
    visit(node.getChild(1), state);

    // Get children's types
    Type leftType = node.getChild(0).getType();
    Type rightType = node.getChild(1).getType();

    // Find a common type for the operation
    Type resultType = Type.getAssignmentCompatibleType(leftType, rightType, false);
    if (!resultType.isValid()) {
        throw new SemanticException("Incompatible types for operator " + node.getOp());
    }

    // If types are not identical, add implicit cast
    if (!leftType.equals(resultType)) {
        node.setChild(0, new CastExpr(resultType, node.getChild(0)));
    }
    if (!rightType.equals(resultType)) {
        node.setChild(1, new CastExpr(resultType, node.getChild(1)));
    }

    // Set the result type for this expression node
    node.setType(resultType);
    return null;
}
```

## 4. 元数据管理：`Catalog`

`Catalog`是StarRocks FE中元数据的心脏。它以内存中的对象形式，缓存了关于数据库、表、分区、副本、列、函数等所有元数据信息。`Catalog`的实现是线程安全的，并由BDBJE（Berkeley DB Java Edition）支持，通过多副本日志复制和选举，保证了元数据在多个FE节点之间的一致性和高可用性。语义分析过程中的所有名称解析，最终都会落到对`Catalog`的查询上。

## 5. 总结

语义分析是连接语法世界和物理世界的桥梁。StarRocks通过基于访问者模式的`AnalyzerVisitor`，系统地遍历AST，并与`Catalog`中权威的元数据进行交互，完成了名称解析、类型检查和元数据绑定等关键任务。经过这个阶段，一个纯粹的语法树就转变成了一个语义合法、信息丰富的逻辑计划，为下一阶段的查询优化做好了充分的准备。
