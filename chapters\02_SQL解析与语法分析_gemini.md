# 第二章：SQL解析与语法分析深度解析

## 1. 引言

SQL作为用户与数据库交互的语言，其解析和分析是所有查询处理流程的第一步，也是至关重要的一步。这一步的准确性和效率直接影响后续所有环节。本章将深入StarRocks的SQL解析模块，详细剖析其如何将一条SQL文本字符串，一步步转换为结构化的、可被计算机理解的抽象语法树（Abstract Syntax Tree, AST）。我们将重点分析StarRocks如何利用强大的ANTLR v4语法解析生成器来定义和扩展SQL方言，并探讨`SqlParser.java`和`AstBuilder.java`在这一过程中的核心作用。

## 2. SQL解析的核心任务

SQL解析主要包含两个阶段：

1.  **词法分析（Lexical Analysis）**：将SQL字符串分解成一个个有意义的最小单元，称为Token。例如，`SELECT id, name FROM users WHERE id > 100`会被分解为`SELECT`, `id`, `,`, `name`, `FROM`, `users`, `WHERE`, `id`, `>`, `100`等Token。
2.  **语法分析（Syntax Analysis）**：根据预定义的SQL语法规则（Grammar），将Token流组合成一个树状结构，即AST。AST能够清晰地表达出SQL语句中各个组件的层次关系和逻辑关系。

## 3. 源码分析：从文本到AST

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/sql/parser/SqlParser.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/parser/AstBuilder.java`
*   `fe/fe-core/src/main/antlr4/com/starrocks/sql/parser/StarRocksSql.g4`

### 3.1. ANTLR与StarRocksSql.g4

StarRocks使用**ANTLR v4**（ANother Tool for Language Recognition）作为其SQL解析器生成工具。ANTLR的强大之处在于，开发者只需要在一个`.g4`文件中定义好语言的词法和语法规则，ANTLR就能自动生成对应的词法分析器（Lexer）和语法分析器（Parser）的Java代码。

`StarRocksSql.g4`是StarRocks SQL语法的核心定义文件。它包含了所有StarRocks支持的SQL语句（`SELECT`, `INSERT`, `CREATE TABLE`等）的语法规则。

```g4
// StarRocksSql.g4 (Simplified Example)
grammar StarRocksSql;

// Entry rule
singleStatement: statement EOF;

statement:
    query | ... ;

query:
    queryNoWith
    (WITH withClause)?
    ;

queryNoWith:
    SELECT ... |
    ( querySpecification ) |
    ...
    ;

// Lexer rules
SELECT: 'SELECT';
FROM: 'FROM';
ID: [a-zA-Z_][a-zA-Z_0-9]*;
...
```

### 3.2. `SqlParser.java`：解析的执行者

`SqlParser.java`是SQL解析的入口类。它的核心方法`parse`接收SQL字符串，并调用ANTLR生成的解析器来完成工作。

```java
// SqlParser.java (Simplified)
public class SqlParser {
    public static StatementBase parse(String sql, SqlModeHelper.SqlMode sqlMode) {
        // 1. Create CharStream from SQL string
        CharStream stream = CharStreams.fromString(sql);

        // 2. Create Lexer
        StarRocksLexer lexer = new StarRocksLexer(stream);

        // 3. Create TokenStream
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);

        // 4. Create Parser
        StarRocksParser parser = new StarRocksParser(tokenStream);

        // 5. Start parsing from the entry rule 'singleStatement'
        ParseTree tree = parser.singleStatement();

        // 6. Build AST from ParseTree
        return (StatementBase) new AstBuilder(sqlMode).visit(tree);
    }
}
```
这个过程清晰地展示了ANTLR的标准工作流程：`String -> CharStream -> Lexer -> TokenStream -> Parser -> ParseTree`。

### 3.3. `AstBuilder.java`：AST的构建师

ANTLR生成的`ParseTree`是一个通用的树结构，它与语法规则强相关，但对于上层处理来说不够直观。`AstBuilder.java`的作用就是将这个`ParseTree`转换为StarRocks自定义的、面向对象的AST。

`AstBuilder`继承自ANTLR生成的`StarRocksSqlBaseVisitor`，它采用**访问者模式**遍历`ParseTree`的每个节点。对于`StarRocksSql.g4`中定义的每条规则，`AstBuilder`中几乎都有一个对应的`visitXxx`方法。在这些方法中，它会创建出StarRocks中定义的各种AST节点类（如`QueryStmt`, `SelectRelation`, `BinaryPredicate`等），并把它们组装起来。

```java
// AstBuilder.java (Simplified)
public class AstBuilder extends StarRocksSqlBaseVisitor<Node> {
    @Override
    public Node visitQuerySpecification(StarRocksParser.QuerySpecificationContext ctx) {
        // Create a SelectRelation AST node
        SelectRelation selectRelation = new SelectRelation(...);

        // Visit child nodes to build up the SelectRelation
        selectRelation.setSelectList(visit(ctx.selectItem()));
        selectRelation.setRelation(visit(ctx.relation()));
        if (ctx.WHERE() != null) {
            selectRelation.setWhereClause(visit(ctx.booleanExpression()));
        }
        // ... and so on for GROUP BY, HAVING, ORDER BY, LIMIT
        return selectRelation;
    }
}
```

## 4. 设计思想与权衡

*   **生成器 vs. 手写**：使用ANTLR这样的解析器生成器，极大地提高了开发效率和可维护性。当需要支持新的SQL语法时，只需修改`.g4`文件并重新生成代码即可，而无需手动编写复杂的解析逻辑。
*   **两阶段构建**：`ParseTree -> AST`的两阶段构建模式是一种解耦。`ParseTree`忠实于语法定义，而`AST`则是为上层逻辑（如语义分析、优化）量身定做的，更易于使用。

## 5. 总结

SQL解析是查询处理的门户。StarRocks通过ANTLR高效地定义和实现了强大的SQL语法支持，并通过`AstBuilder`将解析结果转换为清晰、易于处理的AST。这个结构化的AST是后续所有查询优化和执行步骤的基础。
