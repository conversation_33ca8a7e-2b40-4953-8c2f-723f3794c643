# 第十四章：资源管理与工作负载隔离

## 引言

资源管理与工作负载隔离是多租户数据库系统的核心功能，确保不同用户和应用之间的资源公平分配和性能隔离。StarRocks实现了完整的资源管理体系，包括内存管理、CPU调度、IO控制、资源组隔离等技术，为企业级应用提供了可靠的资源保障。本章将深入分析StarRocks的资源管理与工作负载隔离机制。

## 14.1 资源管理架构

### 14.1.1 资源管理层次

StarRocks的资源管理采用分层架构：

```
┌─────────────────────────────────────────────────────────┐
│                    用户层资源控制                        │
├─────────────────────────────────────────────────────────┤
│                    查询层资源管理                        │
├─────────────────────────────────────────────────────────┤
│                    算子层资源分配                        │
├─────────────────────────────────────────────────────────┤
│                    系统层资源监控                        │
├─────────────────────────────────────────────────────────┤
│                    硬件层资源抽象                        │
└─────────────────────────────────────────────────────────┘
```

### 14.1.2 核心组件分析

基于`ResourceGroup.java`的源码分析：

```java
public class ResourceGroup {
    private final String name;
    private final ResourceGroupType type;
    private final Map<String, String> properties;
    
    // 资源限制
    private volatile int cpuCoreLimit = Integer.MAX_VALUE;
    private volatile long memoryLimit = Long.MAX_VALUE;
    private volatile int concurrencyLimit = Integer.MAX_VALUE;
    private volatile double cpuWeight = 1.0;
    
    // 资源使用统计
    private final AtomicInteger activeConcurrency = new AtomicInteger(0);
    private final AtomicLong memoryUsage = new AtomicLong(0);
    private final AtomicInteger cpuUsage = new AtomicInteger(0);
    
    // 队列管理
    private final BlockingQueue<QueryExecution> queryQueue;
    private final AtomicInteger queuedQueries = new AtomicInteger(0);
    
    public ResourceGroup(String name, ResourceGroupType type, Map<String, String> properties) {
        this.name = name;
        this.type = type;
        this.properties = new HashMap<>(properties);
        
        // 初始化资源限制
        initializeResourceLimits();
        
        // 初始化查询队列
        int maxQueueSize = Integer.parseInt(
            properties.getOrDefault("max_queue_size", "1000"));
        this.queryQueue = new LinkedBlockingQueue<>(maxQueueSize);
    }
    
    public boolean tryAcquireResources(QueryExecution query) {
        // 1. 检查并发限制
        if (activeConcurrency.get() >= concurrencyLimit) {
            return false;
        }
        
        // 2. 检查内存限制
        long estimatedMemory = query.getEstimatedMemoryUsage();
        if (memoryUsage.get() + estimatedMemory > memoryLimit) {
            return false;
        }
        
        // 3. 尝试获取资源
        if (activeConcurrency.compareAndSet(
                activeConcurrency.get(), activeConcurrency.get() + 1)) {
            
            memoryUsage.addAndGet(estimatedMemory);
            query.setAllocatedMemory(estimatedMemory);
            
            return true;
        }
        
        return false;
    }
    
    public void releaseResources(QueryExecution query) {
        activeConcurrency.decrementAndGet();
        memoryUsage.addAndGet(-query.getAllocatedMemory());
        
        // 尝试调度队列中的查询
        scheduleQueuedQueries();
    }
    
    public boolean enqueueQuery(QueryExecution query) {
        if (queuedQueries.get() >= getMaxQueueSize()) {
            return false; // 队列已满
        }
        
        boolean added = queryQueue.offer(query);
        if (added) {
            queuedQueries.incrementAndGet();
            query.setQueuedTime(System.currentTimeMillis());
        }
        
        return added;
    }
    
    private void scheduleQueuedQueries() {
        while (!queryQueue.isEmpty()) {
            QueryExecution query = queryQueue.peek();
            
            if (tryAcquireResources(query)) {
                queryQueue.poll();
                queuedQueries.decrementAndGet();
                
                // 提交查询执行
                query.execute();
            } else {
                break; // 资源不足，停止调度
            }
        }
    }
    
    private void initializeResourceLimits() {
        // 从属性中读取资源限制配置
        if (properties.containsKey("cpu_core_limit")) {
            cpuCoreLimit = Integer.parseInt(properties.get("cpu_core_limit"));
        }
        
        if (properties.containsKey("mem_limit")) {
            memoryLimit = parseMemorySize(properties.get("mem_limit"));
        }
        
        if (properties.containsKey("concurrency_limit")) {
            concurrencyLimit = Integer.parseInt(properties.get("concurrency_limit"));
        }
        
        if (properties.containsKey("cpu_weight")) {
            cpuWeight = Double.parseDouble(properties.get("cpu_weight"));
        }
    }
}

public enum ResourceGroupType {
    NORMAL,     // 普通资源组
    REALTIME,   // 实时资源组
    BATCH,      // 批处理资源组
    SYSTEM      // 系统资源组
}
```

这个资源组设计体现了现代资源管理的核心理念：
- **多维度限制**: CPU、内存、并发等多种资源的综合管理
- **动态调度**: 基于资源可用性的动态查询调度
- **队列管理**: 资源不足时的查询排队机制
- **统计监控**: 实时的资源使用统计和监控

## 14.2 内存管理机制

### 14.2.1 分层内存管理

基于`MemTracker.h`的内存管理实现：

```cpp
class MemTracker {
private:
    std::string _label;
    int64_t _limit;
    std::atomic<int64_t> _consumption{0};
    std::atomic<int64_t> _peak_consumption{0};
    
    // 父子关系
    std::shared_ptr<MemTracker> _parent;
    std::vector<std::weak_ptr<MemTracker>> _children;
    mutable std::mutex _children_mutex;
    
    // 内存回收回调
    std::vector<std::function<void()>> _gc_callbacks;
    
public:
    MemTracker(const std::string& label, int64_t limit, 
               std::shared_ptr<MemTracker> parent = nullptr) 
        : _label(label), _limit(limit), _parent(parent) {
        
        if (_parent) {
            _parent->add_child(shared_from_this());
        }
    }
    
    bool try_consume(int64_t bytes) {
        // 1. 检查当前Tracker限制
        int64_t new_consumption = _consumption.fetch_add(bytes) + bytes;
        
        if (_limit > 0 && new_consumption > _limit) {
            // 超出限制，回滚消费
            _consumption.fetch_sub(bytes);
            return false;
        }
        
        // 2. 更新峰值消费
        update_peak_consumption(new_consumption);
        
        // 3. 递归检查父Tracker
        if (_parent && !_parent->try_consume(bytes)) {
            // 父Tracker限制，回滚消费
            _consumption.fetch_sub(bytes);
            return false;
        }
        
        return true;
    }
    
    void consume(int64_t bytes) {
        if (!try_consume(bytes)) {
            // 内存不足，尝试垃圾回收
            trigger_gc();
            
            if (!try_consume(bytes)) {
                // 仍然不足，抛出异常
                throw std::runtime_error("Memory limit exceeded: " + _label);
            }
        }
    }
    
    void release(int64_t bytes) {
        _consumption.fetch_sub(bytes);
        
        if (_parent) {
            _parent->release(bytes);
        }
    }
    
    void add_gc_callback(std::function<void()> callback) {
        _gc_callbacks.push_back(callback);
    }
    
private:
    void trigger_gc() {
        // 1. 执行本地垃圾回收回调
        for (auto& callback : _gc_callbacks) {
            try {
                callback();
            } catch (const std::exception& e) {
                LOG(WARNING) << "GC callback failed: " << e.what();
            }
        }
        
        // 2. 触发子Tracker垃圾回收
        std::lock_guard<std::mutex> lock(_children_mutex);
        for (auto& weak_child : _children) {
            if (auto child = weak_child.lock()) {
                child->trigger_gc();
            }
        }
    }
    
    void update_peak_consumption(int64_t consumption) {
        int64_t peak = _peak_consumption.load();
        while (consumption > peak && 
               !_peak_consumption.compare_exchange_weak(peak, consumption)) {
            // CAS循环更新峰值
        }
    }
    
    void add_child(std::shared_ptr<MemTracker> child) {
        std::lock_guard<std::mutex> lock(_children_mutex);
        _children.push_back(child);
    }
};

// 查询级内存管理
class QueryMemTracker {
private:
    std::shared_ptr<MemTracker> _query_tracker;
    std::map<int32_t, std::shared_ptr<MemTracker>> _fragment_trackers;
    std::map<int32_t, std::shared_ptr<MemTracker>> _operator_trackers;
    
public:
    QueryMemTracker(const std::string& query_id, int64_t query_mem_limit) {
        // 创建查询级Tracker
        _query_tracker = std::make_shared<MemTracker>(
            "Query-" + query_id, query_mem_limit);
    }
    
    std::shared_ptr<MemTracker> create_fragment_tracker(int32_t fragment_id, 
                                                       int64_t fragment_mem_limit) {
        auto fragment_tracker = std::make_shared<MemTracker>(
            "Fragment-" + std::to_string(fragment_id), 
            fragment_mem_limit, 
            _query_tracker);
        
        _fragment_trackers[fragment_id] = fragment_tracker;
        return fragment_tracker;
    }
    
    std::shared_ptr<MemTracker> create_operator_tracker(int32_t operator_id,
                                                       int32_t fragment_id,
                                                       int64_t operator_mem_limit) {
        auto fragment_tracker = _fragment_trackers[fragment_id];
        auto operator_tracker = std::make_shared<MemTracker>(
            "Operator-" + std::to_string(operator_id),
            operator_mem_limit,
            fragment_tracker);
        
        _operator_trackers[operator_id] = operator_tracker;
        return operator_tracker;
    }
    
    MemoryUsageReport generate_report() const {
        MemoryUsageReport report;
        
        // 查询级统计
        report.query_consumption = _query_tracker->consumption();
        report.query_peak_consumption = _query_tracker->peak_consumption();
        report.query_limit = _query_tracker->limit();
        
        // Fragment级统计
        for (const auto& [fragment_id, tracker] : _fragment_trackers) {
            FragmentMemoryUsage fragment_usage;
            fragment_usage.fragment_id = fragment_id;
            fragment_usage.consumption = tracker->consumption();
            fragment_usage.peak_consumption = tracker->peak_consumption();
            fragment_usage.limit = tracker->limit();
            
            report.fragment_usages.push_back(fragment_usage);
        }
        
        // Operator级统计
        for (const auto& [operator_id, tracker] : _operator_trackers) {
            OperatorMemoryUsage operator_usage;
            operator_usage.operator_id = operator_id;
            operator_usage.consumption = tracker->consumption();
            operator_usage.peak_consumption = tracker->peak_consumption();
            operator_usage.limit = tracker->limit();
            
            report.operator_usages.push_back(operator_usage);
        }
        
        return report;
    }
};
```

### 14.2.2 内存池管理

高效的内存池实现：

```cpp
class MemoryPool {
private:
    struct MemoryChunk {
        void* data;
        size_t size;
        bool is_free;
        MemoryChunk* next;
    };
    
    std::vector<MemoryChunk*> _chunks;
    std::map<size_t, std::vector<MemoryChunk*>> _free_chunks; // 按大小分组
    std::mutex _mutex;
    
    std::shared_ptr<MemTracker> _mem_tracker;
    size_t _total_allocated = 0;
    size_t _total_reserved = 0;
    
    // 内存池配置
    static constexpr size_t MIN_CHUNK_SIZE = 1024;        // 1KB
    static constexpr size_t MAX_CHUNK_SIZE = 64 * 1024 * 1024; // 64MB
    static constexpr size_t CHUNK_SIZE_ALIGNMENT = 8;
    
public:
    MemoryPool(std::shared_ptr<MemTracker> mem_tracker) 
        : _mem_tracker(mem_tracker) {}
    
    ~MemoryPool() {
        clear();
    }
    
    void* allocate(size_t size) {
        size = align_size(size);
        
        std::lock_guard<std::mutex> lock(_mutex);
        
        // 1. 尝试从空闲块中分配
        MemoryChunk* chunk = find_free_chunk(size);
        
        if (chunk) {
            chunk->is_free = false;
            return chunk->data;
        }
        
        // 2. 分配新的内存块
        return allocate_new_chunk(size);
    }
    
    void deallocate(void* ptr) {
        if (!ptr) return;
        
        std::lock_guard<std::mutex> lock(_mutex);
        
        // 查找对应的内存块
        MemoryChunk* chunk = find_chunk(ptr);
        if (chunk) {
            chunk->is_free = true;
            
            // 添加到空闲列表
            _free_chunks[chunk->size].push_back(chunk);
            
            // 尝试合并相邻的空闲块
            try_merge_chunks(chunk);
        }
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(_mutex);
        
        for (MemoryChunk* chunk : _chunks) {
            std::free(chunk->data);
            delete chunk;
        }
        
        _chunks.clear();
        _free_chunks.clear();
        
        // 释放内存跟踪
        _mem_tracker->release(_total_allocated);
        _total_allocated = 0;
        _total_reserved = 0;
    }
    
private:
    size_t align_size(size_t size) {
        return (size + CHUNK_SIZE_ALIGNMENT - 1) & ~(CHUNK_SIZE_ALIGNMENT - 1);
    }
    
    MemoryChunk* find_free_chunk(size_t size) {
        // 查找最适合的空闲块
        auto it = _free_chunks.lower_bound(size);
        
        while (it != _free_chunks.end()) {
            if (!it->second.empty()) {
                MemoryChunk* chunk = it->second.back();
                it->second.pop_back();
                
                // 如果块太大，考虑分割
                if (chunk->size > size * 2) {
                    split_chunk(chunk, size);
                }
                
                return chunk;
            }
            ++it;
        }
        
        return nullptr;
    }
    
    void* allocate_new_chunk(size_t size) {
        // 计算实际分配大小（考虑内存对齐和最小块大小）
        size_t actual_size = std::max(size, MIN_CHUNK_SIZE);
        actual_size = std::min(actual_size, MAX_CHUNK_SIZE);
        
        // 检查内存限制
        if (!_mem_tracker->try_consume(actual_size)) {
            throw std::bad_alloc();
        }
        
        // 分配内存
        void* data = std::aligned_alloc(CHUNK_SIZE_ALIGNMENT, actual_size);
        if (!data) {
            _mem_tracker->release(actual_size);
            throw std::bad_alloc();
        }
        
        // 创建内存块描述符
        MemoryChunk* chunk = new MemoryChunk{
            .data = data,
            .size = actual_size,
            .is_free = false,
            .next = nullptr
        };
        
        _chunks.push_back(chunk);
        _total_allocated += actual_size;
        
        return data;
    }
    
    void split_chunk(MemoryChunk* chunk, size_t size) {
        if (chunk->size <= size + MIN_CHUNK_SIZE) {
            return; // 不值得分割
        }
        
        // 创建新的空闲块
        MemoryChunk* new_chunk = new MemoryChunk{
            .data = static_cast<char*>(chunk->data) + size,
            .size = chunk->size - size,
            .is_free = true,
            .next = chunk->next
        };
        
        // 更新原块
        chunk->size = size;
        chunk->next = new_chunk;
        
        // 添加新块到管理列表
        _chunks.push_back(new_chunk);
        _free_chunks[new_chunk->size].push_back(new_chunk);
    }
};
```

## 14.3 CPU资源调度

### 14.3.1 查询优先级调度

基于优先级的查询调度机制：

```java
public class QueryScheduler {
    private final Map<Priority, BlockingQueue<QueryExecution>> priorityQueues;
    private final ScheduledExecutorService schedulerExecutor;
    private final AtomicInteger activeCpuSlots = new AtomicInteger(0);
    
    // CPU资源配置
    private final int maxCpuSlots;
    private final Map<Priority, Double> priorityWeights;
    
    public QueryScheduler() {
        this.maxCpuSlots = Runtime.getRuntime().availableProcessors();
        
        // 初始化优先级队列
        this.priorityQueues = new EnumMap<>(Priority.class);
        for (Priority priority : Priority.values()) {
            priorityQueues.put(priority, new LinkedBlockingQueue<>());
        }
        
        // 初始化优先级权重
        this.priorityWeights = Map.of(
            Priority.HIGH, 4.0,
            Priority.NORMAL, 2.0,
            Priority.LOW, 1.0
        );
        
        // 启动调度器
        this.schedulerExecutor = Executors.newSingleThreadScheduledExecutor();
        this.schedulerExecutor.scheduleAtFixedRate(this::scheduleQueries, 
                                                  0, 100, TimeUnit.MILLISECONDS);
    }
    
    public boolean submitQuery(QueryExecution query) {
        Priority priority = determinePriority(query);
        
        // 尝试立即执行
        if (tryExecuteImmediately(query)) {
            return true;
        }
        
        // 加入优先级队列
        return priorityQueues.get(priority).offer(query);
    }
    
    private boolean tryExecuteImmediately(QueryExecution query) {
        int requiredSlots = calculateRequiredCpuSlots(query);
        
        if (activeCpuSlots.get() + requiredSlots <= maxCpuSlots) {
            if (activeCpuSlots.compareAndSet(activeCpuSlots.get(), 
                                           activeCpuSlots.get() + requiredSlots)) {
                executeQuery(query, requiredSlots);
                return true;
            }
        }
        
        return false;
    }
    
    private void scheduleQueries() {
        while (activeCpuSlots.get() < maxCpuSlots) {
            QueryExecution nextQuery = selectNextQuery();
            
            if (nextQuery == null) {
                break; // 没有待执行的查询
            }
            
            int requiredSlots = calculateRequiredCpuSlots(nextQuery);
            
            if (activeCpuSlots.get() + requiredSlots <= maxCpuSlots) {
                if (activeCpuSlots.compareAndSet(activeCpuSlots.get(), 
                                               activeCpuSlots.get() + requiredSlots)) {
                    executeQuery(nextQuery, requiredSlots);
                } else {
                    // CAS失败，重新入队
                    Priority priority = determinePriority(nextQuery);
                    priorityQueues.get(priority).offer(nextQuery);
                }
            } else {
                // 资源不足，重新入队
                Priority priority = determinePriority(nextQuery);
                priorityQueues.get(priority).offer(nextQuery);
                break;
            }
        }
    }
    
    private QueryExecution selectNextQuery() {
        // 基于权重的优先级调度
        double totalWeight = priorityWeights.values().stream()
                                           .mapToDouble(Double::doubleValue)
                                           .sum();
        
        double random = Math.random() * totalWeight;
        double currentWeight = 0;
        
        for (Priority priority : Priority.values()) {
            currentWeight += priorityWeights.get(priority);
            
            if (random <= currentWeight) {
                BlockingQueue<QueryExecution> queue = priorityQueues.get(priority);
                QueryExecution query = queue.poll();
                
                if (query != null) {
                    return query;
                }
            }
        }
        
        // 如果高优先级队列为空，从其他队列中选择
        for (Priority priority : Priority.values()) {
            QueryExecution query = priorityQueues.get(priority).poll();
            if (query != null) {
                return query;
            }
        }
        
        return null;
    }
    
    private Priority determinePriority(QueryExecution query) {
        // 基于查询特征确定优先级
        if (query.isRealTimeQuery()) {
            return Priority.HIGH;
        } else if (query.getEstimatedExecutionTime() < 10000) { // 10秒以内
            return Priority.NORMAL;
        } else {
            return Priority.LOW;
        }
    }
    
    private int calculateRequiredCpuSlots(QueryExecution query) {
        // 基于查询复杂度计算所需CPU槽位
        int baseSlots = 1;
        
        // 考虑并行度
        int parallelism = query.getParallelism();
        baseSlots = Math.max(baseSlots, parallelism / 4);
        
        // 考虑查询类型
        if (query.isComplexQuery()) {
            baseSlots *= 2;
        }
        
        return Math.min(baseSlots, maxCpuSlots / 2); // 单个查询最多占用一半CPU
    }
    
    public void onQueryCompleted(QueryExecution query, int usedSlots) {
        activeCpuSlots.addAndGet(-usedSlots);
        
        // 触发新一轮调度
        schedulerExecutor.execute(this::scheduleQueries);
    }
}

public enum Priority {
    HIGH(3),
    NORMAL(2),
    LOW(1);
    
    private final int level;
    
    Priority(int level) {
        this.level = level;
    }
    
    public int getLevel() {
        return level;
    }
}
```

### 14.3.2 动态负载均衡

动态的负载均衡机制：

```java
public class LoadBalancer {
    private final Map<Backend, LoadMetrics> backendLoads = new ConcurrentHashMap<>();
    private final ScheduledExecutorService loadMonitor = 
        Executors.newScheduledThreadPool(2);
    
    public void startLoadMonitoring() {
        // 定期收集负载信息
        loadMonitor.scheduleAtFixedRate(this::collectLoadMetrics, 
                                       0, 5, TimeUnit.SECONDS);
        
        // 定期执行负载均衡
        loadMonitor.scheduleAtFixedRate(this::rebalanceLoad, 
                                       10, 30, TimeUnit.SECONDS);
    }
    
    private void collectLoadMetrics() {
        for (Backend backend : SystemInfoService.getCurrentSystemInfo().getBackends()) {
            if (backend.isAlive()) {
                try {
                    LoadMetrics metrics = collectBackendLoad(backend);
                    backendLoads.put(backend, metrics);
                } catch (Exception e) {
                    LOG.warn("Failed to collect load metrics for backend " + backend.getId(), e);
                }
            }
        }
    }
    
    private LoadMetrics collectBackendLoad(Backend backend) throws Exception {
        // 发送负载查询请求
        LoadQueryRequest request = new LoadQueryRequest();
        LoadQueryResponse response = BackendServiceClient.getInstance()
            .queryLoad(backend.getAddress(), request);
        
        return new LoadMetrics(
            response.getCpuUsagePercent(),
            response.getMemoryUsagePercent(),
            response.getDiskIoRate(),
            response.getNetworkIoRate(),
            response.getActiveQueryCount(),
            response.getQueuedQueryCount()
        );
    }
    
    private void rebalanceLoad() {
        // 1. 识别过载的BE节点
        List<Backend> overloadedBackends = identifyOverloadedBackends();
        
        // 2. 识别空闲的BE节点
        List<Backend> underloadedBackends = identifyUnderloadedBackends();
        
        if (overloadedBackends.isEmpty() || underloadedBackends.isEmpty()) {
            return; // 无需重新平衡
        }
        
        // 3. 执行负载迁移
        for (Backend overloaded : overloadedBackends) {
            Backend target = selectTargetBackend(underloadedBackends);
            if (target != null) {
                migrateLoad(overloaded, target);
            }
        }
    }
    
    private List<Backend> identifyOverloadedBackends() {
        return backendLoads.entrySet().stream()
            .filter(entry -> isOverloaded(entry.getValue()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    }
    
    private boolean isOverloaded(LoadMetrics metrics) {
        return metrics.getCpuUsagePercent() > 80 ||
               metrics.getMemoryUsagePercent() > 85 ||
               metrics.getActiveQueryCount() > Config.max_queries_per_backend;
    }
    
    private void migrateLoad(Backend source, Backend target) {
        try {
            // 1. 选择要迁移的查询
            List<QueryExecution> candidateQueries = selectMigrationCandidates(source);
            
            // 2. 执行查询迁移
            for (QueryExecution query : candidateQueries) {
                if (canMigrateQuery(query)) {
                    migrateQuery(query, source, target);
                }
            }
            
        } catch (Exception e) {
            LOG.error("Failed to migrate load from " + source.getId() + 
                     " to " + target.getId(), e);
        }
    }
    
    private boolean canMigrateQuery(QueryExecution query) {
        // 检查查询是否可以迁移
        return !query.hasStartedExecution() && 
               query.getState() == QueryState.QUEUED;
    }
    
    private void migrateQuery(QueryExecution query, Backend source, Backend target) {
        // 1. 从源BE移除查询
        source.removeQueuedQuery(query);
        
        // 2. 添加到目标BE
        target.addQueuedQuery(query);
        
        // 3. 更新查询的执行节点
        query.updateExecutionBackend(target);
        
        LOG.info("Migrated query {} from backend {} to backend {}", 
                query.getQueryId(), source.getId(), target.getId());
    }
}

public class LoadMetrics {
    private final double cpuUsagePercent;
    private final double memoryUsagePercent;
    private final long diskIoRate;
    private final long networkIoRate;
    private final int activeQueryCount;
    private final int queuedQueryCount;
    
    public LoadMetrics(double cpuUsagePercent, double memoryUsagePercent,
                      long diskIoRate, long networkIoRate,
                      int activeQueryCount, int queuedQueryCount) {
        this.cpuUsagePercent = cpuUsagePercent;
        this.memoryUsagePercent = memoryUsagePercent;
        this.diskIoRate = diskIoRate;
        this.networkIoRate = networkIoRate;
        this.activeQueryCount = activeQueryCount;
        this.queuedQueryCount = queuedQueryCount;
    }
    
    public double getLoadScore() {
        // 计算综合负载分数
        double cpuScore = cpuUsagePercent / 100.0;
        double memoryScore = memoryUsagePercent / 100.0;
        double queryScore = (double) activeQueryCount / Config.max_queries_per_backend;
        
        return (cpuScore + memoryScore + queryScore) / 3.0;
    }
}
```

## 小结

StarRocks的资源管理与工作负载隔离系统实现了企业级的多租户资源管理能力，通过资源组隔离、分层内存管理、CPU调度优化和动态负载均衡等技术，确保了不同用户和应用之间的资源公平分配和性能隔离。其设计特点包括：

1. **多维度资源管理**: CPU、内存、IO、并发等全方位资源控制
2. **分层资源隔离**: 从用户层到算子层的多级资源隔离
3. **智能调度策略**: 基于优先级和负载的智能查询调度
4. **动态负载均衡**: 实时的负载监控和自动负载迁移
5. **精细化控制**: 支持灵活的资源配置和策略定制

在下一章中，我们将深入分析StarRocks的设计思想与未来展望。
