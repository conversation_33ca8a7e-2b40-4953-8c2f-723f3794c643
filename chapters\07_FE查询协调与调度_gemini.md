# 第七章：FE查询协调与调度机制

## 1. 引言

当分布式物理计划（Fragment DAG）生成之后，FE的下一个核心职责就是将这个执行蓝图付诸实施。这需要一个强大的协调者（Coordinator）来负责将计划的各个部分（Fragments）精确地分发到合适的BE节点上，启动并监控它们的执行，处理可能出现的各种异常，并最终将结果汇集起来返回给客户端。本章将深入StarRocks的查询执行（Query Execution, QE）模块，重点剖析`StmtExecutor`和`DefaultCoordinator`如何协同工作，完成从接收请求到返回结果的整个端到端协调流程。

## 2. 查询协调的核心任务

查询协调与调度是一个复杂的分布式过程，其核心任务包括：

1.  **执行入口**：提供一个统一的入口点，接收客户端的查询请求，并串联起SQL解析、分析、优化、规划的全过程。
2.  **Fragment调度**：为DAG中的每个Fragment选择一组BE节点来执行。这个决策需要考虑数据局部性（Data Locality）、节点负载、副本状态等多种因素。
3.  **任务下发**：将Fragment的执行计划（序列化为Thrift结构）连同其他运行时参数，通过RPC发送给选定的BE节点。
4.  **状态监控**：跟踪每个Fragment实例的执行状态（如运行中、已完成、失败）。
5.  **容错处理**：当某个BE节点执行失败时，能够感知到错误，取消整个查询，并向客户端报告错误。在某些情况下，可能会尝试进行重试。
6.  **结果汇聚**：从根Fragment接收最终的查询结果，并将其通过MySQL协议流式地返回给客户端。

## 3. 源码分析：查询执行的指挥中心

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/qe/StmtExecutor.java`
*   `fe/fe-core/src/main/java/com/starrocks/qe/DefaultCoordinator.java`
*   `fe/fe-core/src/main/java/com/starrocks/qe/ConnectContext.java`
*   `fe/fe-core/src/main/java/com/starrocks/thrift/TExecPlanFragmentParams.java`

### 3.1. `StmtExecutor`：查询的生命周期管理者

`StmtExecutor`是处理单个SQL语句的入口和总指挥。当一个MySQL客户端连接（由`ConnectContext`代表）发来一个查询请求时，`StmtExecutor`的`execute()`方法会被调用。

`execute()`方法的逻辑清晰地展示了FE处理查询的完整流程：
```java
// StmtExecutor.java - execute() (Simplified)
public void execute() throws Exception {
    // 1. Parse SQL to AST
    StatementBase statement = SqlParser.parse(originStmt, ...);

    // 2. Analyze AST (Semantic Analysis)
    Analyzer.analyze(statement, connectContext);

    // 3. Logical and Physical Optimization (CBO/RBO)
    PhysicalPlan physicalPlan = new Optimizer().optimize(statement, ...);

    // 4. Create Distributed Plan (Fragments)
    List<PlanFragment> fragments = new StatementPlanner().createPlanFragments(physicalPlan, ...);

    // 5. Create and Execute Coordinator
    Coordinator coord = new DefaultCoordinator(queryId, connectContext, fragments);
    coord.exec();

    // 6. Wait for completion and get results
    RowBatch batch = coord.getNext();
    while (batch != null && !coord.isDone()) {
        // Send batch to client via MySQL protocol
        ...
        batch = coord.getNext();
    }
}
```
可以看到，`StmtExecutor`负责编排整个“上半场”（从解析到规划），然后将接力棒交给`Coordinator`来完成“下半场”（分布式执行）。

### 3.2. `DefaultCoordinator`：分布式执行的调度大师

`DefaultCoordinator`是查询执行的核心。它的`exec()`方法启动了整个分布式执行过程。

#### 3.2.1. 计算Fragment执行位置（`computeFragmentExecParams`）

在下发任务前，`DefaultCoordinator`需要为每个Fragment的每个实例（一个Fragment会在多个BE上并行执行，每个执行实例称为Fragment Instance）确定执行位置。这个过程的核心是**数据局部性**。

*   对于叶子Fragment（通常是`OlapScanNode`），调度器会查看Scan的Tablet位于哪些BE上。它会优先选择持有Tablet副本的、并且负载较低的BE来执行扫描任务，从而避免数据的远程读取。
*   对于中间Fragment（如Join、Aggregate），其执行位置通常取决于其子Fragment的数据分发方式。例如，如果子Fragment的数据是广播的，那么中间Fragment可以在任意BE上执行；如果是哈希重分布的，那么它会在一组固定的BE上执行，以接收哈希后的数据。

#### 3.2.2. 任务下发（`sendFragment`）

确定了执行位置后，`DefaultCoordinator`会将Fragment计划和相关参数打包成`TExecPlanFragmentParams` Thrift结构。然后，它会通过RPC（`BackendServiceClient`）异步地调用BE上的`exec_plan_fragment`接口，将任务下发出去。

这个过程是**自底向上、按Fragment层级**进行的。调度器会先启动叶子Fragment，然后是它们的父Fragment，依此类推，直到根Fragment。这样做可以保证当一个Fragment启动时，它的数据提供者（子Fragment）已经准备就绪。

#### 3.2.3. 状态更新与结果获取

BE在执行过程中，会定期通过心跳向FE汇报Fragment实例的状态。`DefaultCoordinator`维护着每个实例的状态机。

当根Fragment执行完成后，`DefaultCoordinator`就可以通过`getResultReceiver()`来获取最终结果。结果是以`RowBatch`的形式分批次从BE流式传输回FE的，FE再将其流式地发送给客户端，避免了在FE端缓存所有结果集，支持了大规模结果集的返回。

## 4. 容错机制

`DefaultCoordinator`也扮演着容错中心的角色。
*   **超时检测**：它会监控BE的RPC调用和执行是否超时。
*   **错误报告**：如果任何一个BE实例执行失败，BE会通过RPC将错误信息报告给Coordinator。
*   **查询取消**：一旦收到任何错误，`DefaultCoordinator`会立即向所有已下发的Fragment实例发送取消请求，以尽快释放集群资源。然后，它会将详细的错误信息包装并返回给客户端。

## 5. 总结

FE的查询协调与调度机制是StarRocks分布式执行能力的核心。`StmtExecutor`作为前端的总控制器，管理着查询从文本到分布式计划的完整生命周期。而`DefaultCoordinator`则是分布式执行的总调度师，它通过精巧的调度策略（特别是利用数据局部性）、自底向上的任务下发、实时的状态监控和可靠的容错处理，确保了复杂的查询计划能够在MPP集群上被高效、稳定地执行。
