# 第四章：查询优化器核心原理与实现

## 1. 引言

对于一个给定的SQL查询，通常存在成百上千种等价的执行方式。例如，对于多表Join，不同的Join顺序、不同的Join算法（Hash Join, Nested Loop Join）会导致截然不同的执行性能，其差距可能是几秒钟和几小时的区别。查询优化器（Query Optimizer）的职责，就是在海量的执行可能性中，利用数据库的统计信息，选择一个成本最低、效率最高的执行计划。可以说，查询优化器是现代数据库的“大脑”，是决定其性能上限的核心组件。本章将深入StarRocks的查询优化器，重点剖析其采用的Cascades/Volcano优化框架，以及`Memo`、RBO、CBO等核心概念的实现。

## 2. 查询优化的两大流派

现代查询优化器主要分为两大流派：

1.  **基于规则的优化（Rule-Based Optimization, RBO）**：定义一系列启发式规则（Heuristics），例如“总是先做选择（Filter）操作，再做连接（Join）操作”来减少中间结果集的大小。RBO不关心表的实际数据分布，只根据规则对逻辑计划进行等价变换。它实现简单，但无法保证找到最优计划。
2.  **基于成本的优化（Cost-Based Optimization, CBO）**：CBO的核心思想是**量化成本**。它会利用存储在元数据中的统计信息（如表的行数、列的基数、直方图等），去估算每个可能执行计划的成本（通常是I/O和CPU的加权），然后选择成本最低的那个计划。CBO更加智能，更有可能找到最优计划，但实现也更复杂。

StarRocks的优化器融合了RBO和CBO，形成一个统一的、基于Cascades/Volcano模型的先进优化框架。

## 3. 源码分析：Cascades优化框架

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Optimizer.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Memo.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Group.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/GroupExpression.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/Rule.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/task/OptimizerTask.java`

### 3.1. `Memo`：核心数据结构

Cascades优化器的核心数据结构是`Memo`。`Memo`可以看作是一个紧凑的、用于存储所有等价逻辑计划的森林。它由两部分组成：

*   **Group**: 一个`Group`代表一个逻辑上等价的子计划。例如，`A join B`和`B join A`是逻辑等价的，它们属于同一个`Group`。
*   **GroupExpression**: 一个`GroupExpression`代表一个具体的算子和它的输入。输入不是直接的`GroupExpression`，而是指向其子`Group`的引用。例如，`HashJoin(Scan A, Scan B)`就是一个`GroupExpression`，它的输入是`Scan A`所在的`Group`和`Scan B`所在的`Group`。

一个`Group`中可以包含多个`GroupExpression`，代表了实现这个逻辑等价计划的多种不同方式。

```
// Memo Structure Visualization

Group 1: { (A join B) join C }
  - GroupExpression 1: HashJoin(ref to Group 2, ref to Group 4)
  - GroupExpression 2: HashJoin(ref to Group 3, ref to Group 5)

Group 2: { A join B }
  - GroupExpression 3: HashJoin(ref to Group 5, ref to Group 6)

... and so on
```

`Memo.java`就是这个数据结构的实现，它管理着所有的`Group`和`GroupExpression`。

### 3.2. `Rule`：变换的驱动力

优化过程由一系列的`Rule`驱动。StarRocks中的`Rule`分为两类：

*   **Transformation Rule (转换规则)**：实现逻辑上的等价变换，输入是一个`GroupExpression`，输出是一个新的、等价的`GroupExpression`。例如，`JoinCommutativeRule` (Join交换律), `PredicatePushDownRule` (谓词下推)。这些规则主要在RBO阶段起作用。
*   **Implementation Rule (实现规则)**：将一个逻辑算子转换为一个物理算子。例如，`LogicalJoinToHashJoinRule`将逻辑Join转换为物理的Hash Join。

### 3.3. `OptimizerTask`：搜索过程

优化器的工作流程是一个**自顶向下**的搜索过程，由一系列的`OptimizerTask`驱动。

1.  **初始化**: 将初始的逻辑计划装载进`Memo`。
2.  **逻辑优化 (RBO)**: 创建`OptimizeGroupTask`任务，对`Memo`中的`Group`应用所有的Transformation Rule，不断发现新的、等价的逻辑计划，并把它们加入到`Memo`中，直到没有新的计划可以产生。
3.  **物理优化 (CBO)**: 创建`DeriveStatsTask`任务，自底向上地为`Memo`中每个`Group`估算统计信息。然后创建`EnforcePropertyTask`任务，自顶向下地为每个`Group`选择成本最低的物理实现（`GroupExpression`），同时满足上层算子对数据物理属性（如排序、分布）的要求。

`Optimizer.java`是整个流程的编排者，它创建初始任务并启动优化循环。

## 4. CBO的基石：统计信息

CBO的准确性高度依赖于统计信息的质量。StarRocks会定期自动或手动收集表的统计信息，包括：
*   总行数
*   每列的基数（Distinct Value Count）
*   每列的最大/最小值
*   每列的NULL值数量
*   直方图（Histogram）：更精确地描述数据分布

这些信息存储在`Catalog`中，在CBO阶段，`StatisticsCalculator`会使用这些基础信息，并结合一系列复杂的公式和假设（如均匀分布、独立性假设），来估算过滤、连接等操作后的结果集大小和成本。

## 5. 总结

StarRocks的查询优化器是一个高度复杂的系统，它巧妙地融合了RBO和CBO。其核心是基于Cascades/Volcano模型的`Memo`数据结构和任务驱动的搜索策略。通过应用一系列的转换和实现规则，它能够在巨大的搜索空间中，探索各种逻辑和物理的执行可能性。最终，借助统计信息进行成本估算，它能够大概率地找到一个近乎最优的执行计划，这是StarRocks实现极致查询性能的关键所在。
