# 第五章：物化视图自动改写机制

## 1. 引言

在数据仓库和商业智能（BI）应用中，查询通常是复杂且重复的。许多查询都包含对大表的聚合、多表连接等耗时操作。物化视图（Materialized View, MV）是一种强大的查询加速技术，它通过预先计算并存储查询结果，使得对这些复杂查询的响应能够达到亚秒级。然而，传统物化视图需要用户在查询中显式引用，这限制了其应用范围。StarRocks的**物化视图自动改写（Automatic Rewrite with Materialized Views）**机制，则将这一技术提升到了新的高度。它能够在用户毫不知情的情况下，智能地将用户查询重写为对一个或多个物化视图的查询，从而实现透明的、数量级的查询加速。本章将深入这一高级优化技术，剖析其背后的算法和实现。

## 2. 物化视图改写的核心挑战

物化视图改写的核心挑战在于**等价性判断**：如何确定用户的查询（Query）可以由一个已存在的物化视图（MV）来回答？这通常需要满足两个条件：

1.  **数据源匹配**：Query所需要的所有数据，都能从MV中获取。这意味着Query的`FROM`子句中的表，必须是MV `FROM`子句中表的子集（或者可以通过Join推导）。
2.  **计算逻辑匹配**：Query所需要的计算（`WHERE`过滤、`GROUP BY`聚合、`SELECT`投影），都可以基于MV中已有的列和聚合结果来完成。

## 3. 源码分析：StarRocks的改写之路

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/transformation/materialization/MaterializedViewRewriter.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/transformation/materialization/MvRewriteContext.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/transformation/materialization/PredicateRewriter.java`
*   `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/transformation/materialization/AggregateRewriter.java`

StarRocks的物化视图改写是在CBO的逻辑优化阶段，作为一个特殊的`Transformation Rule`来实现的。`MaterializedViewRewriter`是整个流程的入口。

### 3.1. 改写流程概览

`MaterializedViewRewriter`的改写过程可以概括为以下几个步骤：

1.  **结构匹配（Structure Matching）**：首先，快速筛选出可能用于改写的候选MV。这一步通常基于查询和MV所涉及的基表（Base Tables）进行。如果查询用到的表和MV的表不匹配，则直接跳过。
2.  **SPJG模式匹配**：StarRocks的改写主要集中在最常见的**SPJG（Select-Project-Join-GroupBy）**类型的查询上。它会将用户查询和MV的逻辑计划都规范化为SPJG结构，然后进行详细的组件对比。
3.  **查询补偿（Query Compensation）**：这是改写的核心。如果Query和MV不完全等价，系统会尝试构建一个“补偿查询”（Compensation Query），这个补偿查询作用于MV之上，其结果与原始Query等价。
    *   **谓词重写（Predicate Rewrite）**：`PredicateRewriter`负责判断Query的`WHERE`子句是否比MV的`WHERE`子句更“严格”。如果是，那么多余的过滤条件可以应用在扫描MV之后。
    *   **聚合重写（Aggregate Rewrite）**：`AggregateRewriter`负责处理聚合函数的匹配和补偿。例如，如果Query需要`SUM(a)`，而MV中只有`SUM(a)`和`COUNT(a)`，这是可以直接满足的。但如果Query需要`AVG(a)`，而MV中只有`SUM(a)`和`COUNT(a)`，则可以将其改写为`SUM(a) / COUNT(a)`。
    *   **投影补偿（Projection Compensation）**：最后，检查Query的`SELECT`列表中的所有列，是否都能从MV的输出列或者通过补偿计算得到。
4.  **成本评估与选择**：一个查询可能会被多个MV改写。StarRocks会估算每个改写后计划的成本，并选择成本最低的那个版本，与原始查询计划一起放入`Memo`中，参与最终的成本比较。

### 3.2. `MvRewriteContext`：改写的上下文

`MvRewriteContext`是一个关键的数据结构，它在整个重写过程中保存了查询、物化视图以及它们之间列和表达式的映射关系。这为复杂的等价性判断和补偿查询构建提供了必要的信息。

### 3.3. 示例

假设我们有以下物化视图：
```sql
CREATE MATERIALIZED VIEW mv_sales_daily AS
SELECT
    sale_date,
    region,
    SUM(amount) as total_sales,
    COUNT(*) as num_orders
FROM sales
GROUP BY sale_date, region;
```

现在用户发起查询：
```sql
SELECT
    sale_date,
    SUM(amount)
FROM sales
WHERE region = 'ASIA' AND sale_date > '2023-01-01'
GROUP BY sale_date;
```

`MaterializedViewRewriter`会执行以下改写：
1.  **结构匹配**：Query和MV都基于`sales`表，匹配成功。
2.  **谓词重写**：Query的`WHERE`子句 (`region = 'ASIA' AND sale_date > '2023-01-01'`) 是MV（无`WHERE`子句）的超集。因此，可以在扫描MV之后应用这个`WHERE`子句。
3.  **聚合重写**：Query需要的`SUM(amount)`在MV中以`total_sales`的形式存在。
4.  **生成改写计划**：最终，原始查询被透明地改写为：
    ```sql
    SELECT
        sale_date,
        SUM(total_sales) -- Note: This might be further optimized
    FROM mv_sales_daily
    WHERE region = 'ASIA' AND sale_date > '2023-01-01'
    GROUP BY sale_date;
    ```
这个改写后的查询直接作用于数据量大大减小的物化视图上，性能将得到巨大提升。

## 4. 设计思想与权衡

*   **集成于优化器**：将MV改写作为CBO的一个规则，而不是一个独立的模块，使得改写后的计划可以无缝地参与后续的成本比较和优化，保证了全局最优。
*   **扩展性**：基于规则的框架使得添加对更多类型查询（如Join、Union）的改写支持变得相对容易。
*   **复杂性**：物化视图改写逻辑非常复杂，需要处理各种边界情况，是优化器中最具挑战性的部分之一。

## 5. 总结

物化视图自动改写是StarRocks查询优化器中的“黑科技”。它通过复杂的SPJG模式匹配、查询补偿和成本评估，实现了对用户查询的透明加速。理解其工作原理，不仅能帮助我们更好地利用物化视图，也能让我们对现代数据库查询优化的深度和复杂度有更深的认识。
