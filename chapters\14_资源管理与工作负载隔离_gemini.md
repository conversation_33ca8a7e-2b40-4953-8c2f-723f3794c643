# 第十四章：资源管理与工作负载隔离

## 1. 引言

在一个多租户或多业务共享的数据库集群中，不同类型的查询负载（如实时的Ad-hoc短查询、批处理的ETL长查询）会同时运行。如果没有有效的资源管理和隔离机制，一个或几个“坏查询”（消耗大量CPU和内存的查询）就可能耗尽整个集群的资源，导致其他所有查询变慢甚至失败，引发“邻居效应”。为了保证服务的稳定性和QoS（Quality of Service），现代数据库必须具备强大的资源管理和工作负载隔离能力。本章将探讨StarRocks在这方面的机制，分析其如何对内存、CPU等核心资源进行管理和隔离。

## 2. 资源管理的挑战

分布式数据库的资源管理主要面临以下挑战：

1.  **资源计量**：如何准确地追踪和统计一个查询在所有BE节点上消耗的总内存和CPU资源。
2.  **准入控制**：在查询开始执行前，如何判断集群是否有足够的资源来运行它，从而避免因资源不足导致的执行失败。
3.  **运行时控制**：在查询执行过程中，如何限制其资源使用，防止其超出预设的配额。
4.  **优先级调度**：当资源紧张时，如何优先保障高优先级任务的执行，同时兼顾低优先级任务。
5.  **多租户隔离**：如何为不同的用户或业务组划分独立的资源池，使其互不干扰。

## 3. StarRocks的内存管理

内存是数据库中最宝贵也最容易出问题的资源。StarRocks构建了一套精细的内存管理机制。

### 3.1. 内存追踪（Memory Tracker）

StarRocks的内存管理基于一个树状的`MemTracker`体系。
*   每个查询在启动时，都会创建一个顶层的`Query MemTracker`。
*   在BE上，每个Fragment实例、每个算子也都会有自己专属的`MemTracker`，它们都作为子节点挂在`Query MemTracker`之下。
*   当算子需要分配内存时（如Hash Join构建哈希表），它会通过其`MemTracker`进行申请。`MemTracker`在分配内存的同时，会检查是否超过了查询或算子的内存限制。
*   这种树状结构使得内存的使用可以被精确地追踪到具体的查询和算子，为资源限制和问题排查提供了依据。

### 3.2. 查询内存限制

StarRocks支持在多个层级上设置查询的内存限制：
*   **全局级别**：通过FE的配置项`query_mem_limit`可以设置所有查询默认的内存上限。
*   **会话级别**：用户可以通过`SET exec_mem_limit = ...`来为当前会话设置内存限制。
*   **查询级别提示（Hint）**：可以在SQL语句中使用`/*+ set_var(exec_mem_limit=...) */`来为单条查询指定内存限制。

当查询执行过程中，任何一个BE上的`Query MemTracker`发现总内存使用超过了限制，它会立即取消该查询，并释放内存，防止拖垮整个BE节点。

## 4. 工作负载隔离：资源组（Resource Group）

为了实现更灵活的工作负载管理和多租户隔离，StarRocks引入了**资源组（Resource Group）**的概念。

### 4.1. 资源组的定义

资源组允许管理员将集群的资源（主要是CPU和内存）划分成不同的池子。创建一个资源组时，可以指定：
*   `cpu_core_limit`: 该资源组在每个BE节点上可以使用的CPU核心数上限。
*   `mem_limit`: 该资源组在整个集群可以使用的内存上限的百分比。
*   `concurrency_limit`: 该资源组允许同时运行的查询数量上限。
*   `spill_mem_limit_threshold`: 当资源组内存使用超过此阈值时，新来的查询可能会被强制落盘（spill to disk），以缓解内存压力。

### 4.2. 工作负载分类与路由

创建资源组后，需要创建**分类器（Classifier）**来将不同的查询自动路由到指定的资源组。分类器的规则可以基于：
*   `user`: 用户名。
*   `role`: 角色。
*   `query_band`: 用户在会话中设置的标签。
*   `source_ip`: 客户端IP地址。

当一个查询进入系统时，FE会根据分类器的规则，判断该查询属于哪个资源组。然后，FE会在向BE下发Fragment时，带上资源组的信息。

### 4.3. BE端的资源隔离实现

*   **CPU隔离**：BE端的`PipelineDriverExecutor`会为每个资源组维护一个独立的线程池。该线程池的大小受`cpu_core_limit`的限制。一个资源组的查询所对应的`PipelineDriver`，只会被其专属的线程池中的工作线程来调度执行。这从根本上隔离了不同资源组之间的CPU竞争。
*   **内存隔离**：BE会监控每个资源组的内存使用总量。当一个资源组的查询尝试申请内存，而会导致该资源组的总内存超过`mem_limit`时，内存申请会失败，从而导致查询被取消。这实现了硬性的内存隔离。
*   **并发度控制**：FE会跟踪每个资源组当前正在运行的查询数量。如果一个新查询被分类到某个资源组，而该资源组的并发数已达到`concurrency_limit`，则该查询会进入排队等待状态，直到有查询完成并释放出名额。

## 5. 总结

有效的资源管理和工作负载隔离是保障共享数据库集群稳定运行的关键。StarRocks通过`MemTracker`实现了对查询内存的精细化追踪和限制。更进一步，通过引入**资源组**这一强大特性，StarRocks实现了从“查询级别”到“租户级别”的资源管控。管理员可以根据业务需求，将CPU和内存资源划分成独立的池，并通过分类器将不同工作负载自动路由到相应的资源池中，实现了硬性的CPU、内存和并发度隔离。这套机制有效地防止了资源滥用和“邻居效应”，保证了关键业务的服务质量（QoS），是StarRocks作为企业级数据仓库的重要能力体现。
