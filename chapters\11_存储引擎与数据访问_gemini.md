# 第十一章：存储引擎与数据访问优化

## 1. 引言

存储引擎是数据库的基石，它负责数据的持久化存储、组织和高效检索。无论上层的查询优化和执行引擎多么强大，如果无法从磁盘上快速地获取到所需数据，整个系统的性能都将无从谈起。StarRocks设计并实现了一套完全自研的、面向OLAP分析场景的列式存储引擎。本章将深入StarRocks的“地基”，剖析其存储模型的关键设计，包括Tablet划分、列式存储、索引结构、数据压缩以及IO优化等核心技术，探讨它们如何共同支撑起高效的数据访问。

## 2. 存储引擎的核心设计目标

面向OLAP场景的存储引擎，其设计目标与面向OLTP的引擎（如InnoDB）截然不同，它主要关注：

1.  **高吞吐的批量读**：分析型查询通常需要扫描表的大部分数据，因此存储引擎必须能够以极高的吞吐率读取数据。
2.  **高效的过滤能力**：利用索引或其他技术，快速跳过大量无关数据，只读取与查询相关的行，即“谓词下推”到存储层。
3.  **高压缩率**：分析型数据量巨大，高压缩率意味着更少的磁盘占用和更低的I/O开销。
4.  **快速的数据导入**：支持高并发、大批量的数据写入和更新。

## 3. 源码分析：StarRocks的存储架构

核心参考源码：
*   `be/src/storage/storage_engine.h`
*   `be/src/storage/tablet.h`
*   `be/src/storage/rowset/rowset.h`
*   `be/src/storage/segment_v2/segment.h`
*   `be/src/storage/column_reader.h`

### 3.1. 数据模型与Tablet

StarRocks的数据模型与关系型数据库类似，数据逻辑上存储在表中。在物理上，一个表会根据其分区（Partition）和分桶（Bucket）规则，被水平切分成若干个**Tablet**。

*   **Tablet**是StarRocks中最基本的数据管理单元，也是数据副本和一致性的基本单位。每个Tablet都有多个副本（通常是3个），分布在不同的BE上，通过Paxos协议（或其变种）保证写操作的一致性。
*   这种切分方式使得大表可以被分散到整个集群，实现了存储和计算能力的水平扩展。查询可以并发地在多个BE上扫描不同的Tablet。

`storage/tablet.h`中定义的`Tablet`类，是管理Tablet所有操作（如读、写、compaction）的入口。

### 3.2. Rowset与Segment

一个Tablet内部的数据，由多个**Rowset**组成。一个Rowset对应一次导入任务（如一次Broker Load或Stream Load）。这种设计使得不同批次导入的数据在物理上是隔离的，便于管理和回滚。

每个Rowset又由一个或多个**Segment**文件组成。Segment是数据在磁盘上存储的物理文件。当一个Segment的行数或大小达到阈值后，就会创建新的Segment。

### 3.3. 列式存储布局

StarRocks的核心是**列式存储**。在每个Segment文件中，数据不是按行存储，而是按列存储。同一列的数据被连续地存放在一起。

```
// Row-oriented storage
Row 1: (col_A1, col_B1, col_C1)
Row 2: (col_A2, col_B2, col_C2)
...

// Column-oriented storage
File_col_A: (col_A1, col_A2, ...)
File_col_B: (col_B1, col_B2, ...)
File_col_C: (col_C1, col_C2, ...)
```

列式存储的巨大优势：
1.  **I/O优化**：分析型查询通常只关心部分列。列存使得查询可以只读取所需的列，避免了读取无关列数据带来的I/O浪费。
2.  **高压缩率**：同一列的数据类型相同，数据特征相似，因此具有极高的压缩比。StarRocks支持LZ4, ZSTD, Snappy等多种高效的压缩算法。

### 3.4. 索引技术：加速数据过滤

为了避免全量扫描Tablet，StarRocks在存储层内置了多层级的索引结构，以实现高效的数据过滤。

1.  **稀疏索引（Sparse Index）**：StarRocks对每个表的前缀列（最多36字节）自动建立稀疏索引。该索引并不记录每一行的位置，而是每隔一批行（默认1024行）记录一个索引项，索引项内容是这一批行的起始行的前缀列值。当查询带有对前缀列的等值或范围过滤时，可以通过稀疏索引快速定位到可能包含目标数据的行批次，从而跳过大量的无关数据。
2.  **ZoneMap索引**：Segment文件会为每个列的每个Page（数据块）记录其统计信息，如最大值、最小值、是否有NULL值。这就是ZoneMap。当查询带有对非前缀列的过滤时，可以利用ZoneMap快速判断一个Page是否可能包含目标数据，如果查询条件超出了ZoneMap的范围，则整个Page都可以被跳过读取。
3.  **Bloom Filter**：对于高基数的列，可以创建Bloom Filter索引。它能够以很高的概率判断某一列值**不存在**于数据块中，从而安全地跳过读取。
4.  **Bitmap索引**：对于低基数的列（如性别、国家），可以创建Bitmap索引。它为每个不同的列值建立一个位图（Bitmap），位图的每一位对应一行数据。通过位运算可以极快地完成AND, OR, NOT等逻辑操作。

### 3.5. 数据访问：`ColumnReader`

当一个`OlapScanOperator`需要读取数据时，它会通过`Segment`对象获取对应列的`ColumnReader`。`ColumnReader`负责从磁盘读取经过压缩和编码的列数据，解压解码后，填充到`Column`对象中，形成`Chunk`，最终交给上层的计算算子。这个过程也充分利用了缓存、预取等技术来优化I/O性能。

## 4. 总结

StarRocks的存储引擎是其高性能分析能力的地基。通过Tablet实现数据的分布式切分，通过Rowset和Segment管理数据的版本和物理文件，其核心的**列式存储**布局为高效的I/O和压缩奠定了基础。更重要的是，它内置的**多层次、自动化的索引体系**（稀疏索引、ZoneMap、Bloom Filter、Bitmap）使得存储引擎自身就具备了强大的数据过滤能力，能够在数据访问的最初阶段就剔除大量无关数据，极大地减轻了上层计算引擎的负担。正是这个精心设计的存储引擎，保证了数据能够被高效地写入、存储和访问。
