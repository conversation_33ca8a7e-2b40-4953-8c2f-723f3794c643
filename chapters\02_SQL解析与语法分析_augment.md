# 第二章：SQL解析与语法分析

## 引言

SQL解析是数据库查询处理的第一步，也是最基础的环节。StarRocks采用ANTLR4作为语法解析器生成工具，构建了一套完整的SQL解析体系。本章将深入分析StarRocks的SQL解析机制，从词法分析到语法树构建，全面解析现代数据库SQL解析器的设计与实现。

## 2.1 SQL解析架构概览

### 2.1.1 解析流程架构

StarRocks的SQL解析采用经典的编译器前端设计：

```
SQL文本 → 词法分析 → 语法分析 → 语义分析 → AST
   ↓         ↓         ↓         ↓        ↓
Lexer → Token流 → Parser → Parse Tree → AstBuilder → AST
```

### 2.1.2 核心组件分析

基于`SqlParser.java`的源码分析，我们可以看到解析器的核心结构：

```java
public class SqlParser {
    private static final ParseErrorListener PARSE_ERROR_LISTENER = 
        new ParseErrorListener();
    
    public static StatementBase parse(String sql, SessionVariable sessionVariable) {
        try {
            // 1. 词法分析 - 将SQL文本转换为Token流
            StarRocksLexer lexer = new StarRocksLexer(
                CharStreams.fromString(sql));
            lexer.removeErrorListeners();
            lexer.addErrorListener(PARSE_ERROR_LISTENER);
            
            // 2. 语法分析 - 将Token流转换为Parse Tree
            CommonTokenStream tokenStream = new CommonTokenStream(lexer);
            StarRocksParser parser = new StarRocksParser(tokenStream);
            parser.removeErrorListeners();
            parser.addErrorListener(PARSE_ERROR_LISTENER);
            
            // 3. 解析SQL语句
            StarRocksParser.SqlStatementsContext sqlStatements = 
                parser.sqlStatements();
            
            // 4. AST构建 - 将Parse Tree转换为AST
            AstBuilder astBuilder = new AstBuilder(sessionVariable);
            return (StatementBase) astBuilder.visit(sqlStatements);
            
        } catch (Exception e) {
            throw new ParsingException("SQL parsing failed: " + e.getMessage());
        }
    }
}
```

这个解析流程体现了现代编译器的标准设计模式：
- **词法分析器(Lexer)**: 识别SQL关键字、标识符、字面量等
- **语法分析器(Parser)**: 根据语法规则构建解析树
- **AST构建器(AstBuilder)**: 将解析树转换为抽象语法树

## 2.2 词法分析深度解析

### 2.2.1 ANTLR词法规则

StarRocks的词法规则定义在`StarRocksLexer.g4`文件中：

```antlr
// 关键字定义
SELECT: 'SELECT' | 'select';
FROM: 'FROM' | 'from';
WHERE: 'WHERE' | 'where';
GROUP: 'GROUP' | 'group';
BY: 'BY' | 'by';
ORDER: 'ORDER' | 'order';
LIMIT: 'LIMIT' | 'limit';

// 标识符规则
IDENTIFIER: [a-zA-Z_][a-zA-Z0-9_]*;

// 数值字面量
INTEGER_VALUE: [0-9]+;
DECIMAL_VALUE: [0-9]+ '.' [0-9]*;

// 字符串字面量
STRING: '\'' (~'\'' | '\'\'')* '\'';

// 操作符
EQ: '=';
NE: '<>' | '!=';
LT: '<';
LE: '<=';
GT: '>';
GE: '>=';

// 分隔符
SEMICOLON: ';';
COMMA: ',';
DOT: '.';
LPAREN: '(';
RPAREN: ')';
```

### 2.2.2 词法分析器实现

词法分析器的核心功能是将SQL文本流转换为Token流：

```java
public class StarRocksLexer extends Lexer {
    // Token类型定义
    public static final int SELECT = 1;
    public static final int FROM = 2;
    public static final int WHERE = 3;
    // ... 更多Token类型
    
    // 词法规则匹配
    @Override
    public Token nextToken() {
        Token token = super.nextToken();
        
        // 处理特殊情况，如关键字大小写不敏感
        if (token.getType() == IDENTIFIER) {
            String text = token.getText().toUpperCase();
            Integer keywordType = keywordMap.get(text);
            if (keywordType != null) {
                token = new CommonToken(keywordType, text);
            }
        }
        
        return token;
    }
}
```

### 2.2.3 错误处理机制

词法分析阶段的错误处理：

```java
public class ParseErrorListener extends BaseErrorListener {
    @Override
    public void syntaxError(Recognizer<?, ?> recognizer,
                           Object offendingSymbol,
                           int line, int charPositionInLine,
                           String msg, RecognitionException e) {
        
        String errorMsg = String.format(
            "SQL syntax error at line %d:%d - %s",
            line, charPositionInLine, msg);
            
        throw new ParsingException(errorMsg);
    }
}
```

## 2.3 语法分析核心原理

### 2.3.1 语法规则设计

StarRocks的语法规则定义在`StarRocksParser.g4`文件中，采用递归下降的语法结构：

```antlr
// 顶层规则
sqlStatements: (sqlStatement SEMICOLON?)* EOF;

sqlStatement: 
    queryStatement
    | insertStatement  
    | updateStatement
    | deleteStatement
    | createStatement
    | dropStatement
    | alterStatement
    | showStatement
    | useStatement
    ;

// 查询语句规则
queryStatement: queryExpression;

queryExpression:
    queryTerm (UNION (ALL | DISTINCT)? queryTerm)*
    ;

queryTerm:
    queryPrimary
    | '(' queryExpression ')'
    ;

queryPrimary:
    querySpecification
    | tableSubquery
    ;

// SELECT语句规则
querySpecification:
    SELECT selectList
    fromClause?
    whereClause?
    groupByClause?
    havingClause?
    orderByClause?
    limitClause?
    ;

selectList:
    selectItem (',' selectItem)*
    ;

selectItem:
    expression (AS? identifier)?
    | '*'
    ;
```

### 2.3.2 表达式解析

表达式解析是语法分析的重点，需要处理运算符优先级：

```antlr
// 表达式规则（按优先级从低到高）
expression:
    booleanExpression
    ;

booleanExpression:
    valueExpression                                          #valueExpressionDefault
    | booleanExpression AND booleanExpression               #logicalBinary
    | booleanExpression OR booleanExpression                #logicalBinary
    | NOT booleanExpression                                 #logicalNot
    ;

valueExpression:
    primaryExpression                                       #valueExpressionDefault
    | valueExpression comparisonOperator valueExpression   #comparison
    | valueExpression IN '(' expression (',' expression)* ')' #inList
    | valueExpression BETWEEN valueExpression AND valueExpression #between
    | valueExpression IS NULL                               #nullPredicate
    | valueExpression IS NOT NULL                           #nullPredicate
    ;

primaryExpression:
    literal                                                 #literalExpression
    | identifier                                           #columnReference
    | functionCall                                         #functionCallExpression
    | '(' expression ')'                                   #parenthesizedExpression
    ;
```

### 2.3.3 函数调用解析

函数调用的语法规则：

```antlr
functionCall:
    qualifiedName '(' (expression (',' expression)*)? ')'  #regularFunction
    | COUNT '(' '*' ')'                                    #countAll
    | COUNT '(' DISTINCT expression (',' expression)* ')' #countDistinct
    | aggregateFunction                                    #aggregateFunctionCall
    ;

aggregateFunction:
    (SUM | AVG | MAX | MIN | COUNT) '(' (DISTINCT)? expression ')'
    ;
```

## 2.4 AST构建机制

### 2.4.1 AstBuilder核心实现

`AstBuilder.java`负责将ANTLR生成的Parse Tree转换为StarRocks的AST：

```java
public class AstBuilder extends StarRocksParserBaseVisitor<ParseNode> {
    private final SessionVariable sessionVariable;
    
    public AstBuilder(SessionVariable sessionVariable) {
        this.sessionVariable = sessionVariable;
    }
    
    @Override
    public ParseNode visitQuerySpecification(
            StarRocksParser.QuerySpecificationContext ctx) {
        
        // 1. 处理SELECT子句
        SelectList selectList = (SelectList) visit(ctx.selectList());
        
        // 2. 处理FROM子句
        FromClause fromClause = null;
        if (ctx.fromClause() != null) {
            fromClause = (FromClause) visit(ctx.fromClause());
        }
        
        // 3. 处理WHERE子句
        Expr whereClause = null;
        if (ctx.whereClause() != null) {
            whereClause = (Expr) visit(ctx.whereClause().expression());
        }
        
        // 4. 处理GROUP BY子句
        GroupByClause groupByClause = null;
        if (ctx.groupByClause() != null) {
            groupByClause = (GroupByClause) visit(ctx.groupByClause());
        }
        
        // 5. 处理ORDER BY子句
        OrderByClause orderByClause = null;
        if (ctx.orderByClause() != null) {
            orderByClause = (OrderByClause) visit(ctx.orderByClause());
        }
        
        // 6. 处理LIMIT子句
        LimitClause limitClause = null;
        if (ctx.limitClause() != null) {
            limitClause = (LimitClause) visit(ctx.limitClause());
        }
        
        // 7. 构建SelectStmt
        return new SelectStmt(selectList, fromClause, whereClause,
                             groupByClause, null, orderByClause, limitClause);
    }
}
```

### 2.4.2 表达式AST构建

表达式的AST构建需要处理各种表达式类型：

```java
@Override
public ParseNode visitComparison(StarRocksParser.ComparisonContext ctx) {
    Expr left = (Expr) visit(ctx.valueExpression(0));
    Expr right = (Expr) visit(ctx.valueExpression(1));
    
    String operator = ctx.comparisonOperator().getText();
    BinaryPredicate.Operator op;
    
    switch (operator) {
        case "=":
            op = BinaryPredicate.Operator.EQ;
            break;
        case "!=":
        case "<>":
            op = BinaryPredicate.Operator.NE;
            break;
        case "<":
            op = BinaryPredicate.Operator.LT;
            break;
        case "<=":
            op = BinaryPredicate.Operator.LE;
            break;
        case ">":
            op = BinaryPredicate.Operator.GT;
            break;
        case ">=":
            op = BinaryPredicate.Operator.GE;
            break;
        default:
            throw new ParsingException("Unsupported operator: " + operator);
    }
    
    return new BinaryPredicate(op, left, right);
}
```

### 2.4.3 函数调用AST构建

函数调用的AST构建：

```java
@Override
public ParseNode visitFunctionCallExpression(
        StarRocksParser.FunctionCallExpressionContext ctx) {
    
    StarRocksParser.FunctionCallContext functionCall = ctx.functionCall();
    
    // 1. 获取函数名
    String functionName = functionCall.qualifiedName().getText();
    
    // 2. 获取参数列表
    List<Expr> params = new ArrayList<>();
    if (functionCall.expression() != null) {
        for (StarRocksParser.ExpressionContext exprCtx : 
             functionCall.expression()) {
            params.add((Expr) visit(exprCtx));
        }
    }
    
    // 3. 构建FunctionCallExpr
    FunctionName fnName = new FunctionName(functionName);
    return new FunctionCallExpr(fnName, params);
}
```

## 2.5 SQL方言支持

### 2.5.1 MySQL兼容性

StarRocks支持MySQL语法兼容：

```java
public class MySQLCompatibilityHandler {
    public static void handleMySQLSyntax(ParseNode node) {
        if (node instanceof SelectStmt) {
            SelectStmt selectStmt = (SelectStmt) node;
            
            // 处理MySQL特有的LIMIT语法
            if (selectStmt.hasLimitClause()) {
                LimitClause limitClause = selectStmt.getLimitClause();
                // MySQL: LIMIT offset, count
                // StarRocks: LIMIT count OFFSET offset
                convertMySQLLimitSyntax(limitClause);
            }
        }
    }
}
```

### 2.5.2 标准SQL支持

支持SQL标准语法：

```antlr
// 窗口函数语法
windowFunction:
    functionCall OVER '(' 
    (PARTITION BY expression (',' expression)*)?
    (ORDER BY sortItem (',' sortItem)*)?
    windowFrame?
    ')'
    ;

windowFrame:
    (RANGE | ROWS) frameBound
    | (RANGE | ROWS) BETWEEN frameBound AND frameBound
    ;

frameBound:
    UNBOUNDED PRECEDING
    | UNBOUNDED FOLLOWING
    | CURRENT ROW
    | expression PRECEDING
    | expression FOLLOWING
    ;
```

### 2.5.3 扩展语法支持

StarRocks特有的扩展语法：

```antlr
// 物化视图语法
createMaterializedViewStatement:
    CREATE MATERIALIZED VIEW qualifiedName
    (PARTITION BY '(' expression (',' expression)* ')')?
    (DISTRIBUTED BY HASH '(' identifier (',' identifier)* ')' BUCKETS INTEGER_VALUE)?
    (REFRESH (ASYNC | SYNC))?
    AS queryStatement
    ;

// 外表语法
createExternalTableStatement:
    CREATE EXTERNAL TABLE qualifiedName
    '(' columnDefinition (',' columnDefinition)* ')'
    ENGINE '=' engineName
    PROPERTIES '(' property (',' property)* ')'
    ;
```

## 2.6 错误处理与恢复

### 2.6.1 语法错误检测

```java
public class SyntaxErrorHandler {
    public static void handleSyntaxError(RecognitionException e,
                                       String sql, int line, int column) {
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append("Syntax error at line ").append(line)
                .append(", column ").append(column).append(": ");
        
        // 根据错误类型提供具体的错误信息
        if (e instanceof NoViableAltException) {
            errorMsg.append("Unexpected token");
        } else if (e instanceof InputMismatchException) {
            errorMsg.append("Mismatched input");
        } else {
            errorMsg.append("Invalid syntax");
        }
        
        // 提供修复建议
        String suggestion = getSuggestion(e, sql, line, column);
        if (suggestion != null) {
            errorMsg.append(". Suggestion: ").append(suggestion);
        }
        
        throw new ParsingException(errorMsg.toString());
    }
}
```

### 2.6.2 错误恢复机制

```java
public class ErrorRecoveryStrategy {
    public static ParseNode recoverFromError(Parser parser,
                                           RecognitionException e) {
        // 1. 跳过错误的Token
        parser.consume();
        
        // 2. 寻找同步点（如分号、关键字等）
        while (!parser.isExpectedToken(SEMICOLON) && 
               !parser.isExpectedToken(EOF)) {
            parser.consume();
        }
        
        // 3. 尝试继续解析
        try {
            return parser.statement();
        } catch (RecognitionException ex) {
            // 如果仍然失败，返回错误节点
            return new ErrorNode(ex.getMessage());
        }
    }
}
```

## 2.7 性能优化策略

### 2.7.1 解析器缓存

```java
public class ParserCache {
    private static final LRUCache<String, StatementBase> cache = 
        new LRUCache<>(1000);
    
    public static StatementBase getCachedAST(String sql) {
        return cache.get(sql);
    }
    
    public static void cacheAST(String sql, StatementBase ast) {
        cache.put(sql, ast);
    }
}
```

### 2.7.2 预编译支持

```java
public class PreparedStatementParser {
    public static PreparedStatement parse(String sql) {
        // 1. 识别参数占位符
        List<ParameterMarker> parameters = findParameters(sql);
        
        // 2. 替换占位符为临时值进行解析
        String normalizedSQL = normalizeSQL(sql, parameters);
        
        // 3. 解析生成AST模板
        StatementBase astTemplate = SqlParser.parse(normalizedSQL, null);
        
        // 4. 创建预编译语句
        return new PreparedStatement(astTemplate, parameters);
    }
}
```

### 2.7.3 并行解析

```java
public class ParallelParser {
    private static final ExecutorService executor = 
        Executors.newFixedThreadPool(4);
    
    public static List<StatementBase> parseMultiple(List<String> sqls) {
        List<Future<StatementBase>> futures = new ArrayList<>();
        
        for (String sql : sqls) {
            Future<StatementBase> future = executor.submit(() -> 
                SqlParser.parse(sql, null));
            futures.add(future);
        }
        
        return futures.stream()
                     .map(this::getFutureResult)
                     .collect(Collectors.toList());
    }
}
```

## 2.8 扩展性设计

### 2.8.1 插件化语法扩展

```java
public interface SyntaxExtension {
    boolean canHandle(String sql);
    ParseNode parse(String sql, SessionVariable sessionVariable);
}

public class SyntaxExtensionManager {
    private static final List<SyntaxExtension> extensions = new ArrayList<>();
    
    public static void registerExtension(SyntaxExtension extension) {
        extensions.add(extension);
    }
    
    public static ParseNode parseWithExtensions(String sql,
                                              SessionVariable sessionVariable) {
        for (SyntaxExtension extension : extensions) {
            if (extension.canHandle(sql)) {
                return extension.parse(sql, sessionVariable);
            }
        }
        
        // 使用默认解析器
        return SqlParser.parse(sql, sessionVariable);
    }
}
```

### 2.8.2 自定义函数语法

```java
public class CustomFunctionSyntax implements SyntaxExtension {
    @Override
    public boolean canHandle(String sql) {
        return sql.contains("CUSTOM_FUNCTION");
    }
    
    @Override
    public ParseNode parse(String sql, SessionVariable sessionVariable) {
        // 自定义函数的特殊解析逻辑
        return parseCustomFunction(sql);
    }
}
```

## 小结

StarRocks的SQL解析系统采用了现代编译器的标准设计模式，通过ANTLR4构建了一套完整的词法和语法分析体系。其设计特点包括：

1. **模块化设计**: 词法分析、语法分析、AST构建分离
2. **扩展性强**: 支持多种SQL方言和自定义语法
3. **错误处理完善**: 提供详细的错误信息和恢复机制
4. **性能优化**: 缓存、预编译、并行解析等优化策略
5. **可维护性好**: 清晰的代码结构和接口设计

在下一章中，我们将深入分析语义分析阶段，了解StarRocks如何在AST的基础上进行语义检查和元数据绑定。
