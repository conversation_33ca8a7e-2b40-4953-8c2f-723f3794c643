# 第十章：向量化算子与执行优化

## 引言

向量化执行是现代OLAP数据库的核心技术，通过批量处理和SIMD指令优化，显著提升了查询执行性能。StarRocks实现了完整的向量化算子体系，包括扫描、连接、聚合、排序等各种算子的向量化实现。本章将深入分析StarRocks向量化算子的设计原理和执行优化技术。

## 10.1 向量化执行原理

### 10.1.1 向量化 vs 标量化

传统标量化执行 vs 向量化执行：

```
标量化执行（逐行处理）:
for (row in table) {
    if (row.age > 25) {
        result.add(row.name);
    }
}

向量化执行（批量处理）:
chunk = table.get_chunk(4096);  // 一次获取4096行
age_vector = chunk.get_column("age");
name_vector = chunk.get_column("name");
filter_vector = age_vector > 25;  // SIMD向量比较
result_names = name_vector.filter(filter_vector);  // 向量过滤
```

### 10.1.2 核心算子接口

基于`operator.h`的源码分析：

```cpp
class Operator {
public:
    virtual ~Operator() = default;
    
    // 核心执行接口
    virtual Status prepare(RuntimeState* state) = 0;
    virtual Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) = 0;
    virtual Status push_chunk(RuntimeState* state, const ChunkPtr& chunk) = 0;
    
    // 状态查询接口
    virtual bool has_output() const = 0;
    virtual bool need_input() const = 0;
    virtual bool is_finished() const = 0;
    
    // 资源管理接口
    virtual int64_t get_memory_usage() const = 0;
    virtual void close(RuntimeState* state) = 0;
    
    // 性能统计接口
    virtual RuntimeProfile* get_runtime_profile() const = 0;
    
protected:
    OperatorType _operator_type;
    int32_t _id;
    std::string _name;
    
    // 性能统计
    RuntimeProfile* _runtime_profile = nullptr;
    RuntimeProfile::Counter* _pull_chunk_timer = nullptr;
    RuntimeProfile::Counter* _push_chunk_timer = nullptr;
    RuntimeProfile::Counter* _rows_returned_counter = nullptr;
    RuntimeProfile::Counter* _bytes_returned_counter = nullptr;
};

// 源算子基类（数据生产者）
class SourceOperator : public Operator {
public:
    Status push_chunk(RuntimeState* state, const ChunkPtr& chunk) override {
        return Status::NotSupported("Source operator doesn't support push_chunk");
    }
    
    bool need_input() const override { return false; }
    
protected:
    virtual Status _pull_chunk(RuntimeState* state, ChunkPtr* chunk) = 0;
    
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) final {
        SCOPED_TIMER(_pull_chunk_timer);
        
        Status status = _pull_chunk(state, chunk);
        
        if (status.ok() && *chunk && (*chunk)->num_rows() > 0) {
            _rows_returned_counter->update((*chunk)->num_rows());
            _bytes_returned_counter->update((*chunk)->bytes_usage());
        }
        
        return status;
    }
};

// 汇聚算子基类（数据消费者）
class SinkOperator : public Operator {
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        return Status::NotSupported("Sink operator doesn't support pull_chunk");
    }
    
    bool has_output() const override { return false; }
    
protected:
    virtual Status _push_chunk(RuntimeState* state, const ChunkPtr& chunk) = 0;
    
public:
    Status push_chunk(RuntimeState* state, const ChunkPtr& chunk) final {
        SCOPED_TIMER(_push_chunk_timer);
        return _push_chunk(state, chunk);
    }
};
```

这个算子接口设计体现了向量化执行的核心理念：
- **批量处理**: 以Chunk为单位进行数据处理
- **推拉模式**: 支持数据的推送和拉取操作
- **状态管理**: 明确的算子状态和生命周期管理
- **性能监控**: 内置的性能统计和监控机制

## 10.2 扫描算子优化

### 10.2.1 OLAP扫描算子

OLAP表扫描的向量化实现：

```cpp
class OlapScanOperator : public SourceOperator {
private:
    // 扫描参数
    TOlapScanNode _scan_node;
    std::vector<SlotDescriptor*> _slot_descs;
    std::vector<ExprContext*> _conjunct_ctxs;
    
    // 扫描状态
    std::unique_ptr<OlapScanContext> _scan_ctx;
    std::vector<std::unique_ptr<RowsetReader>> _rowset_readers;
    size_t _current_reader_index = 0;
    
    // 性能统计
    RuntimeProfile::Counter* _scan_timer = nullptr;
    RuntimeProfile::Counter* _io_timer = nullptr;
    RuntimeProfile::Counter* _decompress_timer = nullptr;
    RuntimeProfile::Counter* _predicate_filter_timer = nullptr;
    
public:
    Status prepare(RuntimeState* state) override {
        RETURN_IF_ERROR(Operator::prepare(state));
        
        // 1. 初始化扫描上下文
        _scan_ctx = std::make_unique<OlapScanContext>();
        RETURN_IF_ERROR(init_scan_context(state));
        
        // 2. 创建Rowset读取器
        RETURN_IF_ERROR(create_rowset_readers(state));
        
        // 3. 初始化谓词下推
        RETURN_IF_ERROR(init_predicate_pushdown(state));
        
        return Status::OK();
    }
    
protected:
    Status _pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        SCOPED_TIMER(_scan_timer);
        
        while (_current_reader_index < _rowset_readers.size()) {
            auto& reader = _rowset_readers[_current_reader_index];
            
            // 1. 从当前Rowset读取数据
            ChunkPtr raw_chunk;
            Status status = read_chunk_from_rowset(reader.get(), &raw_chunk);
            
            if (status.is_end_of_file()) {
                // 当前Rowset读取完毕，切换到下一个
                _current_reader_index++;
                continue;
            }
            
            RETURN_IF_ERROR(status);
            
            if (!raw_chunk || raw_chunk->num_rows() == 0) {
                continue;
            }
            
            // 2. 应用谓词过滤
            ChunkPtr filtered_chunk;
            RETURN_IF_ERROR(apply_predicate_filter(raw_chunk, &filtered_chunk));
            
            if (filtered_chunk && filtered_chunk->num_rows() > 0) {
                *chunk = filtered_chunk;
                return Status::OK();
            }
        }
        
        // 所有Rowset都已读取完毕
        *chunk = nullptr;
        return Status::EndOfFile("Scan completed");
    }
    
private:
    Status read_chunk_from_rowset(RowsetReader* reader, ChunkPtr* chunk) {
        SCOPED_TIMER(_io_timer);
        
        // 1. 读取原始数据块
        std::unique_ptr<SegmentIterator> seg_iter;
        RETURN_IF_ERROR(reader->get_segment_iterator(&seg_iter));
        
        // 2. 向量化读取
        auto chunk_builder = std::make_unique<ChunkBuilder>(_slot_descs);
        
        while (chunk_builder->num_rows() < DEFAULT_CHUNK_SIZE) {
            RowBlock row_block;
            Status status = seg_iter->next_batch(&row_block);
            
            if (status.is_end_of_file()) {
                break;
            }
            RETURN_IF_ERROR(status);
            
            // 3. 向量化解压和解码
            RETURN_IF_ERROR(decode_row_block(row_block, chunk_builder.get()));
        }
        
        *chunk = chunk_builder->build();
        return Status::OK();
    }
    
    Status decode_row_block(const RowBlock& row_block, ChunkBuilder* builder) {
        SCOPED_TIMER(_decompress_timer);
        
        // 向量化解码各列数据
        for (size_t col_idx = 0; col_idx < _slot_descs.size(); col_idx++) {
            auto* slot_desc = _slot_descs[col_idx];
            const ColumnBlock& col_block = row_block.get_column_block(col_idx);
            
            // 根据列类型选择相应的向量化解码器
            std::unique_ptr<ColumnDecoder> decoder = 
                ColumnDecoderFactory::create_decoder(slot_desc->type(), col_block.encoding());
            
            ColumnPtr column;
            RETURN_IF_ERROR(decoder->decode_batch(col_block, &column));
            
            builder->add_column(column);
        }
        
        return Status::OK();
    }
    
    Status apply_predicate_filter(const ChunkPtr& input_chunk, ChunkPtr* output_chunk) {
        SCOPED_TIMER(_predicate_filter_timer);
        
        if (_conjunct_ctxs.empty()) {
            *output_chunk = input_chunk;
            return Status::OK();
        }
        
        // 1. 创建选择向量
        SelectVector select_vector(input_chunk->num_rows());
        select_vector.select_all();
        
        // 2. 逐个应用谓词条件
        for (auto* conjunct_ctx : _conjunct_ctxs) {
            ColumnPtr result_column;
            RETURN_IF_ERROR(conjunct_ctx->evaluate(input_chunk.get(), &result_column));
            
            // 向量化布尔过滤
            apply_boolean_filter(result_column, &select_vector);
            
            if (select_vector.count_selected() == 0) {
                break; // 提前退出
            }
        }
        
        // 3. 应用过滤结果
        if (select_vector.count_selected() < input_chunk->num_rows()) {
            *output_chunk = input_chunk->filter(select_vector);
        } else {
            *output_chunk = input_chunk;
        }
        
        return Status::OK();
    }
};
```

### 10.2.2 列式存储优化

列式存储的向量化读取优化：

```cpp
class ColumnReader {
private:
    std::unique_ptr<ColumnDecoder> _decoder;
    std::unique_ptr<IndexReader> _index_reader;
    CompressionType _compression_type;
    
public:
    Status read_batch(size_t start_row, size_t num_rows, ColumnPtr* column) {
        // 1. 索引过滤
        std::vector<uint32_t> row_ids;
        RETURN_IF_ERROR(_index_reader->filter_rows(start_row, num_rows, &row_ids));
        
        if (row_ids.empty()) {
            *column = create_empty_column();
            return Status::OK();
        }
        
        // 2. 向量化解压
        std::vector<Slice> compressed_blocks;
        RETURN_IF_ERROR(read_compressed_blocks(row_ids, &compressed_blocks));
        
        std::vector<Slice> decompressed_blocks;
        RETURN_IF_ERROR(decompress_blocks(compressed_blocks, &decompressed_blocks));
        
        // 3. 向量化解码
        return _decoder->decode_batch(decompressed_blocks, row_ids, column);
    }
    
private:
    Status decompress_blocks(const std::vector<Slice>& compressed_blocks,
                           std::vector<Slice>* decompressed_blocks) {
        
        switch (_compression_type) {
            case CompressionType::LZ4:
                return decompress_lz4_batch(compressed_blocks, decompressed_blocks);
            case CompressionType::ZSTD:
                return decompress_zstd_batch(compressed_blocks, decompressed_blocks);
            case CompressionType::SNAPPY:
                return decompress_snappy_batch(compressed_blocks, decompressed_blocks);
            default:
                *decompressed_blocks = compressed_blocks;
                return Status::OK();
        }
    }
    
    Status decompress_lz4_batch(const std::vector<Slice>& compressed_blocks,
                              std::vector<Slice>* decompressed_blocks) {
        
        // 向量化LZ4解压
        decompressed_blocks->reserve(compressed_blocks.size());
        
        for (const auto& compressed_block : compressed_blocks) {
            Slice decompressed_block;
            RETURN_IF_ERROR(LZ4Decompressor::decompress(compressed_block, &decompressed_block));
            decompressed_blocks->push_back(decompressed_block);
        }
        
        return Status::OK();
    }
};
```

## 10.3 连接算子优化

### 10.3.1 Hash连接向量化

Hash连接的向量化实现：

```cpp
class VectorizedHashJoinOperator : public Operator {
private:
    // 连接参数
    JoinType _join_type;
    std::vector<ExprContext*> _build_expr_ctxs;
    std::vector<ExprContext*> _probe_expr_ctxs;
    std::vector<ExprContext*> _other_join_conjunct_ctxs;
    
    // Hash表
    std::unique_ptr<HashTable> _hash_table;
    bool _build_finished = false;
    
    // 探测状态
    ChunkPtr _current_probe_chunk;
    size_t _probe_chunk_index = 0;
    
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        // 1. 构建阶段
        if (!_build_finished) {
            RETURN_IF_ERROR(build_hash_table(state));
            _build_finished = true;
        }
        
        // 2. 探测阶段
        return probe_hash_table(state, chunk);
    }
    
private:
    Status build_hash_table(RuntimeState* state) {
        _hash_table = std::make_unique<HashTable>();
        
        ChunkPtr build_chunk;
        while (true) {
            RETURN_IF_ERROR(_children[1]->pull_chunk(state, &build_chunk));
            
            if (!build_chunk || build_chunk->num_rows() == 0) {
                break; // 构建端数据读取完毕
            }
            
            // 向量化Hash表构建
            RETURN_IF_ERROR(build_hash_table_from_chunk(build_chunk));
        }
        
        return Status::OK();
    }
    
    Status build_hash_table_from_chunk(const ChunkPtr& chunk) {
        // 1. 计算Hash键
        std::vector<ColumnPtr> key_columns;
        for (auto* build_expr_ctx : _build_expr_ctxs) {
            ColumnPtr key_column;
            RETURN_IF_ERROR(build_expr_ctx->evaluate(chunk.get(), &key_column));
            key_columns.push_back(key_column);
        }
        
        // 2. 向量化Hash值计算
        std::vector<uint64_t> hash_values;
        RETURN_IF_ERROR(compute_hash_values(key_columns, &hash_values));
        
        // 3. 批量插入Hash表
        return _hash_table->insert_batch(key_columns, chunk, hash_values);
    }
    
    Status probe_hash_table(RuntimeState* state, ChunkPtr* result_chunk) {
        while (true) {
            // 1. 获取探测数据
            if (!_current_probe_chunk || _probe_chunk_index >= _current_probe_chunk->num_rows()) {
                RETURN_IF_ERROR(_children[0]->pull_chunk(state, &_current_probe_chunk));
                _probe_chunk_index = 0;
                
                if (!_current_probe_chunk || _current_probe_chunk->num_rows() == 0) {
                    *result_chunk = nullptr;
                    return Status::EndOfFile("Probe completed");
                }
            }
            
            // 2. 向量化探测
            ChunkPtr joined_chunk;
            size_t processed_rows;
            RETURN_IF_ERROR(probe_chunk_batch(_current_probe_chunk, _probe_chunk_index, 
                                            &joined_chunk, &processed_rows));
            
            _probe_chunk_index += processed_rows;
            
            if (joined_chunk && joined_chunk->num_rows() > 0) {
                *result_chunk = joined_chunk;
                return Status::OK();
            }
        }
    }
    
    Status probe_chunk_batch(const ChunkPtr& probe_chunk, size_t start_index,
                           ChunkPtr* result_chunk, size_t* processed_rows) {
        
        size_t batch_size = std::min(DEFAULT_CHUNK_SIZE, 
                                   probe_chunk->num_rows() - start_index);
        
        // 1. 提取探测键
        std::vector<ColumnPtr> probe_key_columns;
        for (auto* probe_expr_ctx : _probe_expr_ctxs) {
            ColumnPtr key_column;
            RETURN_IF_ERROR(probe_expr_ctx->evaluate(probe_chunk.get(), &key_column));
            
            // 切片处理
            probe_key_columns.push_back(key_column->slice(start_index, batch_size));
        }
        
        // 2. 向量化Hash查找
        std::vector<uint64_t> probe_hash_values;
        RETURN_IF_ERROR(compute_hash_values(probe_key_columns, &probe_hash_values));
        
        std::vector<HashTable::MatchResult> match_results;
        RETURN_IF_ERROR(_hash_table->probe_batch(probe_key_columns, probe_hash_values, 
                                               &match_results));
        
        // 3. 构建结果Chunk
        RETURN_IF_ERROR(build_result_chunk(probe_chunk, start_index, match_results, 
                                         result_chunk));
        
        *processed_rows = batch_size;
        return Status::OK();
    }
    
    Status compute_hash_values(const std::vector<ColumnPtr>& key_columns,
                             std::vector<uint64_t>* hash_values) {
        
        size_t num_rows = key_columns[0]->size();
        hash_values->resize(num_rows);
        
        // 向量化Hash计算
        if (key_columns.size() == 1) {
            // 单列Hash
            RETURN_IF_ERROR(key_columns[0]->compute_hash_values(hash_values->data()));
        } else {
            // 多列组合Hash
            for (size_t i = 0; i < num_rows; i++) {
                uint64_t hash_value = 0;
                for (auto& key_column : key_columns) {
                    uint64_t col_hash = key_column->compute_hash_value(i);
                    hash_value = combine_hash(hash_value, col_hash);
                }
                (*hash_values)[i] = hash_value;
            }
        }
        
        return Status::OK();
    }
};
```

### 10.3.2 Hash表优化

高性能Hash表的实现：

```cpp
class HashTable {
private:
    // Hash表结构
    std::vector<HashBucket> _buckets;
    size_t _bucket_count;
    size_t _size = 0;
    
    // 内存管理
    std::unique_ptr<MemPool> _mem_pool;
    
    // 性能优化
    static constexpr double MAX_LOAD_FACTOR = 0.75;
    static constexpr size_t CACHE_LINE_SIZE = 64;
    
public:
    struct MatchResult {
        bool matched;
        size_t build_row_index;
        size_t probe_row_index;
    };
    
    Status insert_batch(const std::vector<ColumnPtr>& key_columns,
                       const ChunkPtr& value_chunk,
                       const std::vector<uint64_t>& hash_values) {
        
        // 检查是否需要扩容
        if (need_resize()) {
            RETURN_IF_ERROR(resize());
        }
        
        size_t num_rows = key_columns[0]->size();
        
        // 向量化批量插入
        for (size_t i = 0; i < num_rows; i++) {
            uint64_t hash_value = hash_values[i];
            size_t bucket_index = hash_value % _bucket_count;
            
            // 创建Hash表项
            HashEntry* entry = create_hash_entry(key_columns, value_chunk, i);
            
            // 插入到Hash桶
            _buckets[bucket_index].insert(entry);
        }
        
        _size += num_rows;
        return Status::OK();
    }
    
    Status probe_batch(const std::vector<ColumnPtr>& probe_key_columns,
                      const std::vector<uint64_t>& hash_values,
                      std::vector<MatchResult>* results) {
        
        size_t num_rows = probe_key_columns[0]->size();
        results->resize(num_rows);
        
        // 向量化批量探测
        for (size_t i = 0; i < num_rows; i++) {
            uint64_t hash_value = hash_values[i];
            size_t bucket_index = hash_value % _bucket_count;
            
            // 在Hash桶中查找匹配项
            HashEntry* matched_entry = _buckets[bucket_index].find(probe_key_columns, i);
            
            if (matched_entry) {
                (*results)[i] = {true, matched_entry->build_row_index, i};
            } else {
                (*results)[i] = {false, 0, i};
            }
        }
        
        return Status::OK();
    }
    
private:
    bool need_resize() const {
        return static_cast<double>(_size) / _bucket_count > MAX_LOAD_FACTOR;
    }
    
    Status resize() {
        size_t new_bucket_count = _bucket_count * 2;
        std::vector<HashBucket> new_buckets(new_bucket_count);
        
        // 重新Hash所有元素
        for (auto& bucket : _buckets) {
            for (HashEntry* entry : bucket.get_entries()) {
                size_t new_bucket_index = entry->hash_value % new_bucket_count;
                new_buckets[new_bucket_index].insert(entry);
            }
        }
        
        _buckets = std::move(new_buckets);
        _bucket_count = new_bucket_count;
        
        return Status::OK();
    }
};

class HashBucket {
private:
    std::vector<HashEntry*> _entries;
    
public:
    void insert(HashEntry* entry) {
        _entries.push_back(entry);
    }
    
    HashEntry* find(const std::vector<ColumnPtr>& key_columns, size_t row_index) {
        for (HashEntry* entry : _entries) {
            if (entry->equals(key_columns, row_index)) {
                return entry;
            }
        }
        return nullptr;
    }
    
    const std::vector<HashEntry*>& get_entries() const {
        return _entries;
    }
};
```

## 10.4 聚合算子优化

### 10.4.1 向量化聚合

聚合算子的向量化实现：

```cpp
class VectorizedAggregateOperator : public Operator {
private:
    // 聚合参数
    std::vector<ExprContext*> _group_by_expr_ctxs;
    std::vector<AggregateFunction*> _agg_functions;
    
    // 聚合状态
    std::unique_ptr<AggregateHashTable> _agg_hash_table;
    bool _input_finished = false;
    bool _output_finished = false;
    
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        // 1. 处理输入数据
        if (!_input_finished) {
            RETURN_IF_ERROR(consume_input(state));
            _input_finished = true;
        }
        
        // 2. 输出聚合结果
        if (!_output_finished) {
            RETURN_IF_ERROR(_agg_hash_table->convert_to_chunk(chunk));
            _output_finished = true;
        } else {
            *chunk = nullptr;
        }
        
        return Status::OK();
    }
    
private:
    Status consume_input(RuntimeState* state) {
        ChunkPtr input_chunk;
        
        while (true) {
            RETURN_IF_ERROR(_children[0]->pull_chunk(state, &input_chunk));
            
            if (!input_chunk || input_chunk->num_rows() == 0) {
                break; // 输入数据处理完毕
            }
            
            // 向量化聚合处理
            RETURN_IF_ERROR(process_chunk(input_chunk));
        }
        
        return Status::OK();
    }
    
    Status process_chunk(const ChunkPtr& chunk) {
        // 1. 计算分组键
        std::vector<ColumnPtr> group_key_columns;
        for (auto* group_expr_ctx : _group_by_expr_ctxs) {
            ColumnPtr group_column;
            RETURN_IF_ERROR(group_expr_ctx->evaluate(chunk.get(), &group_column));
            group_key_columns.push_back(group_column);
        }
        
        // 2. 向量化聚合更新
        return _agg_hash_table->process_chunk(chunk, group_key_columns, _agg_functions);
    }
};

class AggregateHashTable {
private:
    std::unordered_map<GroupKey, AggregateState*> _groups;
    std::unique_ptr<MemPool> _mem_pool;
    
public:
    Status process_chunk(const ChunkPtr& chunk,
                        const std::vector<ColumnPtr>& group_key_columns,
                        const std::vector<AggregateFunction*>& agg_functions) {
        
        size_t num_rows = chunk->num_rows();
        
        // 1. 计算分组键Hash值
        std::vector<GroupKey> group_keys;
        RETURN_IF_ERROR(compute_group_keys(group_key_columns, &group_keys));
        
        // 2. 向量化聚合更新
        for (size_t row_idx = 0; row_idx < num_rows; row_idx++) {
            const GroupKey& group_key = group_keys[row_idx];
            
            // 查找或创建聚合状态
            AggregateState* agg_state = find_or_create_group(group_key);
            
            // 更新聚合函数
            for (size_t func_idx = 0; func_idx < agg_functions.size(); func_idx++) {
                auto* agg_func = agg_functions[func_idx];
                RETURN_IF_ERROR(agg_func->update(agg_state, chunk.get(), row_idx));
            }
        }
        
        return Status::OK();
    }
    
    Status convert_to_chunk(ChunkPtr* result_chunk) {
        if (_groups.empty()) {
            *result_chunk = nullptr;
            return Status::OK();
        }
        
        // 构建结果Chunk
        std::vector<ColumnPtr> result_columns;
        
        // 1. 添加分组键列
        for (size_t i = 0; i < _group_key_types.size(); i++) {
            auto column = create_column(_group_key_types[i]);
            
            for (auto& [group_key, agg_state] : _groups) {
                column->append(group_key.get_value(i));
            }
            
            result_columns.push_back(column);
        }
        
        // 2. 添加聚合结果列
        for (size_t func_idx = 0; func_idx < _agg_functions.size(); func_idx++) {
            auto* agg_func = _agg_functions[func_idx];
            auto column = create_column(agg_func->return_type());
            
            for (auto& [group_key, agg_state] : _groups) {
                Datum result;
                RETURN_IF_ERROR(agg_func->finalize(agg_state, &result));
                column->append(result);
            }
            
            result_columns.push_back(column);
        }
        
        *result_chunk = std::make_shared<Chunk>(result_columns, _groups.size());
        return Status::OK();
    }
    
private:
    AggregateState* find_or_create_group(const GroupKey& group_key) {
        auto it = _groups.find(group_key);
        if (it != _groups.end()) {
            return it->second;
        }
        
        // 创建新的聚合状态
        AggregateState* agg_state = _mem_pool->allocate<AggregateState>();
        _groups[group_key] = agg_state;
        
        return agg_state;
    }
};
```

### 10.4.2 聚合函数优化

高性能聚合函数的实现：

```cpp
class SumAggregateFunction : public AggregateFunction {
public:
    Status update(AggregateState* state, const Chunk* chunk, size_t row_index) override {
        auto* sum_state = reinterpret_cast<SumState*>(state);
        
        // 获取输入值
        auto input_column = chunk->get_column_by_index(_input_column_index);
        Datum input_value = input_column->get(row_index);
        
        if (!input_value.is_null()) {
            sum_state->sum += input_value.get_int64();
            sum_state->count++;
        }
        
        return Status::OK();
    }
    
    Status update_batch(AggregateState* state, const Chunk* chunk,
                       const std::vector<size_t>& row_indices) override {
        auto* sum_state = reinterpret_cast<SumState*>(state);
        auto input_column = chunk->get_column_by_index(_input_column_index);
        
        // 向量化批量更新
        for (size_t row_index : row_indices) {
            Datum input_value = input_column->get(row_index);
            if (!input_value.is_null()) {
                sum_state->sum += input_value.get_int64();
                sum_state->count++;
            }
        }
        
        return Status::OK();
    }
    
    Status finalize(AggregateState* state, Datum* result) override {
        auto* sum_state = reinterpret_cast<SumState*>(state);
        
        if (sum_state->count == 0) {
            *result = Datum(); // NULL
        } else {
            *result = Datum(sum_state->sum);
        }
        
        return Status::OK();
    }
    
private:
    struct SumState {
        int64_t sum = 0;
        int64_t count = 0;
    };
};

// SIMD优化的聚合函数
class SIMDSumAggregateFunction : public AggregateFunction {
public:
    Status update_batch_simd(AggregateState* state, const Chunk* chunk,
                            const SelectVector& select_vector) override {
        auto* sum_state = reinterpret_cast<SumState*>(state);
        auto input_column = down_cast<Int64Column*>(
            chunk->get_column_by_index(_input_column_index).get());
        
        const int64_t* data = input_column->get_data().data();
        
        // 使用SIMD指令进行向量化求和
        __m256i sum_vec = _mm256_setzero_si256();
        size_t simd_end = (select_vector.count_selected() / 4) * 4;
        
        for (size_t i = 0; i < simd_end; i += 4) {
            // 加载4个int64值
            __m256i values = _mm256_loadu_si256(
                reinterpret_cast<const __m256i*>(&data[select_vector.get_index(i)]));
            
            // 向量化加法
            sum_vec = _mm256_add_epi64(sum_vec, values);
        }
        
        // 提取SIMD结果
        int64_t simd_sum[4];
        _mm256_storeu_si256(reinterpret_cast<__m256i*>(simd_sum), sum_vec);
        
        for (int i = 0; i < 4; i++) {
            sum_state->sum += simd_sum[i];
        }
        
        // 处理剩余元素
        for (size_t i = simd_end; i < select_vector.count_selected(); i++) {
            sum_state->sum += data[select_vector.get_index(i)];
        }
        
        sum_state->count += select_vector.count_selected();
        
        return Status::OK();
    }
};
```

## 小结

StarRocks的向量化算子与执行优化体现了现代OLAP数据库的先进技术，通过批量处理、SIMD优化和高效的数据结构设计，显著提升了查询执行性能。其设计特点包括：

1. **完整的向量化体系**: 覆盖扫描、连接、聚合等所有核心算子
2. **高效的数据结构**: Chunk、Column、SelectVector等优化的数据结构
3. **SIMD指令优化**: 充分利用现代CPU的向量化指令集
4. **内存管理优化**: 高效的内存池和缓存友好的数据布局
5. **性能监控完善**: 详细的性能统计和调优支持

在下一章中，我们将深入分析存储引擎与数据访问优化的具体实现技术。
