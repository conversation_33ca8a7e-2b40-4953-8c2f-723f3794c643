# StarRocks SQL查询流程源码引用

本文档整理了《StarRocks_SQL查询流程深度解析》系列文章中引用的核心源码文件、类和方法，方便读者查阅和深入研究。

## FE (Frontend)

### `fe/fe-core/src/main/java/com/starrocks/StarRocksFE.java`
- **类**: `StarRocksFE`
- **描述**: StarRocks Frontend服务的入口点，负责启动和管理FE的各个组件。

### `fe/fe-core/src/main/java/com/starrocks/sql/parser/SqlParser.java`
- **类**: `SqlParser`
- **描述**: 使用ANTLR v4生成的SQL解析器，负责将SQL文本转换为抽象语法树 (AST)。

### `fe/fe-core/src/main/java/com/starrocks/sql/parser/AstBuilder.java`
- **类**: `AstBuilder`
- **描述**: 遍历ANTLR生成的Parse Tree，构建StarRocks自定义的AST结构。

### `fe/fe-core/src/main/java/com/starrocks/sql/analyzer/AnalyzerVisitor.java`
- **类**: `AnalyzerVisitor`
- **描述**: 语义分析器的核心，采用访问者模式遍历AST，进行表名/列名绑定、类型检查、权限验证等。

### `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Optimizer.java`
- **类**: `Optimizer`
- **描述**: 查询优化器的主类，驱动整个优化过程，包括RBO和CBO。

### `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/Memo.java`
- **类**: `Memo`
- **描述**: Cascades-style优化器中的核心数据结构，用于存储等价的逻辑计划表达式。

### `fe/fe-core/src/main/java/com/starrocks/sql/optimizer/rule/transformation/materialization/MaterializedViewRewriter.java`
- **类**: `MaterializedViewRewriter`
- **描述**: 物化视图查询改写的主逻辑，负责识别和执行基于物化视图的查询重写。

### `fe/fe-core/src/main/java/com/starrocks/sql/StatementPlanner.java`
- **类**: `StatementPlanner`
- **描述**: 将优化后的逻辑计划转换为物理执行计划（PlanFragment）。

### `fe/fe-core/src/main/java/com/starrocks/qe/StmtExecutor.java`
- **类**: `StmtExecutor`
- **描述**: SQL语句执行的入口，协调解析、分析、优化、规划和执行的整个流程。

### `fe/fe-core/src/main/java/com/starrocks/qe/DefaultCoordinator.java`
- **类**: `DefaultCoordinator`
- **描述**: 查询执行的协调者，负责将PlanFragment调度到BE节点，并管理执行状态。

### `fe/fe-core/src/main/java/com/starrocks/rpc/BackendServiceClient.java`
- **类**: `BackendServiceClient`
- **描述**: FE用于与BE进行RPC通信的客户端，基于Thrift。

## BE (Backend)

### `be/src/exec/pipeline/fragment_executor.cpp`
- **类**: `FragmentExecutor`
- **描述**: BE端接收并执行来自FE的PlanFragment的入口。

### `be/src/exec/pipeline/pipeline_driver.cpp`
- **类**: `PipelineDriver`
- **描述**: Pipeline执行模型的驱动核心，负责拉取数据并驱动算子执行。

### `be/src/exec/pipeline/operator.h` / `operator.cpp`
- **类**: `Operator`
- **描述**: Pipeline模型中执行算子的基类定义。

### `be/src/exec/vectorized/scan_node.h`
- **类**: `ScanNode`
- **描述**: 数据扫描节点的基类，负责从存储层读取数据。

### `be/src/storage/storage_engine.h`
- **类**: `StorageEngine`
- **描述**: BE存储引擎的顶层接口，管理Tablet的生命周期和数据操作。

## 文档

### `docs/zh/developers/trace-tools/query_trace_profile.md`
- **描述**: 官方文档，介绍如何使用Query Trace Profile和Runtime Profile进行性能分析和调试。