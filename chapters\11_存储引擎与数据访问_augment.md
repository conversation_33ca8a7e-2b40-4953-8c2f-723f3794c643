# 第十一章：存储引擎与数据访问

## 引言

存储引擎是数据库系统的基础组件，负责数据的持久化存储、索引管理和高效访问。StarRocks采用了先进的列式存储引擎，结合智能索引、数据压缩和向量化访问等技术，为OLAP查询提供了卓越的性能。本章将深入分析StarRocks存储引擎的核心架构和数据访问优化技术。

## 11.1 存储引擎架构概览

### 11.1.1 分层存储架构

StarRocks存储引擎采用分层架构设计：

```
┌─────────────────────────────────────────────────────────┐
│                    查询执行层                            │
├─────────────────────────────────────────────────────────┤
│                    存储访问层                            │
├─────────────────────────────────────────────────────────┤
│                    索引管理层                            │
├─────────────────────────────────────────────────────────┤
│                    数据组织层                            │
├─────────────────────────────────────────────────────────┤
│                    文件系统层                            │
└─────────────────────────────────────────────────────────┘
```

### 11.1.2 核心组件分析

存储引擎的核心组件：

```cpp
class StorageEngine {
private:
    // 核心管理器
    std::unique_ptr<TabletManager> _tablet_manager;
    std::unique_ptr<RowsetManager> _rowset_manager;
    std::unique_ptr<SchemaChangeHandler> _schema_change_handler;
    std::unique_ptr<CompactionManager> _compaction_manager;
    
    // 存储路径管理
    std::vector<DataDir*> _data_dirs;
    std::unique_ptr<StoragePathPolicy> _path_policy;
    
    // 内存管理
    std::shared_ptr<MemTracker> _mem_tracker;
    std::unique_ptr<MemTableFlushExecutor> _memtable_flush_executor;
    
    // 后台任务
    std::unique_ptr<TaskWorkerPool> _compaction_thread_pool;
    std::unique_ptr<TaskWorkerPool> _schema_change_thread_pool;
    
public:
    Status open() {
        // 1. 初始化数据目录
        RETURN_IF_ERROR(init_data_dirs());
        
        // 2. 恢复Tablet元数据
        RETURN_IF_ERROR(recover_tablet_metadata());
        
        // 3. 启动后台任务
        RETURN_IF_ERROR(start_background_tasks());
        
        // 4. 初始化管理器
        _tablet_manager = std::make_unique<TabletManager>();
        _rowset_manager = std::make_unique<RowsetManager>();
        _compaction_manager = std::make_unique<CompactionManager>();
        
        return Status::OK();
    }
    
    Status create_tablet(const TCreateTabletReq& request) {
        // 1. 创建Tablet目录
        std::string tablet_path = generate_tablet_path(request.tablet_id);
        RETURN_IF_ERROR(create_directory(tablet_path));
        
        // 2. 创建Tablet对象
        auto tablet = std::make_shared<Tablet>(request.tablet_id, request.tablet_schema);
        
        // 3. 初始化Tablet
        RETURN_IF_ERROR(tablet->init());
        
        // 4. 注册到TabletManager
        _tablet_manager->register_tablet(tablet);
        
        return Status::OK();
    }
    
private:
    Status recover_tablet_metadata() {
        // 扫描所有数据目录，恢复Tablet元数据
        for (auto* data_dir : _data_dirs) {
            RETURN_IF_ERROR(recover_tablets_in_dir(data_dir));
        }
        return Status::OK();
    }
};
```

这个存储引擎架构体现了现代存储系统的核心设计：
- **分层管理**: 清晰的分层架构，职责分离
- **元数据管理**: 完善的Tablet和Rowset元数据管理
- **后台任务**: 异步的Compaction和Schema Change处理
- **资源管理**: 统一的内存和存储资源管理

## 11.2 列式存储实现

### 11.2.1 Segment文件格式

StarRocks的Segment文件采用列式存储格式：

```cpp
class SegmentWriter {
private:
    struct ColumnWriter {
        std::unique_ptr<ColumnEncoder> encoder;
        std::unique_ptr<IndexBuilder> index_builder;
        std::unique_ptr<BloomFilterBuilder> bf_builder;
        CompressionType compression_type;
    };
    
    std::vector<ColumnWriter> _column_writers;
    std::unique_ptr<FileWriter> _file_writer;
    SegmentFooter _footer;
    
public:
    Status write_chunk(const Chunk& chunk) {
        // 1. 向量化列编码
        for (size_t col_idx = 0; col_idx < chunk.num_columns(); col_idx++) {
            auto& column_writer = _column_writers[col_idx];
            auto column = chunk.get_column_by_index(col_idx);
            
            // 编码列数据
            RETURN_IF_ERROR(column_writer.encoder->encode_batch(column));
            
            // 构建索引
            RETURN_IF_ERROR(column_writer.index_builder->add_batch(column));
            
            // 构建Bloom Filter
            if (column_writer.bf_builder) {
                RETURN_IF_ERROR(column_writer.bf_builder->add_batch(column));
            }
        }
        
        // 2. 更新统计信息
        _footer.num_rows += chunk.num_rows();
        update_column_statistics(chunk);
        
        return Status::OK();
    }
    
    Status finalize() {
        // 1. 完成列编码
        for (auto& column_writer : _column_writers) {
            RETURN_IF_ERROR(column_writer.encoder->finalize());
            RETURN_IF_ERROR(column_writer.index_builder->finalize());
            
            if (column_writer.bf_builder) {
                RETURN_IF_ERROR(column_writer.bf_builder->finalize());
            }
        }
        
        // 2. 写入Footer
        RETURN_IF_ERROR(write_footer());
        
        // 3. 关闭文件
        return _file_writer->close();
    }
    
private:
    void update_column_statistics(const Chunk& chunk) {
        for (size_t col_idx = 0; col_idx < chunk.num_columns(); col_idx++) {
            auto column = chunk.get_column_by_index(col_idx);
            auto& col_stats = _footer.column_statistics[col_idx];
            
            // 更新最小值/最大值
            col_stats.update_min_max(column);
            
            // 更新NULL值统计
            col_stats.null_count += column->count_nulls();
            
            // 更新数据大小
            col_stats.data_size += column->byte_size();
        }
    }
};

// Segment文件格式
struct SegmentFormat {
    // 文件头
    struct Header {
        uint32_t magic_number = 0x12345678;
        uint32_t version = 1;
        uint32_t num_columns;
        uint64_t num_rows;
    };
    
    // 列数据块
    struct ColumnBlock {
        uint64_t offset;
        uint64_t size;
        CompressionType compression;
        EncodingType encoding;
    };
    
    // 索引块
    struct IndexBlock {
        IndexType type;
        uint64_t offset;
        uint64_t size;
    };
    
    // 文件尾
    struct Footer {
        std::vector<ColumnBlock> column_blocks;
        std::vector<IndexBlock> index_blocks;
        std::vector<ColumnStatistics> column_statistics;
        uint64_t footer_offset;
        uint32_t footer_size;
    };
};
```

### 11.2.2 列编码优化

高效的列编码实现：

```cpp
class ColumnEncoder {
public:
    virtual ~ColumnEncoder() = default;
    virtual Status encode_batch(const ColumnPtr& column) = 0;
    virtual Status finalize() = 0;
    virtual EncodingType get_encoding_type() const = 0;
};

// 字典编码器
class DictionaryEncoder : public ColumnEncoder {
private:
    std::unordered_map<std::string, uint32_t> _dictionary;
    std::vector<uint32_t> _encoded_values;
    std::vector<std::string> _dict_values;
    
public:
    Status encode_batch(const ColumnPtr& column) override {
        auto string_column = down_cast<BinaryColumn*>(column.get());
        
        for (size_t i = 0; i < column->size(); i++) {
            Slice value = string_column->get_slice(i);
            std::string str_value = value.to_string();
            
            auto it = _dictionary.find(str_value);
            if (it == _dictionary.end()) {
                // 新值，添加到字典
                uint32_t dict_id = _dict_values.size();
                _dictionary[str_value] = dict_id;
                _dict_values.push_back(str_value);
                _encoded_values.push_back(dict_id);
            } else {
                // 已存在，使用字典ID
                _encoded_values.push_back(it->second);
            }
        }
        
        return Status::OK();
    }
    
    Status finalize() override {
        // 1. 压缩字典值
        RETURN_IF_ERROR(compress_dictionary());
        
        // 2. 压缩编码值
        RETURN_IF_ERROR(compress_encoded_values());
        
        return Status::OK();
    }
    
private:
    Status compress_dictionary() {
        // 使用LZ4压缩字典数据
        std::string dict_data;
        for (const auto& value : _dict_values) {
            dict_data += value;
            dict_data += '\0'; // 分隔符
        }
        
        return LZ4Compressor::compress(dict_data, &_compressed_dict);
    }
    
    Status compress_encoded_values() {
        // 使用位打包压缩编码值
        uint32_t max_value = _dict_values.size() - 1;
        uint32_t bits_per_value = calculate_bits_needed(max_value);
        
        BitPackingEncoder bit_encoder(bits_per_value);
        return bit_encoder.encode(_encoded_values, &_compressed_values);
    }
};

// RLE编码器
class RLEEncoder : public ColumnEncoder {
private:
    struct RLEPair {
        Datum value;
        uint32_t count;
    };
    
    std::vector<RLEPair> _rle_pairs;
    Datum _current_value;
    uint32_t _current_count = 0;
    
public:
    Status encode_batch(const ColumnPtr& column) override {
        for (size_t i = 0; i < column->size(); i++) {
            Datum value = column->get(i);
            
            if (_current_count == 0 || value.equals(_current_value)) {
                // 相同值，增加计数
                _current_value = value;
                _current_count++;
            } else {
                // 不同值，保存当前RLE对
                _rle_pairs.push_back({_current_value, _current_count});
                _current_value = value;
                _current_count = 1;
            }
        }
        
        return Status::OK();
    }
    
    Status finalize() override {
        // 保存最后一个RLE对
        if (_current_count > 0) {
            _rle_pairs.push_back({_current_value, _current_count});
        }
        
        // 序列化RLE数据
        return serialize_rle_pairs();
    }
};
```

### 11.2.3 数据压缩策略

多种压缩算法的选择和优化：

```cpp
class CompressionManager {
public:
    static CompressionType choose_compression(const ColumnPtr& column, 
                                            const ColumnStatistics& stats) {
        
        // 1. 基于数据类型选择
        if (column->is_numeric()) {
            if (stats.distinct_count < stats.row_count * 0.1) {
                return CompressionType::DICTIONARY; // 低基数数值
            } else {
                return CompressionType::LZ4; // 高基数数值
            }
        }
        
        // 2. 基于字符串特征选择
        if (column->is_binary()) {
            double avg_length = static_cast<double>(stats.total_size) / stats.row_count;
            
            if (avg_length < 10 && stats.distinct_count < stats.row_count * 0.3) {
                return CompressionType::DICTIONARY; // 短字符串，低基数
            } else if (avg_length > 100) {
                return CompressionType::ZSTD; // 长字符串，高压缩率
            } else {
                return CompressionType::LZ4; // 中等长度，平衡压缩率和速度
            }
        }
        
        // 3. 基于重复模式选择
        if (stats.run_length_ratio > 0.8) {
            return CompressionType::RLE; // 高重复率
        }
        
        return CompressionType::LZ4; // 默认选择
    }
    
    static std::unique_ptr<Compressor> create_compressor(CompressionType type) {
        switch (type) {
            case CompressionType::LZ4:
                return std::make_unique<LZ4Compressor>();
            case CompressionType::ZSTD:
                return std::make_unique<ZSTDCompressor>();
            case CompressionType::SNAPPY:
                return std::make_unique<SnappyCompressor>();
            case CompressionType::DICTIONARY:
                return std::make_unique<DictionaryCompressor>();
            case CompressionType::RLE:
                return std::make_unique<RLECompressor>();
            default:
                return nullptr;
        }
    }
};

class LZ4Compressor : public Compressor {
public:
    Status compress(const Slice& input, std::string* output) override {
        int max_compressed_size = LZ4_compressBound(input.size);
        output->resize(max_compressed_size);
        
        int compressed_size = LZ4_compress_default(
            input.data, 
            output->data(), 
            input.size, 
            max_compressed_size);
        
        if (compressed_size <= 0) {
            return Status::InternalError("LZ4 compression failed");
        }
        
        output->resize(compressed_size);
        return Status::OK();
    }
    
    Status decompress(const Slice& input, std::string* output, size_t uncompressed_size) override {
        output->resize(uncompressed_size);
        
        int decompressed_size = LZ4_decompress_safe(
            input.data,
            output->data(),
            input.size,
            uncompressed_size);
        
        if (decompressed_size != uncompressed_size) {
            return Status::InternalError("LZ4 decompression failed");
        }
        
        return Status::OK();
    }
};
```

## 11.3 智能索引系统

### 11.3.1 多层索引架构

StarRocks实现了多层索引系统：

```cpp
class IndexManager {
private:
    // 不同类型的索引
    std::unique_ptr<ZoneMapIndex> _zone_map_index;
    std::unique_ptr<BloomFilterIndex> _bloom_filter_index;
    std::unique_ptr<BitmapIndex> _bitmap_index;
    std::unique_ptr<InvertedIndex> _inverted_index;
    
public:
    Status build_indexes(const Chunk& chunk, const std::vector<ColumnId>& index_columns) {
        for (ColumnId col_id : index_columns) {
            auto column = chunk.get_column_by_index(col_id);
            
            // 1. 构建Zone Map索引
            RETURN_IF_ERROR(_zone_map_index->add_batch(col_id, column));
            
            // 2. 构建Bloom Filter索引
            if (should_build_bloom_filter(col_id)) {
                RETURN_IF_ERROR(_bloom_filter_index->add_batch(col_id, column));
            }
            
            // 3. 构建Bitmap索引
            if (should_build_bitmap_index(col_id)) {
                RETURN_IF_ERROR(_bitmap_index->add_batch(col_id, column));
            }
            
            // 4. 构建倒排索引
            if (should_build_inverted_index(col_id)) {
                RETURN_IF_ERROR(_inverted_index->add_batch(col_id, column));
            }
        }
        
        return Status::OK();
    }
    
    Status filter_by_indexes(const std::vector<Predicate*>& predicates,
                           std::vector<uint32_t>* row_ids) {
        
        // 1. Zone Map过滤
        RETURN_IF_ERROR(_zone_map_index->filter(predicates, row_ids));
        
        if (row_ids->empty()) {
            return Status::OK(); // 已被完全过滤
        }
        
        // 2. Bloom Filter过滤
        RETURN_IF_ERROR(_bloom_filter_index->filter(predicates, row_ids));
        
        if (row_ids->empty()) {
            return Status::OK();
        }
        
        // 3. Bitmap索引过滤
        RETURN_IF_ERROR(_bitmap_index->filter(predicates, row_ids));
        
        return Status::OK();
    }
};

// Zone Map索引实现
class ZoneMapIndex {
private:
    struct ZoneMapEntry {
        Datum min_value;
        Datum max_value;
        uint32_t start_row;
        uint32_t end_row;
        bool has_null;
    };
    
    std::map<ColumnId, std::vector<ZoneMapEntry>> _zone_maps;
    
public:
    Status add_batch(ColumnId col_id, const ColumnPtr& column) {
        auto& zone_map = _zone_maps[col_id];
        
        // 计算当前批次的统计信息
        ZoneMapEntry entry;
        entry.start_row = _current_row_count;
        entry.end_row = _current_row_count + column->size() - 1;
        
        // 计算最小值和最大值
        column->get_min_max(&entry.min_value, &entry.max_value);
        entry.has_null = column->has_null();
        
        zone_map.push_back(entry);
        _current_row_count += column->size();
        
        return Status::OK();
    }
    
    Status filter(const std::vector<Predicate*>& predicates,
                 std::vector<uint32_t>* row_ids) {
        
        std::vector<uint32_t> filtered_rows;
        
        for (uint32_t row_id : *row_ids) {
            bool should_include = true;
            
            for (auto* predicate : predicates) {
                if (!can_satisfy_predicate(predicate, row_id)) {
                    should_include = false;
                    break;
                }
            }
            
            if (should_include) {
                filtered_rows.push_back(row_id);
            }
        }
        
        *row_ids = std::move(filtered_rows);
        return Status::OK();
    }
    
private:
    bool can_satisfy_predicate(Predicate* predicate, uint32_t row_id) {
        ColumnId col_id = predicate->get_column_id();
        auto& zone_map = _zone_maps[col_id];
        
        // 找到包含该行的Zone Map条目
        for (const auto& entry : zone_map) {
            if (row_id >= entry.start_row && row_id <= entry.end_row) {
                return predicate->can_satisfy(entry.min_value, entry.max_value, entry.has_null);
            }
        }
        
        return true; // 保守策略，如果找不到则包含
    }
};
```

### 11.3.2 Bloom Filter索引

高效的Bloom Filter实现：

```cpp
class BloomFilterIndex {
private:
    struct BloomFilterEntry {
        std::unique_ptr<BloomFilter> filter;
        uint32_t start_row;
        uint32_t end_row;
    };
    
    std::map<ColumnId, std::vector<BloomFilterEntry>> _bloom_filters;
    
public:
    Status add_batch(ColumnId col_id, const ColumnPtr& column) {
        auto& bf_entries = _bloom_filters[col_id];
        
        // 创建新的Bloom Filter
        BloomFilterEntry entry;
        entry.filter = std::make_unique<BloomFilter>(
            calculate_optimal_size(column->size()),
            calculate_optimal_hash_functions(column->size()));
        entry.start_row = _current_row_count;
        entry.end_row = _current_row_count + column->size() - 1;
        
        // 添加所有值到Bloom Filter
        for (size_t i = 0; i < column->size(); i++) {
            if (!column->is_null(i)) {
                Datum value = column->get(i);
                uint64_t hash = value.hash();
                entry.filter->add(hash);
            }
        }
        
        bf_entries.push_back(std::move(entry));
        _current_row_count += column->size();
        
        return Status::OK();
    }
    
    Status filter(const std::vector<Predicate*>& predicates,
                 std::vector<uint32_t>* row_ids) {
        
        std::vector<uint32_t> filtered_rows;
        
        for (uint32_t row_id : *row_ids) {
            bool should_include = true;
            
            for (auto* predicate : predicates) {
                if (predicate->get_type() == PredicateType::EQ) {
                    if (!bloom_filter_contains(predicate, row_id)) {
                        should_include = false;
                        break;
                    }
                }
            }
            
            if (should_include) {
                filtered_rows.push_back(row_id);
            }
        }
        
        *row_ids = std::move(filtered_rows);
        return Status::OK();
    }
    
private:
    bool bloom_filter_contains(Predicate* predicate, uint32_t row_id) {
        ColumnId col_id = predicate->get_column_id();
        auto& bf_entries = _bloom_filters[col_id];
        
        // 找到对应的Bloom Filter
        for (const auto& entry : bf_entries) {
            if (row_id >= entry.start_row && row_id <= entry.end_row) {
                Datum target_value = predicate->get_value();
                uint64_t hash = target_value.hash();
                return entry.filter->contains(hash);
            }
        }
        
        return true; // 保守策略
    }
    
    size_t calculate_optimal_size(size_t num_elements) {
        // 基于期望的假阳性率计算最优大小
        double fpp = 0.01; // 1%假阳性率
        return static_cast<size_t>(-num_elements * std::log(fpp) / (std::log(2) * std::log(2)));
    }
    
    size_t calculate_optimal_hash_functions(size_t num_elements) {
        size_t m = calculate_optimal_size(num_elements);
        return static_cast<size_t>(m * std::log(2) / num_elements);
    }
};

class BloomFilter {
private:
    std::vector<uint64_t> _bits;
    size_t _num_bits;
    size_t _num_hash_functions;
    
public:
    BloomFilter(size_t num_bits, size_t num_hash_functions) 
        : _num_bits(num_bits), _num_hash_functions(num_hash_functions) {
        _bits.resize((_num_bits + 63) / 64, 0);
    }
    
    void add(uint64_t hash) {
        for (size_t i = 0; i < _num_hash_functions; i++) {
            uint64_t bit_pos = (hash + i * 0x9e3779b9) % _num_bits;
            size_t word_index = bit_pos / 64;
            size_t bit_index = bit_pos % 64;
            _bits[word_index] |= (1ULL << bit_index);
        }
    }
    
    bool contains(uint64_t hash) const {
        for (size_t i = 0; i < _num_hash_functions; i++) {
            uint64_t bit_pos = (hash + i * 0x9e3779b9) % _num_bits;
            size_t word_index = bit_pos / 64;
            size_t bit_index = bit_pos % 64;
            
            if ((_bits[word_index] & (1ULL << bit_index)) == 0) {
                return false;
            }
        }
        return true;
    }
};
```

## 11.4 数据访问优化

### 11.4.1 向量化读取

高效的向量化数据读取：

```cpp
class SegmentReader {
private:
    std::unique_ptr<FileReader> _file_reader;
    SegmentFooter _footer;
    std::vector<std::unique_ptr<ColumnReader>> _column_readers;
    
public:
    Status read_chunk(const std::vector<ColumnId>& column_ids,
                     const std::vector<Predicate*>& predicates,
                     ChunkPtr* chunk) {
        
        // 1. 索引过滤
        std::vector<uint32_t> row_ids;
        RETURN_IF_ERROR(filter_by_indexes(predicates, &row_ids));
        
        if (row_ids.empty()) {
            *chunk = nullptr;
            return Status::OK();
        }
        
        // 2. 向量化列读取
        std::vector<ColumnPtr> columns;
        for (ColumnId col_id : column_ids) {
            ColumnPtr column;
            RETURN_IF_ERROR(_column_readers[col_id]->read_batch(row_ids, &column));
            columns.push_back(column);
        }
        
        // 3. 构建Chunk
        *chunk = std::make_shared<Chunk>(columns, row_ids.size());
        
        return Status::OK();
    }
    
private:
    Status filter_by_indexes(const std::vector<Predicate*>& predicates,
                           std::vector<uint32_t>* row_ids) {
        
        // 初始化所有行ID
        row_ids->resize(_footer.num_rows);
        std::iota(row_ids->begin(), row_ids->end(), 0);
        
        // 应用索引过滤
        return _index_manager->filter_by_indexes(predicates, row_ids);
    }
};

class ColumnReader {
private:
    std::unique_ptr<ColumnDecoder> _decoder;
    std::unique_ptr<Decompressor> _decompressor;
    ColumnMetadata _metadata;
    
public:
    Status read_batch(const std::vector<uint32_t>& row_ids, ColumnPtr* column) {
        // 1. 计算需要读取的数据块
        std::vector<DataBlock> blocks_to_read;
        RETURN_IF_ERROR(calculate_blocks_to_read(row_ids, &blocks_to_read));
        
        // 2. 批量读取数据块
        std::vector<Slice> compressed_blocks;
        RETURN_IF_ERROR(read_data_blocks(blocks_to_read, &compressed_blocks));
        
        // 3. 向量化解压
        std::vector<Slice> decompressed_blocks;
        RETURN_IF_ERROR(decompress_blocks(compressed_blocks, &decompressed_blocks));
        
        // 4. 向量化解码
        RETURN_IF_ERROR(_decoder->decode_batch(decompressed_blocks, row_ids, column));
        
        return Status::OK();
    }
    
private:
    Status calculate_blocks_to_read(const std::vector<uint32_t>& row_ids,
                                  std::vector<DataBlock>* blocks) {
        
        // 将行ID转换为数据块范围
        std::set<uint32_t> block_ids;
        for (uint32_t row_id : row_ids) {
            uint32_t block_id = row_id / ROWS_PER_BLOCK;
            block_ids.insert(block_id);
        }
        
        // 构建数据块信息
        for (uint32_t block_id : block_ids) {
            DataBlock block;
            block.block_id = block_id;
            block.offset = _metadata.block_offsets[block_id];
            block.size = _metadata.block_sizes[block_id];
            blocks->push_back(block);
        }
        
        return Status::OK();
    }
    
    Status decompress_blocks(const std::vector<Slice>& compressed_blocks,
                           std::vector<Slice>* decompressed_blocks) {
        
        decompressed_blocks->reserve(compressed_blocks.size());
        
        // 并行解压多个数据块
        std::vector<std::future<Status>> futures;
        
        for (const auto& compressed_block : compressed_blocks) {
            auto future = std::async(std::launch::async, [this, &compressed_block]() {
                std::string decompressed_data;
                return _decompressor->decompress(compressed_block, &decompressed_data);
            });
            futures.push_back(std::move(future));
        }
        
        // 收集解压结果
        for (auto& future : futures) {
            RETURN_IF_ERROR(future.get());
        }
        
        return Status::OK();
    }
};
```

### 11.4.2 预取和缓存

智能的数据预取和缓存策略：

```cpp
class DataCache {
private:
    struct CacheEntry {
        std::string data;
        uint64_t access_time;
        uint32_t access_count;
        size_t size;
    };
    
    std::unordered_map<std::string, CacheEntry> _cache;
    std::mutex _cache_mutex;
    size_t _max_cache_size;
    size_t _current_cache_size = 0;
    
    // LRU管理
    std::list<std::string> _lru_list;
    std::unordered_map<std::string, std::list<std::string>::iterator> _lru_map;
    
public:
    bool get(const std::string& key, std::string* data) {
        std::lock_guard<std::mutex> lock(_cache_mutex);
        
        auto it = _cache.find(key);
        if (it != _cache.end()) {
            // 更新访问信息
            it->second.access_time = get_current_time();
            it->second.access_count++;
            
            // 更新LRU
            update_lru(key);
            
            *data = it->second.data;
            return true;
        }
        
        return false;
    }
    
    void put(const std::string& key, const std::string& data) {
        std::lock_guard<std::mutex> lock(_cache_mutex);
        
        // 检查是否需要淘汰
        while (_current_cache_size + data.size() > _max_cache_size && !_cache.empty()) {
            evict_lru();
        }
        
        // 添加新条目
        CacheEntry entry;
        entry.data = data;
        entry.access_time = get_current_time();
        entry.access_count = 1;
        entry.size = data.size();
        
        _cache[key] = entry;
        _current_cache_size += data.size();
        
        // 更新LRU
        _lru_list.push_front(key);
        _lru_map[key] = _lru_list.begin();
    }
    
private:
    void evict_lru() {
        if (_lru_list.empty()) return;
        
        std::string key = _lru_list.back();
        _lru_list.pop_back();
        _lru_map.erase(key);
        
        auto it = _cache.find(key);
        if (it != _cache.end()) {
            _current_cache_size -= it->second.size;
            _cache.erase(it);
        }
    }
    
    void update_lru(const std::string& key) {
        auto lru_it = _lru_map.find(key);
        if (lru_it != _lru_map.end()) {
            _lru_list.erase(lru_it->second);
            _lru_list.push_front(key);
            _lru_map[key] = _lru_list.begin();
        }
    }
};

class PrefetchManager {
private:
    std::unique_ptr<ThreadPool> _prefetch_thread_pool;
    std::shared_ptr<DataCache> _cache;
    
public:
    void prefetch_data(const std::vector<std::string>& keys) {
        for (const auto& key : keys) {
            _prefetch_thread_pool->submit([this, key]() {
                prefetch_single_key(key);
            });
        }
    }
    
private:
    void prefetch_single_key(const std::string& key) {
        // 检查缓存中是否已存在
        std::string cached_data;
        if (_cache->get(key, &cached_data)) {
            return; // 已在缓存中
        }
        
        // 从存储读取数据
        std::string data;
        Status status = read_from_storage(key, &data);
        
        if (status.ok()) {
            // 添加到缓存
            _cache->put(key, data);
        }
    }
};
```

## 小结

StarRocks的存储引擎与数据访问系统实现了现代OLAP数据库的先进存储技术，通过列式存储、智能索引、数据压缩和向量化访问等技术，为高性能分析查询提供了坚实的基础。其设计特点包括：

1. **高效的列式存储**: 优化的列编码和压缩算法
2. **多层索引系统**: Zone Map、Bloom Filter、Bitmap等多种索引类型
3. **智能数据访问**: 向量化读取和预取缓存优化
4. **灵活的压缩策略**: 基于数据特征的自适应压缩选择
5. **完善的元数据管理**: 高效的Tablet和Rowset管理机制

在下一章中，我们将深入分析性能监控与调试工具的设计与实现。
