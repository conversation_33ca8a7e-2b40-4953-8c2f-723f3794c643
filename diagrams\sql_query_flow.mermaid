sequenceDiagram
    participant Client as 客户端
    participant FE as Frontend
    participant Parser as SQL解析器
    participant Analy<PERSON> as 语义分析器
    participant Optimizer as 查询优化器
    participant Planner as 计划生成器
    participant Coordinator as 查询协调器
    participant B<PERSON> as Backend
    participant Pipeline as Pipeline引擎
    participant Storage as 存储引擎

    Client->>FE: SQL查询请求
    FE->>Parser: 解析SQL文本
    Parser->>Parser: ANTLR语法分析
    Parser->>FE: 返回AST
    FE->>Analyzer: 语义分析
    Analyzer->>Analyzer: 绑定表/列，类型检查
    Analyzer->>FE: 返回逻辑计划
    FE->>Optimizer: 查询优化
    Optimizer->>Optimizer: RBO/CBO优化，MV改写
    Optimizer->>FE: 返回优化计划
    FE->>Planner: 生成物理计划
    Planner->>Planner: Fragment分片
    Planner->>FE: 返回执行计划
    FE->>Coordinator: 协调执行
    Coordinator->>BE: 下发Fragment
    BE->>Pipeline: 启动Pipeline执行
    Pipeline->>Storage: 数据访问
    Storage->>Pipeline: 返回数据
    Pipeline->>BE: 计算结果
    BE->>Coordinator: 返回结果
    Coordinator->>FE: 汇总结果
    FE->>Client: 返回查询结果