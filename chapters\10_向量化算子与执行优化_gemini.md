# 第十章：向量化算子与执行优化

## 1. 引言

在第九章中，我们探讨了Pipeline执行引擎如何从宏观上组织和调度计算任务，以最大化CPU的利用率。然而，引擎的宏观调度效率再高，最终的性能也取决于微观层面——即每个算子（Operator）内部计算的效率。传统的数据库算子通常采用“一次一元组”（Tuple-at-a-time）的处理模式，这种模式在现代CPU架构下面临严重的性能瓶颈。为了解决这个问题，StarRocks从一开始就采用了**全向量化（Vectorized）**的计算模型。本章将深入算子内部，剖析向量化计算的原理，以及StarRocks如何通过SIMD指令、内存优化、算子融合等技术，将算子的执行效率推向极致。

## 2. 从元组模型到向量化模型

*   **元组模型（Tuple-at-a-time）**：算子一次处理一行数据。数据在算子之间以行的形式流转。
    ```cpp
    // Conceptual Tuple-at-a-time execution
    while (tuple = child->GetNext()) {
        if (evaluate_predicate(tuple)) {
            process_tuple(tuple);
        }
    }
    ```
    *   **缺点**：
        1.  **高昂的函数调用开销**：每处理一行数据，都可能涉及多次虚函数调用，CPU开销巨大。
        2.  **无法利用SIMD**：CPU无法对单行数据进行并行计算。
        3.  **缓存不友好**：解释性执行，分支预测困难，指令缓存命中率低。

*   **向量化模型（Vectorized / Batch-at-a-time）**：算子一次处理一批数据（一个`Chunk`或`Column`）。数据在算子之间以列式的数据块形式流转。
    ```cpp
    // Conceptual Vectorized execution
    while (chunk = child->GetNextChunk()) {
        // The whole loop can be optimized by the compiler
        for (int i = 0; i < chunk->size(); ++i) {
            if (predicate_column[i]) {
                // process data at row i
            }
        }
    }
    ```
    *   **优点**：
        1.  **分摊函数调用开销**：函数调用的开销被分摊到一批数据上，单位数据的开销急剧下降。
        2.  **SIMD友好**：紧凑的列式内存布局，非常适合使用CPU的SIMD（Single Instruction, Multiple Data）指令集（如SSE, AVX）进行并行计算。
        3.  **缓存友好**：在一个紧凑的循环内对一批数据进行操作，极大地提高了CPU指令和数据缓存的命中率。

## 3. 源码分析：StarRocks的向量化实现

核心参考源码：
*   `be/src/exec/pipeline/operator.h`
*   `be/src/exec/vectorized/` (目录下所有算子实现, e.g., `hash_join_operator.cpp`, `aggregate_operator.cpp`, `scan_operator.cpp`)
*   `be/src/column/column.h`
*   `be/src/gutil/simd.h`

### 3.1. `Chunk`与`Column`：向量化数据的载体

向量化计算的基础是列式的数据块。在StarRocks中，这个载体是`Chunk`。
*   一个`Chunk`代表一批（通常是几千行）数据。
*   `Chunk`内部不存储数据，而是持有一组`Column`对象的智能指针。
*   `Column`是实际存储数据的接口，它有多种实现，如`Int32Column`, `StringColumn`, `NullableColumn`等。数据在`Column`中以连续的数组形式存储，这是向量化和SIMD优化的关键。

### 3.2. 向量化算子（`Operator`）的实现

StarRocks的`Operator`都继承自`pipeline::Operator`，并实现了向量化的计算逻辑。以一个简单的过滤算子为例：

```cpp
// Simplified Filter Operator
class FilterOperator : public pipeline::Operator {
public:
    Status push_chunk(RuntimeState* state, Chunk* chunk) override {
        // 1. Evaluate the predicate on the whole chunk
        Column* filter_column = evaluate_predicate(chunk);

        // 2. Get a selection vector (an array of indices of rows that passed the filter)
        uint16_t* selection = get_selection_vector(filter_column);
        size_t selected_size = ...;

        // 3. Create a new chunk with only the selected rows
        Chunk* result_chunk = chunk->clone_empty();
        chunk->filter_by(selection, selected_size, result_chunk);

        // 4. Push the filtered chunk to the next operator
        return _child->push_chunk(state, result_chunk);
    }
};
```
这个例子展示了向量化计算的典型模式：操作以整列（`filter_column`）或整个`Chunk`为单位，而不是单行。

### 3.3. SIMD的应用

SIMD允许CPU用一条指令对多个数据执行相同的操作。StarRocks在许多计算密集型的算子中，都广泛地使用了SIMD指令来加速计算。

`gutil/simd.h`中封装了对不同SIMD指令集（AVX2, SSE4.2等）的跨平台调用。例如，在执行`a + b`这样的算术运算时，代码会检查CPU是否支持AVX2，如果支持，就会一次性加载8个`int32`整数（256位），用一条`_mm256_add_epi32`指令完成8次加法，相比传统的循环，性能可以提升数倍。

### 3.4. 其他执行优化

除了向量化和SIMD，StarRocks还应用了许多其他的执行层优化技术：

*   **运行时代码生成（Runtime Code Generation / JIT）**：对于一些复杂的表达式计算，StarRocks使用LLVM在运行时动态生成高度优化的机器码，消除虚函数调用和不必要的分支判断，将表达式求值性能推向极致。
*   **列式内存布局**：所有数据都以列式存储和处理，这不仅有利于向量化，也极大地提高了压缩率和I/O效率。
*   **Arena内存管理**：在查询执行期间，使用`Arena`来统一管理内存分配。这避免了频繁调用`malloc/free`带来的系统开销，并通过一次性释放所有内存，解决了复杂的内存生命周期管理问题。
*   **算子融合（Operator Fusion）**：在可能的情况下，将多个简单的算子（如连续的Filter）在代码层面融合成一个超级算子，减少算子间数据传递的开销。

## 4. 总结

如果说Pipeline引擎是StarRocks高性能的骨架，那么向量化算子就是其强健的肌肉。通过将数据处理模式从“一次一行”转变为“一次一批”，StarRocks充分释放了现代CPU的潜力。向量化计算、SIMD指令、运行时代码生成、列式内存布局等一系列优化技术的综合运用，使得每个算子内部的计算都达到了极高的效率。正是这种在宏观调度和微观计算上对性能毫不妥协的追求，共同铸就了StarRocks在OLAP领域的领先地位。
