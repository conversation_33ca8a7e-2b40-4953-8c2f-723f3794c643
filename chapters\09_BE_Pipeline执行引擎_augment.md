# 第九章：BE Pipeline执行引擎

## 引言

Pipeline执行引擎是StarRocks BE端的核心组件，代表了现代OLAP数据库执行引擎的先进设计理念。相比传统的火山模型，Pipeline执行引擎采用向量化批处理和流水线并行的方式，显著提升了查询执行性能。本章将深入分析StarRocks BE Pipeline执行引擎的核心原理和实现机制。

## 9.1 Pipeline架构概览

### 9.1.1 执行模型对比

传统火山模型 vs Pipeline模型：

```
传统火山模型:
┌─────────────┐
│   Operator  │ ← next()
├─────────────┤   ↓ 单行处理
│   Operator  │ ← next()
├─────────────┤   ↓
│   Operator  │ ← next()
└─────────────┘

Pipeline模型:
┌─────────────┐
│   Operator  │ ← pull_chunk()
├─────────────┤   ↓ 批量处理(4096行)
│   Operator  │ ← pull_chunk()
├─────────────┤   ↓
│   Operator  │ ← pull_chunk()
└─────────────┘
```

### 9.1.2 核心组件分析

基于`fragment_executor.cpp`的源码分析：

```cpp
class FragmentExecutor {
private:
    std::unique_ptr<RuntimeState> _runtime_state;
    std::vector<std::shared_ptr<Pipeline>> _pipelines;
    std::shared_ptr<QueryContext> _query_ctx;
    
    // 执行状态
    std::atomic<bool> _is_cancelled{false};
    std::atomic<bool> _is_finished{false};
    
    // 性能统计
    RuntimeProfile* _runtime_profile;
    
public:
    Status prepare(const TExecPlanFragmentParams& params) {
        // 1. 初始化运行时状态
        RETURN_IF_ERROR(init_runtime_state(params));
        
        // 2. 构建Pipeline
        RETURN_IF_ERROR(build_pipelines(params.fragment));
        
        // 3. 准备执行环境
        RETURN_IF_ERROR(prepare_execution_environment());
        
        return Status::OK();
    }
    
    Status execute() {
        // 1. 启动所有Pipeline
        for (auto& pipeline : _pipelines) {
            RETURN_IF_ERROR(pipeline->prepare(_runtime_state.get()));
        }
        
        // 2. 创建Pipeline驱动器
        std::vector<std::shared_ptr<PipelineDriver>> drivers;
        for (auto& pipeline : _pipelines) {
            auto driver = std::make_shared<PipelineDriver>(pipeline, _query_ctx);
            drivers.push_back(driver);
        }
        
        // 3. 提交到执行器
        auto* executor = ExecEnv::GetInstance()->pipeline_executor();
        for (auto& driver : drivers) {
            executor->submit(driver);
        }
        
        // 4. 等待执行完成
        return wait_for_completion();
    }
    
private:
    Status build_pipelines(const TPlanFragment& fragment) {
        // 1. 构建算子树
        std::shared_ptr<Operator> root_op;
        RETURN_IF_ERROR(build_operator_tree(fragment.plan, &root_op));
        
        // 2. 分解为Pipeline
        PipelineBuilder builder(_runtime_state.get());
        _pipelines = builder.decompose(root_op);
        
        return Status::OK();
    }
};
```

这个Fragment执行器体现了Pipeline执行的核心设计：
- **Pipeline分解**: 将算子树分解为多个可并行执行的Pipeline
- **驱动器模式**: 通过PipelineDriver驱动Pipeline执行
- **异步执行**: 基于线程池的异步执行模型
- **状态管理**: 完善的执行状态跟踪和错误处理

## 9.2 Pipeline构建机制

### 9.2.1 算子树分解

将算子树分解为Pipeline的核心算法：

```cpp
class PipelineBuilder {
private:
    RuntimeState* _runtime_state;
    std::vector<std::shared_ptr<Pipeline>> _pipelines;
    
public:
    std::vector<std::shared_ptr<Pipeline>> decompose(std::shared_ptr<Operator> root) {
        _pipelines.clear();
        
        // 1. 深度优先遍历算子树
        decompose_recursive(root, nullptr);
        
        // 2. 设置Pipeline间的依赖关系
        setup_pipeline_dependencies();
        
        return _pipelines;
    }
    
private:
    std::shared_ptr<Pipeline> decompose_recursive(std::shared_ptr<Operator> op, 
                                                 std::shared_ptr<Pipeline> current_pipeline) {
        
        if (is_pipeline_breaker(op)) {
            // 遇到Pipeline分割点，创建新的Pipeline
            auto new_pipeline = create_new_pipeline();
            new_pipeline->add_operator(op);
            
            // 递归处理子节点
            for (auto& child : op->get_children()) {
                auto child_pipeline = decompose_recursive(child, new_pipeline);
                new_pipeline->add_dependency(child_pipeline);
            }
            
            _pipelines.push_back(new_pipeline);
            return new_pipeline;
            
        } else {
            // 普通算子，添加到当前Pipeline
            if (current_pipeline == nullptr) {
                current_pipeline = create_new_pipeline();
                _pipelines.push_back(current_pipeline);
            }
            
            current_pipeline->add_operator(op);
            
            // 递归处理子节点
            for (auto& child : op->get_children()) {
                decompose_recursive(child, current_pipeline);
            }
            
            return current_pipeline;
        }
    }
    
    bool is_pipeline_breaker(std::shared_ptr<Operator> op) {
        // 判断是否是Pipeline分割点
        return op->get_type() == OperatorType::EXCHANGE_SOURCE ||
               op->get_type() == OperatorType::SCAN ||
               op->get_type() == OperatorType::HASH_JOIN_BUILD ||
               op->get_type() == OperatorType::AGGREGATE_BLOCKING;
    }
};

class Pipeline {
private:
    std::vector<std::shared_ptr<Operator>> _operators;
    std::vector<std::shared_ptr<Pipeline>> _dependencies;
    PipelineId _id;
    
    // 执行状态
    std::atomic<PipelineState> _state{PipelineState::PENDING};
    std::atomic<int> _active_drivers{0};
    
public:
    void add_operator(std::shared_ptr<Operator> op) {
        _operators.push_back(op);
    }
    
    void add_dependency(std::shared_ptr<Pipeline> dependency) {
        _dependencies.push_back(dependency);
    }
    
    bool is_ready_to_execute() {
        // 检查所有依赖Pipeline是否已完成
        for (auto& dep : _dependencies) {
            if (dep->get_state() != PipelineState::FINISHED) {
                return false;
            }
        }
        return true;
    }
    
    Status prepare(RuntimeState* state) {
        // 准备所有算子
        for (auto& op : _operators) {
            RETURN_IF_ERROR(op->prepare(state));
        }
        
        _state = PipelineState::READY;
        return Status::OK();
    }
};
```

### 9.2.2 算子类型与特征

不同算子类型的Pipeline特征：

```cpp
enum class OperatorType {
    // 源算子（Pipeline起点）
    SCAN,
    EXCHANGE_SOURCE,
    
    // 流式算子（不阻塞数据流）
    PROJECT,
    FILTER,
    LIMIT,
    
    // 阻塞算子（需要缓存数据）
    HASH_JOIN_BUILD,
    HASH_JOIN_PROBE,
    AGGREGATE_BLOCKING,
    AGGREGATE_STREAMING,
    SORT,
    
    // 汇聚算子（Pipeline终点）
    EXCHANGE_SINK,
    RESULT_SINK
};

class OperatorFactory {
public:
    static std::shared_ptr<Operator> create_operator(OperatorType type, 
                                                    const TPlanNode& plan_node,
                                                    RuntimeState* state) {
        switch (type) {
            case OperatorType::SCAN:
                return create_scan_operator(plan_node, state);
            case OperatorType::PROJECT:
                return create_project_operator(plan_node, state);
            case OperatorType::FILTER:
                return create_filter_operator(plan_node, state);
            case OperatorType::HASH_JOIN_BUILD:
                return create_hash_join_build_operator(plan_node, state);
            case OperatorType::HASH_JOIN_PROBE:
                return create_hash_join_probe_operator(plan_node, state);
            case OperatorType::AGGREGATE_BLOCKING:
                return create_aggregate_operator(plan_node, state);
            default:
                return nullptr;
        }
    }
    
private:
    static std::shared_ptr<Operator> create_scan_operator(const TPlanNode& plan_node,
                                                         RuntimeState* state) {
        if (plan_node.node_type == TPlanNodeType::OLAP_SCAN_NODE) {
            return std::make_shared<OlapScanOperator>(plan_node.olap_scan_node, state);
        } else if (plan_node.node_type == TPlanNodeType::EXTERNAL_SCAN_NODE) {
            return std::make_shared<ExternalScanOperator>(plan_node.external_scan_node, state);
        }
        return nullptr;
    }
};
```

### 9.2.3 Pipeline调度策略

Pipeline的调度和执行策略：

```cpp
class PipelineScheduler {
private:
    std::queue<std::shared_ptr<Pipeline>> _ready_pipelines;
    std::vector<std::shared_ptr<Pipeline>> _pending_pipelines;
    std::mutex _scheduler_mutex;
    
public:
    void schedule_pipelines(const std::vector<std::shared_ptr<Pipeline>>& pipelines) {
        std::lock_guard<std::mutex> lock(_scheduler_mutex);
        
        for (auto& pipeline : pipelines) {
            if (pipeline->is_ready_to_execute()) {
                _ready_pipelines.push(pipeline);
            } else {
                _pending_pipelines.push_back(pipeline);
            }
        }
    }
    
    std::shared_ptr<Pipeline> get_next_ready_pipeline() {
        std::lock_guard<std::mutex> lock(_scheduler_mutex);
        
        if (!_ready_pipelines.empty()) {
            auto pipeline = _ready_pipelines.front();
            _ready_pipelines.pop();
            return pipeline;
        }
        
        return nullptr;
    }
    
    void on_pipeline_finished(std::shared_ptr<Pipeline> finished_pipeline) {
        std::lock_guard<std::mutex> lock(_scheduler_mutex);
        
        // 检查是否有依赖此Pipeline的其他Pipeline可以执行
        auto it = _pending_pipelines.begin();
        while (it != _pending_pipelines.end()) {
            if ((*it)->is_ready_to_execute()) {
                _ready_pipelines.push(*it);
                it = _pending_pipelines.erase(it);
            } else {
                ++it;
            }
        }
    }
};
```

## 9.3 PipelineDriver执行机制

### 9.3.1 驱动器核心实现

基于`pipeline_driver.cpp`的源码分析：

```cpp
class PipelineDriver {
private:
    std::shared_ptr<Pipeline> _pipeline;
    std::shared_ptr<QueryContext> _query_ctx;
    RuntimeState* _runtime_state;
    
    // 执行状态
    DriverState _state = DriverState::NOT_READY;
    std::atomic<bool> _is_cancelled{false};
    
    // 性能统计
    int64_t _total_chunks_moved = 0;
    int64_t _total_rows_moved = 0;
    
public:
    Status process(RuntimeState* state) {
        if (_is_cancelled.load()) {
            return Status::Cancelled("Driver cancelled");
        }
        
        // 1. 检查Pipeline是否准备就绪
        if (!_pipeline->is_ready_to_execute()) {
            return Status::OK(); // 等待依赖完成
        }
        
        // 2. 执行Pipeline中的算子
        ChunkPtr chunk;
        bool has_output = false;
        
        RETURN_IF_ERROR(pull_chunk(state, &chunk, &has_output));
        
        if (has_output && chunk && chunk->num_rows() > 0) {
            // 3. 将数据推送到下游
            RETURN_IF_ERROR(push_chunk(state, chunk));
            
            // 4. 更新统计信息
            _total_chunks_moved++;
            _total_rows_moved += chunk->num_rows();
        }
        
        // 5. 检查是否完成
        if (is_finished()) {
            _state = DriverState::FINISHED;
            _pipeline->on_driver_finished();
        }
        
        return Status::OK();
    }
    
private:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk, bool* has_output) {
        *has_output = false;
        
        // 从Pipeline的源算子开始拉取数据
        auto& operators = _pipeline->get_operators();
        auto source_op = operators.front(); // 第一个算子是源算子
        
        RETURN_IF_ERROR(source_op->pull_chunk(state, chunk));
        
        if (*chunk && (*chunk)->num_rows() > 0) {
            *has_output = true;
            
            // 数据流经Pipeline中的所有算子
            for (size_t i = 1; i < operators.size(); i++) {
                RETURN_IF_ERROR(operators[i]->push_chunk(state, *chunk));
                
                // 某些算子可能会修改或过滤数据
                if ((*chunk)->num_rows() == 0) {
                    *has_output = false;
                    break;
                }
            }
        }
        
        return Status::OK();
    }
    
    Status push_chunk(RuntimeState* state, const ChunkPtr& chunk) {
        // 将数据推送到Pipeline的汇聚算子
        auto& operators = _pipeline->get_operators();
        auto sink_op = operators.back(); // 最后一个算子是汇聚算子
        
        return sink_op->push_chunk(state, chunk);
    }
    
    bool is_finished() {
        // 检查Pipeline中的所有算子是否都已完成
        auto& operators = _pipeline->get_operators();
        for (auto& op : operators) {
            if (!op->is_finished()) {
                return false;
            }
        }
        return true;
    }
};
```

### 9.3.2 执行器线程池

Pipeline执行器的线程池实现：

```cpp
class PipelineExecutor {
private:
    std::vector<std::thread> _worker_threads;
    std::queue<std::shared_ptr<PipelineDriver>> _driver_queue;
    std::mutex _queue_mutex;
    std::condition_variable _queue_cv;
    std::atomic<bool> _shutdown{false};
    
    // 配置参数
    int _num_threads;
    int _max_queue_size;
    
public:
    PipelineExecutor(int num_threads, int max_queue_size) 
        : _num_threads(num_threads), _max_queue_size(max_queue_size) {
        
        // 启动工作线程
        for (int i = 0; i < _num_threads; i++) {
            _worker_threads.emplace_back(&PipelineExecutor::worker_thread, this, i);
        }
    }
    
    Status submit(std::shared_ptr<PipelineDriver> driver) {
        std::unique_lock<std::mutex> lock(_queue_mutex);
        
        // 检查队列是否已满
        if (_driver_queue.size() >= _max_queue_size) {
            return Status::ResourceBusy("Driver queue is full");
        }
        
        _driver_queue.push(driver);
        _queue_cv.notify_one();
        
        return Status::OK();
    }
    
private:
    void worker_thread(int thread_id) {
        while (!_shutdown.load()) {
            std::shared_ptr<PipelineDriver> driver;
            
            // 1. 从队列中获取Driver
            {
                std::unique_lock<std::mutex> lock(_queue_mutex);
                _queue_cv.wait(lock, [this] { 
                    return !_driver_queue.empty() || _shutdown.load(); 
                });
                
                if (_shutdown.load()) {
                    break;
                }
                
                driver = _driver_queue.front();
                _driver_queue.pop();
            }
            
            // 2. 执行Driver
            try {
                Status status = driver->process(driver->get_runtime_state());
                
                if (!status.ok()) {
                    LOG(WARNING) << "Driver execution failed: " << status.to_string();
                    driver->cancel();
                } else if (!driver->is_finished()) {
                    // 如果Driver未完成，重新提交到队列
                    submit(driver);
                }
                
            } catch (const std::exception& e) {
                LOG(ERROR) << "Exception in driver execution: " << e.what();
                driver->cancel();
            }
        }
    }
};
```

### 9.3.3 资源管理

Pipeline执行的资源管理：

```cpp
class PipelineResourceManager {
private:
    // 内存管理
    std::shared_ptr<MemTracker> _mem_tracker;
    int64_t _max_memory_per_pipeline;
    
    // CPU管理
    std::atomic<int> _active_pipelines{0};
    int _max_concurrent_pipelines;
    
    // IO管理
    std::shared_ptr<IOThrottler> _io_throttler;
    
public:
    Status acquire_resources(std::shared_ptr<Pipeline> pipeline) {
        // 1. 检查并发Pipeline数量
        if (_active_pipelines.load() >= _max_concurrent_pipelines) {
            return Status::ResourceBusy("Too many concurrent pipelines");
        }
        
        // 2. 分配内存资源
        int64_t required_memory = estimate_pipeline_memory(pipeline);
        if (!_mem_tracker->try_consume(required_memory)) {
            return Status::MemoryLimitExceeded("Insufficient memory for pipeline");
        }
        
        // 3. 分配IO资源
        if (!_io_throttler->try_acquire()) {
            _mem_tracker->release(required_memory);
            return Status::ResourceBusy("IO throttling limit reached");
        }
        
        _active_pipelines.fetch_add(1);
        return Status::OK();
    }
    
    void release_resources(std::shared_ptr<Pipeline> pipeline) {
        // 1. 释放内存资源
        int64_t used_memory = pipeline->get_memory_usage();
        _mem_tracker->release(used_memory);
        
        // 2. 释放IO资源
        _io_throttler->release();
        
        // 3. 减少活跃Pipeline计数
        _active_pipelines.fetch_sub(1);
    }
    
private:
    int64_t estimate_pipeline_memory(std::shared_ptr<Pipeline> pipeline) {
        int64_t total_memory = 0;
        
        for (auto& op : pipeline->get_operators()) {
            total_memory += op->estimate_memory_usage();
        }
        
        return total_memory;
    }
};
```

## 9.4 向量化执行优化

### 9.4.1 Chunk数据结构

向量化执行的核心数据结构：

```cpp
class Chunk {
private:
    std::vector<ColumnPtr> _columns;
    size_t _num_rows;
    
    // 选择向量（用于过滤）
    std::shared_ptr<SelectVector> _select_vector;
    
public:
    static constexpr size_t DEFAULT_CHUNK_SIZE = 4096;
    
    Chunk(std::vector<ColumnPtr> columns, size_t num_rows) 
        : _columns(std::move(columns)), _num_rows(num_rows) {}
    
    size_t num_rows() const { return _num_rows; }
    size_t num_columns() const { return _columns.size(); }
    
    ColumnPtr get_column_by_index(size_t index) const {
        return _columns[index];
    }
    
    void append_column(ColumnPtr column) {
        _columns.push_back(column);
    }
    
    // 过滤操作
    ChunkPtr filter(const SelectVector& select_vector) const {
        std::vector<ColumnPtr> filtered_columns;
        
        for (auto& column : _columns) {
            filtered_columns.push_back(column->filter(select_vector));
        }
        
        return std::make_shared<Chunk>(filtered_columns, select_vector.count_selected());
    }
    
    // 投影操作
    ChunkPtr project(const std::vector<size_t>& column_indices) const {
        std::vector<ColumnPtr> projected_columns;
        
        for (size_t index : column_indices) {
            projected_columns.push_back(_columns[index]);
        }
        
        return std::make_shared<Chunk>(projected_columns, _num_rows);
    }
    
    // 内存使用估算
    size_t memory_usage() const {
        size_t total_memory = 0;
        for (auto& column : _columns) {
            total_memory += column->memory_usage();
        }
        return total_memory;
    }
};

class Column {
public:
    virtual ~Column() = default;
    
    virtual size_t size() const = 0;
    virtual size_t memory_usage() const = 0;
    virtual ColumnPtr filter(const SelectVector& select_vector) const = 0;
    virtual ColumnPtr slice(size_t offset, size_t length) const = 0;
    
    // 类型相关方法
    virtual TypeDescriptor get_type() const = 0;
    virtual bool is_nullable() const = 0;
    
    // 数据访问方法
    virtual Datum get(size_t index) const = 0;
    virtual void set(size_t index, const Datum& value) = 0;
};
```

### 9.4.2 向量化算子实现

向量化算子的实现示例：

```cpp
class VectorizedFilterOperator : public Operator {
private:
    std::vector<ExprContext*> _conjunct_ctxs;
    
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        // 1. 从子算子获取数据
        RETURN_IF_ERROR(_child->pull_chunk(state, chunk));
        
        if (!*chunk || (*chunk)->num_rows() == 0) {
            return Status::OK();
        }
        
        // 2. 向量化过滤
        SelectVector select_vector((*chunk)->num_rows());
        RETURN_IF_ERROR(evaluate_conjuncts(*chunk, &select_vector));
        
        // 3. 应用过滤结果
        if (select_vector.count_selected() < (*chunk)->num_rows()) {
            *chunk = (*chunk)->filter(select_vector);
        }
        
        return Status::OK();
    }
    
private:
    Status evaluate_conjuncts(const ChunkPtr& chunk, SelectVector* select_vector) {
        // 初始化选择向量（全部选中）
        select_vector->select_all();
        
        // 逐个评估过滤条件
        for (auto* conjunct_ctx : _conjunct_ctxs) {
            ColumnPtr result_column;
            RETURN_IF_ERROR(conjunct_ctx->evaluate(chunk.get(), &result_column));
            
            // 将布尔结果应用到选择向量
            apply_boolean_filter(result_column, select_vector);
            
            // 如果没有行被选中，可以提前退出
            if (select_vector->count_selected() == 0) {
                break;
            }
        }
        
        return Status::OK();
    }
    
    void apply_boolean_filter(const ColumnPtr& boolean_column, SelectVector* select_vector) {
        auto* bool_data = down_cast<BooleanColumn*>(boolean_column.get());
        
        for (size_t i = 0; i < boolean_column->size(); i++) {
            if (select_vector->is_selected(i)) {
                if (bool_data->get_data()[i] == 0) {
                    select_vector->unselect(i);
                }
            }
        }
    }
};

class VectorizedAggregateOperator : public Operator {
private:
    std::vector<AggregateFunction*> _agg_functions;
    std::vector<ExprContext*> _group_by_expr_ctxs;
    
    // 聚合状态
    std::unique_ptr<AggregateHashTable> _hash_table;
    bool _is_finished = false;
    
public:
    Status pull_chunk(RuntimeState* state, ChunkPtr* chunk) override {
        if (_is_finished) {
            *chunk = nullptr;
            return Status::OK();
        }
        
        // 1. 处理输入数据
        ChunkPtr input_chunk;
        while (true) {
            RETURN_IF_ERROR(_child->pull_chunk(state, &input_chunk));
            
            if (!input_chunk || input_chunk->num_rows() == 0) {
                break; // 没有更多输入数据
            }
            
            // 2. 向量化聚合处理
            RETURN_IF_ERROR(process_chunk(input_chunk));
        }
        
        // 3. 输出聚合结果
        RETURN_IF_ERROR(output_result(chunk));
        _is_finished = true;
        
        return Status::OK();
    }
    
private:
    Status process_chunk(const ChunkPtr& chunk) {
        // 1. 计算分组键
        std::vector<ColumnPtr> group_columns;
        for (auto* group_expr_ctx : _group_by_expr_ctxs) {
            ColumnPtr group_column;
            RETURN_IF_ERROR(group_expr_ctx->evaluate(chunk.get(), &group_column));
            group_columns.push_back(group_column);
        }
        
        // 2. 向量化聚合更新
        return _hash_table->process_chunk(chunk, group_columns, _agg_functions);
    }
    
    Status output_result(ChunkPtr* result_chunk) {
        return _hash_table->convert_to_chunk(result_chunk);
    }
};
```

## 小结

StarRocks的BE Pipeline执行引擎代表了现代OLAP数据库执行引擎的先进设计，通过Pipeline分解、向量化处理和并行执行等技术，显著提升了查询执行性能。其设计特点包括：

1. **Pipeline分解**: 将算子树智能分解为可并行执行的Pipeline
2. **向量化执行**: 基于Chunk的批量数据处理，提高CPU利用率
3. **异步并行**: 基于线程池的异步执行模型，充分利用多核资源
4. **资源管理**: 完善的内存、CPU、IO资源管理机制
5. **性能优化**: 多种执行优化技术，如选择向量、SIMD等

在下一章中，我们将深入分析向量化算子与执行优化的具体实现技术。
