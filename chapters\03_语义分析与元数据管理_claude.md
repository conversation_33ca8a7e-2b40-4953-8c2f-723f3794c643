# 第三章：语义分析与元数据管理

## 引言

语义分析是SQL查询处理的关键阶段，它将语法正确的AST转换为语义明确的逻辑计划。在这个过程中，StarRocks需要解析表名和列名、检查权限、推导数据类型、验证语义约束等。本章将深入分析StarRocks的语义分析架构，重点探讨元数据管理系统的设计理念和实现机制。

## 3.1 语义分析器整体架构

### 3.1.1 语义分析流程概览

StarRocks的语义分析采用了基于访问者模式的多阶段分析架构：

```mermaid
graph TD
    A[AST] --> B[Scope Builder]
    B --> C[Reference Resolver]
    C --> D[Type Resolver]
    D --> E[Function Analyzer]
    E --> F[Privilege Checker]
    F --> G[Constraint Validator]
    G --> H[Analyzed AST]
    
    subgraph "Analysis Phases"
        B
        C
        D
        E
        F
        G
    end
    
    subgraph "Metadata Dependencies"
        I[Catalog Manager]
        J[Schema Registry]
        K[Function Registry]
        L[Privilege Manager]
    end
    
    B -.-> I
    C -.-> I
    C -.-> J
    E -.-> K
    F -.-> L
```

### 3.1.2 核心分析器类结构

```java
// 语义分析器的核心类关系
AnalyzerVisitor
    ├── StatementAnalyzer
    ├── QueryAnalyzer  
    ├── ExpressionAnalyzer
    └── RelationAnalyzer

AnalyzerContext
    ├── GlobalStateMgr (元数据管理)
    ├── ConnectContext (会话上下文)
    └── AnalyzeState (分析状态)

Scope
    ├── LocalScope (局部作用域)
    ├── FieldScope (字段作用域)
    └── RelationScope (关系作用域)
```

## 3.2 AnalyzerVisitor核心实现

### 3.2.1 AnalyzerVisitor主类分析

让我们深入分析`AnalyzerVisitor.java`的核心实现：

```java
// fe/fe-core/src/main/java/com/starrocks/sql/analyzer/AnalyzerVisitor.java
public class AnalyzerVisitor implements AstVisitor<Void, ConnectContext> {
    
    // 分析状态管理
    private final AnalyzeState analyzeState;
    
    // 作用域管理器
    private final ScopeBuilder scopeBuilder;
    
    // 表达式分析器
    private final ExpressionAnalyzer expressionAnalyzer;
    
    // 关系分析器
    private final RelationAnalyzer relationAnalyzer;
    
    public AnalyzerVisitor() {
        this.analyzeState = new AnalyzeState();
        this.scopeBuilder = new ScopeBuilder();
        this.expressionAnalyzer = new ExpressionAnalyzer(this);
        this.relationAnalyzer = new RelationAnalyzer(this);
    }
    
    // 查询语句分析入口
    @Override
    public Void visitQueryStatement(QueryStatement statement, ConnectContext context) {
        // 1. 构建初始作用域
        Scope scope = scopeBuilder.createGlobalScope();
        analyzeState.setScope(scope);
        
        // 2. 分析查询关系
        QueryRelation query = statement.getQueryRelation();
        relationAnalyzer.analyze(query, scope, context);
        
        // 3. 设置输出列标签
        List<String> columnLabels = generateColumnLabels(query);
        statement.setColLabels(columnLabels);
        
        // 4. 验证查询语义
        validateQuerySemantics(statement, context);
        
        return null;
    }
    
    // SELECT语句分析
    @Override
    public Void visitSelectRelation(SelectRelation relation, ConnectContext context) {
        Scope scope = analyzeState.getScope();
        
        // 1. 分析FROM子句，建立表的作用域
        if (relation.getFromRelation() != null) {
            Scope fromScope = analyzeFromClause(relation.getFromRelation(), scope, context);
            analyzeState.setScope(fromScope);
        }
        
        // 2. 分析WHERE子句
        if (relation.getWhereClause() != null) {
            analyzeWhereClause(relation.getWhereClause(), context);
        }
        
        // 3. 分析GROUP BY子句
        if (relation.getGroupByClause() != null) {
            analyzeGroupByClause(relation.getGroupByClause(), context);
        }
        
        // 4. 分析SELECT列表
        analyzeSelectList(relation.getSelectList(), context);
        
        // 5. 分析HAVING子句
        if (relation.getHavingClause() != null) {
            analyzeHavingClause(relation.getHavingClause(), context);
        }
        
        // 6. 构建输出作用域
        Scope outputScope = buildOutputScope(relation);
        relation.setScope(outputScope);
        
        return null;
    }
    
    // FROM子句分析
    private Scope analyzeFromClause(Relation fromRelation, Scope parentScope, 
                                   ConnectContext context) {
        // 根据关系类型进行不同的分析
        if (fromRelation instanceof TableRelation) {
            return analyzeTableRelation((TableRelation) fromRelation, parentScope, context);
        } else if (fromRelation instanceof JoinRelation) {
            return analyzeJoinRelation((JoinRelation) fromRelation, parentScope, context);
        } else if (fromRelation instanceof SubqueryRelation) {
            return analyzeSubqueryRelation((SubqueryRelation) fromRelation, parentScope, context);
        } else {
            throw new AnalysisException("Unsupported relation type: " + fromRelation.getClass());
        }
    }
    
    // 表关系分析
    private Scope analyzeTableRelation(TableRelation tableRelation, Scope parentScope, 
                                      ConnectContext context) {
        // 1. 解析表名
        TableName tableName = tableRelation.getName();
        String dbName = tableName.getDb();
        String tblName = tableName.getTbl();
        
        // 2. 从Catalog中获取表信息
        Database database = GlobalStateMgr.getCurrentState().getDb(dbName);
        if (database == null) {
            throw new AnalysisException("Database '" + dbName + "' does not exist");
        }
        
        Table table = database.getTable(tblName);
        if (table == null) {
            throw new AnalysisException("Table '" + tblName + "' does not exist");
        }
        
        // 3. 权限检查
        checkTablePrivilege(table, PrivilegeType.SELECT, context);
        
        // 4. 构建表的作用域
        Scope tableScope = createTableScope(table, tableRelation.getAlias());
        
        // 5. 记录表引用信息
        analyzeState.addTableRef(new TableRef(table, tableRelation.getAlias()));
        
        return tableScope;
    }
    
    // JOIN关系分析
    private Scope analyzeJoinRelation(JoinRelation joinRelation, Scope parentScope, 
                                     ConnectContext context) {
        // 1. 分析左侧关系
        Relation leftRelation = joinRelation.getLeft();
        Scope leftScope = analyzeFromClause(leftRelation, parentScope, context);
        
        // 2. 分析右侧关系
        Relation rightRelation = joinRelation.getRight();
        Scope rightScope = analyzeFromClause(rightRelation, parentScope, context);
        
        // 3. 分析JOIN条件
        Expr joinCondition = joinRelation.getOnPredicate();
        if (joinCondition != null) {
            // 创建JOIN作用域，包含左右两侧的字段
            Scope joinScope = Scope.builder()
                .parent(parentScope)
                .addScope(leftScope)
                .addScope(rightScope)
                .build();
                
            analyzeState.setScope(joinScope);
            expressionAnalyzer.analyze(joinCondition, context);
        }
        
        // 4. 构建JOIN输出作用域
        return buildJoinOutputScope(joinRelation, leftScope, rightScope);
    }
}
```

### 3.2.2 作用域管理机制

StarRocks的作用域系统是语义分析的核心基础设施：

```java
// 作用域基类
public abstract class Scope {
    protected Scope parent;
    protected Map<String, Field> fields = new HashMap<>();
    protected List<Scope> childScopes = new ArrayList<>();
    
    // 字段解析
    public Field resolveField(String name) {
        // 1. 在当前作用域查找
        Field field = fields.get(name);
        if (field != null) {
            return field;
        }
        
        // 2. 在子作用域查找
        for (Scope childScope : childScopes) {
            field = childScope.resolveField(name);
            if (field != null) {
                return field;
            }
        }
        
        // 3. 在父作用域查找
        if (parent != null) {
            return parent.resolveField(name);
        }
        
        return null;
    }
    
    // 添加字段
    public void addField(String name, Field field) {
        if (fields.containsKey(name)) {
            throw new AnalysisException("Duplicate field name: " + name);
        }
        fields.put(name, field);
    }
    
    // 添加子作用域
    public void addChildScope(Scope scope) {
        scope.parent = this;
        childScopes.add(scope);
    }
}

// 表作用域
public class TableScope extends Scope {
    private final Table table;
    private final String alias;
    
    public TableScope(Table table, String alias) {
        this.table = table;
        this.alias = alias;
        buildFieldsFromTable();
    }
    
    private void buildFieldsFromTable() {
        // 从表结构构建字段信息
        for (Column column : table.getColumns()) {
            Field field = new Field(
                column.getName(),
                column.getType(),
                table,
                column
            );
            
            // 添加列名到作用域
            fields.put(column.getName(), field);
            
            // 如果有别名，也添加别名.列名的形式
            if (alias != null) {
                fields.put(alias + "." + column.getName(), field);
            }
            
            // 添加表名.列名的形式
            fields.put(table.getName() + "." + column.getName(), field);
        }
    }
}

// 字段信息
public class Field {
    private final String name;
    private final Type type;
    private final Table originTable;
    private final Column originColumn;
    private final Expr expression;
    
    public Field(String name, Type type, Table originTable, Column originColumn) {
        this.name = name;
        this.type = type;
        this.originTable = originTable;
        this.originColumn = originColumn;
        this.expression = null;
    }
    
    // 用于计算字段（如聚合函数、表达式等）
    public Field(String name, Type type, Expr expression) {
        this.name = name;
        this.type = type;
        this.expression = expression;
        this.originTable = null;
        this.originColumn = null;
    }
    
    public boolean isFromTable() {
        return originTable != null;
    }
    
    public boolean isComputed() {
        return expression != null;
    }
}
```

### 3.2.3 表达式分析器实现

```java
// 表达式分析器
public class ExpressionAnalyzer {
    private final AnalyzerVisitor visitor;
    private final TypeResolver typeResolver;
    private final FunctionAnalyzer functionAnalyzer;
    
    public ExpressionAnalyzer(AnalyzerVisitor visitor) {
        this.visitor = visitor;
        this.typeResolver = new TypeResolver();
        this.functionAnalyzer = new FunctionAnalyzer();
    }
    
    // 表达式分析入口
    public void analyze(Expr expr, ConnectContext context) {
        expr.accept(new ExpressionVisitor(), context);
    }
    
    private class ExpressionVisitor implements AstVisitor<Void, ConnectContext> {
        
        // 列引用分析
        @Override
        public Void visitSlotRef(SlotRef slotRef, ConnectContext context) {
            // 1. 构建完整的列名
            String fullColumnName = buildFullColumnName(slotRef);
            
            // 2. 在当前作用域中解析字段
            Scope currentScope = visitor.analyzeState.getScope();
            Field field = currentScope.resolveField(fullColumnName);
            
            if (field == null) {
                throw new AnalysisException("Column '" + fullColumnName + "' not found");
            }
            
            // 3. 设置类型信息
            slotRef.setType(field.getType());
            
            // 4. 创建SlotDescriptor
            SlotDescriptor slotDesc = new SlotDescriptor(
                SlotId.createSlotId(),
                field.getName(),
                field.getType(),
                field.isNullable()
            );
            slotRef.setDesc(slotDesc);
            
            // 5. 记录列引用
            visitor.analyzeState.addColumnRef(slotRef);
            
            return null;
        }
        
        // 函数调用分析
        @Override
        public Void visitFunctionCall(FunctionCallExpr functionCall, ConnectContext context) {
            // 1. 分析函数参数
            for (Expr arg : functionCall.getParams().exprs()) {
                analyze(arg, context);
            }
            
            // 2. 解析函数
            Function function = functionAnalyzer.resolveFunction(
                functionCall.getFnName().getFunction(),
                functionCall.getParams().exprs().stream()
                    .map(Expr::getType)
                    .collect(Collectors.toList())
            );
            
            if (function == null) {
                throw new AnalysisException("Function '" + 
                    functionCall.getFnName().getFunction() + "' not found");
            }
            
            // 3. 设置函数信息
            functionCall.setFn(function);
            functionCall.setType(function.getReturnType());
            
            // 4. 特殊函数处理
            if (function instanceof AggregateFunction) {
                handleAggregateFunction(functionCall, (AggregateFunction) function, context);
            }
            
            return null;
        }
        
        // 二元表达式分析
        @Override
        public Void visitBinaryPredicate(BinaryPredicate predicate, ConnectContext context) {
            // 1. 分析左右操作数
            analyze(predicate.getChild(0), context);
            analyze(predicate.getChild(1), context);
            
            // 2. 类型检查和转换
            Type leftType = predicate.getChild(0).getType();
            Type rightType = predicate.getChild(1).getType();
            
            // 3. 类型兼容性检查
            if (!typeResolver.isComparable(leftType, rightType)) {
                throw new AnalysisException("Cannot compare " + leftType + " with " + rightType);
            }
            
            // 4. 类型提升
            Type commonType = typeResolver.getCommonType(leftType, rightType);
            if (!leftType.equals(commonType)) {
                predicate.setChild(0, new CastExpr(commonType, predicate.getChild(0)));
            }
            if (!rightType.equals(commonType)) {
                predicate.setChild(1, new CastExpr(commonType, predicate.getChild(1)));
            }
            
            // 5. 设置结果类型
            predicate.setType(Type.BOOLEAN);
            
            return null;
        }
        
        // 聚合函数特殊处理
        private void handleAggregateFunction(FunctionCallExpr functionCall, 
                                           AggregateFunction function, 
                                           ConnectContext context) {
            // 检查聚合函数的使用位置
            if (!visitor.analyzeState.isInAggregateContext()) {
                throw new AnalysisException("Aggregate function '" + 
                    function.getFunctionName() + "' can only be used in aggregate context");
            }
            
            // 标记为聚合表达式
            functionCall.setIsAggregateFunction(true);
            
            // 记录聚合函数信息
            visitor.analyzeState.addAggregateFunction(functionCall);
        }
    }
}
```

## 3.3 元数据管理系统深度解析

### 3.3.1 Catalog系统架构

StarRocks的Catalog系统负责管理所有的元数据信息：

```java
// 全局状态管理器
public class GlobalStateMgr {
    private static final GlobalStateMgr INSTANCE = new GlobalStateMgr();
    
    // 数据库管理
    private final ConcurrentHashMap<String, Database> fullNameToDb = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, Database> idToDb = new ConcurrentHashMap<>();
    
    // 函数管理
    private final FunctionSet functionSet = new FunctionSet();
    
    // 用户和权限管理
    private final Auth auth = new Auth();
    
    // 资源管理
    private final ResourceMgr resourceMgr = new ResourceMgr();
    
    // 系统信息管理
    private final SystemInfoService systemInfo = new SystemInfoService();
    
    // 元数据日志
    private final EditLog editLog = new EditLog();
    
    public static GlobalStateMgr getCurrentState() {
        return INSTANCE;
    }
    
    // 获取数据库
    public Database getDb(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return null;
        }
        return fullNameToDb.get(name);
    }
    
    public Database getDb(long dbId) {
        return idToDb.get(dbId);
    }
    
    // 创建数据库
    public void createDb(CreateDbStmt stmt) throws AnalysisException {
        String dbName = stmt.getFullDbName();
        
        // 检查数据库是否已存在
        if (fullNameToDb.containsKey(dbName)) {
            if (stmt.isSetIfNotExists()) {
                return;
            }
            throw new AnalysisException("Database '" + dbName + "' already exists");
        }
        
        // 创建数据库对象
        long dbId = GlobalStateMgr.getCurrentState().getNextId();
        Database db = new Database(dbId, dbName);
        
        // 添加到管理器
        fullNameToDb.put(dbName, db);
        idToDb.put(dbId, db);
        
        // 记录元数据变更日志
        CreateDbInfo info = new CreateDbInfo(dbName);
        editLog.logCreateDb(info);
        
        LOG.info("Create database[{}] successfully", dbName);
    }
    
    // 删除数据库
    public void dropDb(DropDbStmt stmt) throws AnalysisException {
        String dbName = stmt.getDbName();
        
        Database db = fullNameToDb.get(dbName);
        if (db == null) {
            if (stmt.isSetIfExists()) {
                return;
            }
            throw new AnalysisException("Database '" + dbName + "' does not exist");
        }
        
        // 检查数据库是否为空
        if (!stmt.isForceDrop() && !db.getTables().isEmpty()) {
            throw new AnalysisException("Database '" + dbName + "' is not empty");
        }
        
        // 从管理器中移除
        fullNameToDb.remove(dbName);
        idToDb.remove(db.getId());
        
        // 记录元数据变更日志
        DropDbInfo info = new DropDbInfo(dbName);
        editLog.logDropDb(info);
        
        LOG.info("Drop database[{}] successfully", dbName);
    }
}
```

### 3.3.2 Database和Table管理

```java
// 数据库类
public class Database {
    private final long id;
    private final String fullQualifiedName;
    private final ConcurrentHashMap<String, Table> nameToTable = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, Table> idToTable = new ConcurrentHashMap<>();
    private final ReadWriteLock rwLock = new ReentrantReadWriteLock(true);
    
    public Database(long id, String name) {
        this.id = id;
        this.fullQualifiedName = name;
    }
    
    // 获取表
    public Table getTable(String name) {
        readLock();
        try {
            return nameToTable.get(name);
        } finally {
            readUnlock();
        }
    }
    
    public Table getTable(long tableId) {
        readLock();
        try {
            return idToTable.get(tableId);
        } finally {
            readUnlock();
        }
    }
    
    // 创建表
    public void createTable(Table table) throws AnalysisException {
        writeLock();
        try {
            String tableName = table.getName();
            if (nameToTable.containsKey(tableName)) {
                throw new AnalysisException("Table '" + tableName + "' already exists");
            }
            
            nameToTable.put(tableName, table);
            idToTable.put(table.getId(), table);
            
            LOG.info("Create table[{}] in database[{}] successfully", 
                     tableName, fullQualifiedName);
        } finally {
            writeUnlock();
        }
    }
    
    // 删除表
    public void dropTable(String tableName) throws AnalysisException {
        writeLock();
        try {
            Table table = nameToTable.get(tableName);
            if (table == null) {
                throw new AnalysisException("Table '" + tableName + "' does not exist");
            }
            
            nameToTable.remove(tableName);
            idToTable.remove(table.getId());
            
            LOG.info("Drop table[{}] from database[{}] successfully", 
                     tableName, fullQualifiedName);
        } finally {
            writeUnlock();
        }
    }
    
    // 获取所有表
    public List<Table> getTables() {
        readLock();
        try {
            return new ArrayList<>(nameToTable.values());
        } finally {
            readUnlock();
        }
    }
    
    private void readLock() { rwLock.readLock().lock(); }
    private void readUnlock() { rwLock.readLock().unlock(); }
    private void writeLock() { rwLock.writeLock().lock(); }
    private void writeUnlock() { rwLock.writeLock().unlock(); }
}

// 表基类
public abstract class Table {
    protected long id;
    protected String name;
    protected TableType type;
    protected List<Column> baseSchema = new ArrayList<>();
    protected Map<String, String> properties = new HashMap<>();
    
    public Table(long id, String name, TableType type, List<Column> schema) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.baseSchema = schema;
    }
    
    // 获取列信息
    public Column getColumn(String name) {
        for (Column column : baseSchema) {
            if (column.getName().equalsIgnoreCase(name)) {
                return column;
            }
        }
        return null;
    }
    
    public List<Column> getColumns() {
        return baseSchema;
    }
    
    // 获取键列（用于分桶和排序）
    public abstract List<Column> getKeyColumns();
    
    // 获取值列（用于聚合表）
    public List<Column> getValueColumns() {
        return baseSchema.stream()
            .filter(column -> !getKeyColumns().contains(column))
            .collect(Collectors.toList());
    }
    
    // 表的统计信息
    public abstract long getRowCount();
    public abstract long getDataSize();
}

// OLAP表实现
public class OlapTable extends Table {
    private final KeysType keysType;
    private final PartitionInfo partitionInfo;
    private final DistributionInfo defaultDistributionInfo;
    private final ConcurrentHashMap<Long, Partition> idToPartition = new ConcurrentHashMap<>();
    
    // 索引管理
    private final Map<Long, MaterializedIndex> indexIdToMeta = new HashMap<>();
    private final Map<String, Long> indexNameToId = new HashMap<>();
    
    public OlapTable(long id, String name, List<Column> schema, KeysType keysType) {
        super(id, name, TableType.OLAP, schema);
        this.keysType = keysType;
        this.partitionInfo = new RangePartitionInfo();
        this.defaultDistributionInfo = new HashDistributionInfo();
    }
    
    @Override
    public List<Column> getKeyColumns() {
        return baseSchema.stream()
            .filter(Column::isKey)
            .collect(Collectors.toList());
    }
    
    // 分区管理
    public void addPartition(Partition partition) {
        idToPartition.put(partition.getId(), partition);
    }
    
    public Partition getPartition(long partitionId) {
        return idToPartition.get(partitionId);
    }
    
    public Collection<Partition> getPartitions() {
        return idToPartition.values();
    }
    
    // 物化索引管理
    public void addRollupIndex(MaterializedIndex index) {
        indexIdToMeta.put(index.getId(), index);
        indexNameToId.put(index.getName(), index.getId());
    }
    
    public MaterializedIndex getRollupIndex(long indexId) {
        return indexIdToMeta.get(indexId);
    }
    
    public MaterializedIndex getRollupIndex(String indexName) {
        Long indexId = indexNameToId.get(indexName);
        return indexId != null ? indexIdToMeta.get(indexId) : null;
    }
}
```

### 3.3.3 列定义和类型系统

```java
// 列定义
public class Column {
    private final String name;
    private Type type;
    private boolean isKey;
    private AggregateType aggregateType;
    private boolean isAllowNull;
    private boolean isAutoIncrement;
    private String defaultValue;
    private String comment;
    
    public Column(String name, Type type) {
        this.name = name;
        this.type = type;
        this.isKey = false;
        this.aggregateType = null;
        this.isAllowNull = true;
        this.isAutoIncrement = false;
    }
    
    public Column(String name, Type type, boolean isKey, AggregateType aggregateType,
                  boolean isAllowNull, String defaultValue, String comment) {
        this.name = name;
        this.type = type;
        this.isKey = isKey;
        this.aggregateType = aggregateType;
        this.isAllowNull = isAllowNull;
        this.defaultValue = defaultValue;
        this.comment = comment;
    }
    
    // 类型兼容性检查
    public boolean isCompatible(Column other) {
        // 名称必须相同
        if (!name.equals(other.name)) {
            return false;
        }
        
        // 类型必须兼容
        if (!type.isCompatible(other.type)) {
            return false;
        }
        
        // Key属性必须一致
        if (isKey != other.isKey) {
            return false;
        }
        
        // 聚合类型必须一致
        if (aggregateType != other.aggregateType) {
            return false;
        }
        
        return true;
    }
    
    // 验证列定义
    public void validate() throws AnalysisException {
        // 验证列名
        if (Strings.isNullOrEmpty(name)) {
            throw new AnalysisException("Column name cannot be empty");
        }
        
        // 验证类型
        if (type == null) {
            throw new AnalysisException("Column type cannot be null");
        }
        
        // 验证聚合类型
        if (aggregateType != null) {
            if (!aggregateType.isCompatible(type)) {
                throw new AnalysisException("Aggregate type '" + aggregateType + 
                    "' is not compatible with column type '" + type + "'");
            }
        }
        
        // 验证默认值
        if (defaultValue != null) {
            validateDefaultValue();
        }
    }
    
    private void validateDefaultValue() throws AnalysisException {
        try {
            // 尝试将默认值转换为目标类型
            type.parseFromString(defaultValue);
        } catch (Exception e) {
            throw new AnalysisException("Invalid default value '" + defaultValue + 
                "' for column type '" + type + "'");
        }
    }
}

// 聚合类型枚举
public enum AggregateType {
    NONE("NONE"),
    SUM("SUM"),
    MAX("MAX"),
    MIN("MIN"),
    REPLACE("REPLACE"),
    HLL_UNION("HLL_UNION"),
    BITMAP_UNION("BITMAP_UNION");
    
    private final String name;
    
    AggregateType(String name) {
        this.name = name;
    }
    
    public boolean isCompatible(Type columnType) {
        switch (this) {
            case SUM:
                return columnType.isNumericType();
            case MAX:
            case MIN:
                return columnType.isComparable();
            case REPLACE:
                return true;
            case HLL_UNION:
                return columnType.isHllType();
            case BITMAP_UNION:
                return columnType.isBitmapType();
            default:
                return true;
        }
    }
}
```

## 3.4 类型系统与类型推导

### 3.4.1 类型层次结构

```java
// 类型基类
public abstract class Type {
    protected final PrimitiveType type;
    
    public Type(PrimitiveType type) {
        this.type = type;
    }
    
    // 类型兼容性检查
    public abstract boolean isCompatible(Type other);
    
    // 是否可比较
    public abstract boolean isComparable();
    
    // 是否为数值类型
    public boolean isNumericType() {
        return type.isNumericType();
    }
    
    // 是否可为空
    public boolean isNullable() {
        return true;
    }
    
    // 获取显示大小
    public abstract int getSlotSize();
    
    // 类型转换
    public abstract boolean canCastTo(Type targetType);
    
    // 从字符串解析值
    public abstract Object parseFromString(String value) throws AnalysisException;
}

// 标量类型
public class ScalarType extends Type {
    private int precision = -1;
    private int scale = -1;
    private int length = -1;
    
    protected ScalarType(PrimitiveType type) {
        super(type);
    }
    
    public static ScalarType createType(PrimitiveType type) {
        switch (type) {
            case BOOLEAN:
                return BOOLEAN;
            case TINYINT:
                return TINYINT;
            case SMALLINT:
                return SMALLINT;
            case INT:
                return INT;
            case BIGINT:
                return BIGINT;
            case FLOAT:
                return FLOAT;
            case DOUBLE:
                return DOUBLE;
            case DATE:
                return DATE;
            case DATETIME:
                return DATETIME;
            case CHAR:
                return createCharType(-1);
            case VARCHAR:
                return createVarcharType(-1);
            default:
                throw new IllegalArgumentException("Invalid type: " + type);
        }
    }
    
    // 创建字符类型
    public static ScalarType createCharType(int len) {
        ScalarType type = new ScalarType(PrimitiveType.CHAR);
        type.length = len;
        return type;
    }
    
    public static ScalarType createVarcharType(int len) {
        ScalarType type = new ScalarType(PrimitiveType.VARCHAR);
        type.length = len;
        return type;
    }
    
    // 创建decimal类型
    public static ScalarType createDecimalType(int precision, int scale) {
        ScalarType type = new ScalarType(PrimitiveType.DECIMAL);
        type.precision = precision;
        type.scale = scale;
        return type;
    }
    
    @Override
    public boolean isCompatible(Type other) {
        if (!(other instanceof ScalarType)) {
            return false;
        }
        
        ScalarType otherScalar = (ScalarType) other;
        
        // 相同类型直接兼容
        if (type == otherScalar.type) {
            return true;
        }
        
        // 数值类型之间的兼容性
        if (isNumericType() && otherScalar.isNumericType()) {
            return true;
        }
        
        // 字符类型之间的兼容性
        if (isStringType() && otherScalar.isStringType()) {
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean canCastTo(Type targetType) {
        if (equals(targetType)) {
            return true;
        }
        
        if (!(targetType instanceof ScalarType)) {
            return false;
        }
        
        ScalarType target = (ScalarType) targetType;
        
        // 数值类型之间可以相互转换
        if (isNumericType() && target.isNumericType()) {
            return true;
        }
        
        // 字符串类型可以转换为其他类型
        if (isStringType()) {
            return true;
        }
        
        // 其他类型可以转换为字符串
        if (target.isStringType()) {
            return true;
        }
        
        return false;
    }
}

// 数组类型
public class ArrayType extends Type {
    private final Type itemType;
    
    public ArrayType(Type itemType) {
        super(PrimitiveType.ARRAY);
        this.itemType = itemType;
    }
    
    @Override
    public boolean isCompatible(Type other) {
        if (!(other instanceof ArrayType)) {
            return false;
        }
        
        ArrayType otherArray = (ArrayType) other;
        return itemType.isCompatible(otherArray.itemType);
    }
    
    @Override
    public boolean canCastTo(Type targetType) {
        if (!(targetType instanceof ArrayType)) {
            return false;
        }
        
        ArrayType targetArray = (ArrayType) targetType;
        return itemType.canCastTo(targetArray.itemType);
    }
}
```

### 3.4.2 类型解析器实现

```java
// 类型解析器
public class TypeResolver {
    
    // 获取两个类型的公共类型
    public Type getCommonType(Type type1, Type type2) {
        if (type1.equals(type2)) {
            return type1;
        }
        
        // 空值类型处理
        if (type1.isNull()) {
            return type2;
        }
        if (type2.isNull()) {
            return type1;
        }
        
        // 数值类型提升
        if (type1.isNumericType() && type2.isNumericType()) {
            return getNumericCommonType((ScalarType) type1, (ScalarType) type2);
        }
        
        // 字符串类型处理
        if (type1.isStringType() && type2.isStringType()) {
            return getStringCommonType((ScalarType) type1, (ScalarType) type2);
        }
        
        // 日期时间类型处理
        if (type1.isDateType() && type2.isDateType()) {
            return getDateCommonType((ScalarType) type1, (ScalarType) type2);
        }
        
        // 数组类型处理
        if (type1.isArrayType() && type2.isArrayType()) {
            ArrayType array1 = (ArrayType) type1;
            ArrayType array2 = (ArrayType) type2;
            Type commonItemType = getCommonType(array1.getItemType(), array2.getItemType());
            return new ArrayType(commonItemType);
        }
        
        // 无法找到公共类型
        return null;
    }
    
    // 数值类型提升规则
    private Type getNumericCommonType(ScalarType type1, ScalarType type2) {
        PrimitiveType pt1 = type1.getPrimitiveType();
        PrimitiveType pt2 = type2.getPrimitiveType();
        
        // 类型提升优先级：DOUBLE > FLOAT > BIGINT > INT > SMALLINT > TINYINT
        PrimitiveType[] promotionOrder = {
            PrimitiveType.TINYINT,
            PrimitiveType.SMALLINT,
            PrimitiveType.INT,
            PrimitiveType.BIGINT,
            PrimitiveType.FLOAT,
            PrimitiveType.DOUBLE
        };
        
        int rank1 = getTypeRank(pt1, promotionOrder);
        int rank2 = getTypeRank(pt2, promotionOrder);
        
        // 返回级别更高的类型
        return ScalarType.createType(promotionOrder[Math.max(rank1, rank2)]);
    }
    
    private int getTypeRank(PrimitiveType type, PrimitiveType[] order) {
        for (int i = 0; i < order.length; i++) {
            if (order[i] == type) {
                return i;
            }
        }
        return -1;
    }
    
    // 字符串类型处理
    private Type getStringCommonType(ScalarType type1, ScalarType type2) {
        // VARCHAR的长度取较大值
        if (type1.getPrimitiveType() == PrimitiveType.VARCHAR &&
            type2.getPrimitiveType() == PrimitiveType.VARCHAR) {
            int maxLen = Math.max(type1.getLength(), type2.getLength());
            return ScalarType.createVarcharType(maxLen);
        }
        
        // CHAR和VARCHAR的混合，转换为VARCHAR
        if ((type1.getPrimitiveType() == PrimitiveType.CHAR ||
             type1.getPrimitiveType() == PrimitiveType.VARCHAR) &&
            (type2.getPrimitiveType() == PrimitiveType.CHAR ||
             type2.getPrimitiveType() == PrimitiveType.VARCHAR)) {
            int maxLen = Math.max(type1.getLength(), type2.getLength());
            return ScalarType.createVarcharType(maxLen);
        }
        
        // 默认返回VARCHAR
        return ScalarType.createVarcharType(-1);
    }
    
    // 检查类型是否可比较
    public boolean isComparable(Type type1, Type type2) {
        // 相同类型可比较
        if (type1.equals(type2)) {
            return true;
        }
        
        // 数值类型之间可比较
        if (type1.isNumericType() && type2.isNumericType()) {
            return true;
        }
        
        // 字符串类型之间可比较
        if (type1.isStringType() && type2.isStringType()) {
            return true;
        }
        
        // 日期时间类型之间可比较
        if (type1.isDateType() && type2.isDateType()) {
            return true;
        }
        
        return false;
    }
    
    // 类型转换插入
    public Expr insertCast(Expr expr, Type targetType) {
        if (expr.getType().equals(targetType)) {
            return expr;
        }
        
        if (!expr.getType().canCastTo(targetType)) {
            throw new AnalysisException("Cannot cast " + expr.getType() + " to " + targetType);
        }
        
        return new CastExpr(targetType, expr);
    }
}
```

## 3.5 权限检查机制

### 3.5.1 权限管理架构

```java
// 权限管理器
public class Auth {
    private final UserPropertyMgr userPropertyMgr = new UserPropertyMgr();
    private final Map<String, User> nameToUser = new HashMap<>();
    private final Map<String, Role> nameToRole = new HashMap<>();
    
    // 检查表权限
    public void checkTblPriv(ConnectContext ctx, String db, String tbl, PrivilegeType priv) 
            throws AnalysisException {
        UserIdentity currentUser = ctx.getCurrentUserIdentity();
        
        // 超级用户跳过权限检查
        if (isSuperUser(currentUser)) {
            return;
        }
        
        // 检查用户权限
        if (checkUserPrivilege(currentUser, db, tbl, priv)) {
            return;
        }
        
        // 检查角色权限
        List<String> roles = getUserRoles(currentUser);
        for (String roleName : roles) {
            if (checkRolePrivilege(roleName, db, tbl, priv)) {
                return;
            }
        }
        
        // 权限不足
        throw new AnalysisException("Access denied for user '" + currentUser.getQualifiedUser() + 
            "' to table '" + db + "." + tbl + "' with privilege '" + priv + "'");
    }
    
    // 检查数据库权限
    public void checkDbPriv(ConnectContext ctx, String db, PrivilegeType priv) 
            throws AnalysisException {
        UserIdentity currentUser = ctx.getCurrentUserIdentity();
        
        if (isSuperUser(currentUser)) {
            return;
        }
        
        if (checkUserDbPrivilege(currentUser, db, priv)) {
            return;
        }
        
        List<String> roles = getUserRoles(currentUser);
        for (String roleName : roles) {
            if (checkRoleDbPrivilege(roleName, db, priv)) {
                return;
            }
        }
        
        throw new AnalysisException("Access denied for user '" + currentUser.getQualifiedUser() + 
            "' to database '" + db + "' with privilege '" + priv + "'");
    }
    
    // 检查全局权限
    public void checkGlobalPriv(ConnectContext ctx, PrivilegeType priv) 
            throws AnalysisException {
        UserIdentity currentUser = ctx.getCurrentUserIdentity();
        
        if (isSuperUser(currentUser)) {
            return;
        }
        
        if (checkUserGlobalPrivilege(currentUser, priv)) {
            return;
        }
        
        List<String> roles = getUserRoles(currentUser);
        for (String roleName : roles) {
            if (checkRoleGlobalPrivilege(roleName, priv)) {
                return;
            }
        }
        
        throw new AnalysisException("Access denied for user '" + currentUser.getQualifiedUser() + 
            "' with global privilege '" + priv + "'");
    }
    
    private boolean checkUserPrivilege(UserIdentity user, String db, String tbl, PrivilegeType priv) {
        User userObj = nameToUser.get(user.getQualifiedUser());
        if (userObj == null) {
            return false;
        }
        
        return userObj.hasPrivilege(db, tbl, priv);
    }
    
    private boolean isSuperUser(UserIdentity user) {
        return "root".equals(user.getQualifiedUser()) || 
               checkUserGlobalPrivilege(user, PrivilegeType.ADMIN);
    }
}

// 用户类
public class User {
    private final String name;
    private final String host;
    private final byte[] password;
    private final Set<PrivilegeEntry> privileges = new HashSet<>();
    private final Set<String> roles = new HashSet<>();
    
    public User(String name, String host, byte[] password) {
        this.name = name;
        this.host = host;
        this.password = password;
    }
    
    // 检查是否有特定权限
    public boolean hasPrivilege(String db, String table, PrivilegeType priv) {
        // 检查全局权限
        if (hasGlobalPrivilege(priv)) {
            return true;
        }
        
        // 检查数据库级权限
        if (hasDbPrivilege(db, priv)) {
            return true;
        }
        
        // 检查表级权限
        return hasTablePrivilege(db, table, priv);
    }
    
    private boolean hasGlobalPrivilege(PrivilegeType priv) {
        return privileges.stream()
            .anyMatch(entry -> entry.isGlobal() && entry.hasPrivilege(priv));
    }
    
    private boolean hasDbPrivilege(String db, PrivilegeType priv) {
        return privileges.stream()
            .anyMatch(entry -> entry.isDbLevel() && 
                      entry.getDb().equals(db) && 
                      entry.hasPrivilege(priv));
    }
    
    private boolean hasTablePrivilege(String db, String table, PrivilegeType priv) {
        return privileges.stream()
            .anyMatch(entry -> entry.isTableLevel() && 
                      entry.getDb().equals(db) && 
                      entry.getTable().equals(table) && 
                      entry.hasPrivilege(priv));
    }
    
    // 授予权限
    public void grantPrivilege(PrivilegeEntry privilege) {
        privileges.add(privilege);
    }
    
    // 撤销权限
    public void revokePrivilege(PrivilegeEntry privilege) {
        privileges.remove(privilege);
    }
    
    // 添加角色
    public void addRole(String roleName) {
        roles.add(roleName);
    }
    
    // 移除角色
    public void removeRole(String roleName) {
        roles.remove(roleName);
    }
}

// 权限条目
public class PrivilegeEntry {
    private final PrivilegeLevel level;
    private final String db;
    private final String table;
    private final Set<PrivilegeType> privileges;
    
    public PrivilegeEntry(PrivilegeLevel level, String db, String table, 
                         Set<PrivilegeType> privileges) {
        this.level = level;
        this.db = db;
        this.table = table;
        this.privileges = privileges;
    }
    
    public boolean hasPrivilege(PrivilegeType priv) {
        return privileges.contains(priv) || privileges.contains(PrivilegeType.ALL);
    }
    
    public boolean isGlobal() {
        return level == PrivilegeLevel.GLOBAL;
    }
    
    public boolean isDbLevel() {
        return level == PrivilegeLevel.DATABASE;
    }
    
    public boolean isTableLevel() {
        return level == PrivilegeLevel.TABLE;
    }
}

// 权限类型枚举
public enum PrivilegeType {
    SELECT,
    INSERT,
    UPDATE,
    DELETE,
    CREATE,
    DROP,
    ALTER,
    INDEX,
    ADMIN,
    ALL
}

// 权限级别枚举
public enum PrivilegeLevel {
    GLOBAL,
    DATABASE,
    TABLE
}
```

## 3.6 函数分析与解析

### 3.6.1 函数注册系统

```java
// 函数集合管理
public class FunctionSet {
    private final Map<String, List<Function>> functions = new HashMap<>();
    private final Map<String, AggregateFunction> aggregateFunctions = new HashMap<>();
    private final Map<String, ScalarFunction> scalarFunctions = new HashMap<>();
    
    public FunctionSet() {
        initBuiltinFunctions();
    }
    
    // 初始化内置函数
    private void initBuiltinFunctions() {
        // 聚合函数
        addAggregateFunction("count", new CountFunction());
        addAggregateFunction("sum", new SumFunction());
        addAggregateFunction("avg", new AvgFunction());
        addAggregateFunction("max", new MaxFunction());
        addAggregateFunction("min", new MinFunction());
        
        // 数学函数
        addScalarFunction("abs", new AbsFunction());
        addScalarFunction("round", new RoundFunction());
        addScalarFunction("floor", new FloorFunction());
        addScalarFunction("ceil", new CeilFunction());
        
        // 字符串函数
        addScalarFunction("length", new LengthFunction());
        addScalarFunction("upper", new UpperFunction());
        addScalarFunction("lower", new LowerFunction());
        addScalarFunction("substr", new SubstringFunction());
        
        // 日期函数
        addScalarFunction("now", new NowFunction());
        addScalarFunction("date_format", new DateFormatFunction());
        addScalarFunction("date_add", new DateAddFunction());
        addScalarFunction("date_sub", new DateSubFunction());
    }
    
    // 添加聚合函数
    public void addAggregateFunction(String name, AggregateFunction function) {
        aggregateFunctions.put(name.toLowerCase(), function);
        addFunction(name, function);
    }
    
    // 添加标量函数
    public void addScalarFunction(String name, ScalarFunction function) {
        scalarFunctions.put(name.toLowerCase(), function);
        addFunction(name, function);
    }
    
    // 添加函数到总函数列表
    private void addFunction(String name, Function function) {
        String lowerName = name.toLowerCase();
        functions.computeIfAbsent(lowerName, k -> new ArrayList<>()).add(function);
    }
    
    // 函数解析
    public Function getFunction(String name, List<Type> argTypes) {
        String lowerName = name.toLowerCase();
        List<Function> candidates = functions.get(lowerName);
        
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }
        
        // 精确匹配
        for (Function function : candidates) {
            if (function.matchesSignature(argTypes)) {
                return function;
            }
        }
        
        // 类型兼容匹配
        for (Function function : candidates) {
            if (function.isCompatible(argTypes)) {
                return function;
            }
        }
        
        return null;
    }
}

// 函数基类
public abstract class Function {
    protected final String functionName;
    protected final List<Type> argTypes;
    protected final Type returnType;
    protected final boolean hasVarArgs;
    
    public Function(String functionName, List<Type> argTypes, Type returnType) {
        this(functionName, argTypes, returnType, false);
    }
    
    public Function(String functionName, List<Type> argTypes, Type returnType, boolean hasVarArgs) {
        this.functionName = functionName;
        this.argTypes = argTypes;
        this.returnType = returnType;
        this.hasVarArgs = hasVarArgs;
    }
    
    // 检查函数签名是否精确匹配
    public boolean matchesSignature(List<Type> callArgTypes) {
        if (hasVarArgs) {
            return callArgTypes.size() >= argTypes.size() - 1;
        } else {
            return callArgTypes.size() == argTypes.size() && 
                   typesMatch(callArgTypes, argTypes);
        }
    }
    
    // 检查函数是否兼容（允许类型转换）
    public boolean isCompatible(List<Type> callArgTypes) {
        if (hasVarArgs) {
            if (callArgTypes.size() < argTypes.size() - 1) {
                return false;
            }
        } else {
            if (callArgTypes.size() != argTypes.size()) {
                return false;
            }
        }
        
        // 检查每个参数的兼容性
        for (int i = 0; i < Math.min(callArgTypes.size(), argTypes.size()); i++) {
            Type callType = callArgTypes.get(i);
            Type expectedType = argTypes.get(i);
            
            if (!callType.canCastTo(expectedType)) {
                return false;
            }
        }
        
        return true;
    }
    
    private boolean typesMatch(List<Type> types1, List<Type> types2) {
        if (types1.size() != types2.size()) {
            return false;
        }
        
        for (int i = 0; i < types1.size(); i++) {
            if (!types1.get(i).equals(types2.get(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    public String getFunctionName() { return functionName; }
    public List<Type> getArgTypes() { return argTypes; }
    public Type getReturnType() { return returnType; }
    public boolean hasVarArgs() { return hasVarArgs; }
}

// 聚合函数
public abstract class AggregateFunction extends Function {
    protected final boolean isDistinct;
    
    public AggregateFunction(String name, List<Type> argTypes, Type returnType) {
        this(name, argTypes, returnType, false);
    }
    
    public AggregateFunction(String name, List<Type> argTypes, Type returnType, boolean isDistinct) {
        super(name, argTypes, returnType);
        this.isDistinct = isDistinct;
    }
    
    public boolean isDistinct() { return isDistinct; }
    
    // 聚合函数需要实现的方法
    public abstract Expr createAggregateExpr(List<Expr> args);
}

// 标量函数
public abstract class ScalarFunction extends Function {
    
    public ScalarFunction(String name, List<Type> argTypes, Type returnType) {
        super(name, argTypes, returnType);
    }
    
    // 标量函数可以进行常量折叠
    public abstract boolean isConstantFoldable();
    
    // 常量求值
    public abstract Object evaluateConstant(List<Object> args) throws AnalysisException;
}
```

### 3.6.2 函数分析器实现

```java
// 函数分析器
public class FunctionAnalyzer {
    private final FunctionSet functionSet;
    private final TypeResolver typeResolver;
    
    public FunctionAnalyzer() {
        this.functionSet = GlobalStateMgr.getCurrentState().getFunctionSet();
        this.typeResolver = new TypeResolver();
    }
    
    // 解析函数调用
    public Function resolveFunction(String functionName, List<Type> argTypes) {
        // 1. 尝试内置函数
        Function function = functionSet.getFunction(functionName, argTypes);
        if (function != null) {
            return function;
        }
        
        // 2. 尝试用户定义函数
        function = resolveUserDefinedFunction(functionName, argTypes);
        if (function != null) {
            return function;
        }
        
        // 3. 尝试类型转换后的匹配
        function = resolveFunctionWithCast(functionName, argTypes);
        if (function != null) {
            return function;
        }
        
        return null;
    }
    
    // 解析用户定义函数
    private Function resolveUserDefinedFunction(String functionName, List<Type> argTypes) {
        Database db = GlobalStateMgr.getCurrentState().getDb("_udf_");
        if (db == null) {
            return null;
        }
        
        // 查找匹配的UDF
        List<UserDefinedFunction> udfs = db.getFunctions(functionName);
        for (UserDefinedFunction udf : udfs) {
            if (udf.isCompatible(argTypes)) {
                return udf;
            }
        }
        
        return null;
    }
    
    // 通过类型转换解析函数
    private Function resolveFunctionWithCast(String functionName, List<Type> argTypes) {
        List<Function> candidates = functionSet.getFunctions(functionName);
        if (candidates == null) {
            return null;
        }
        
        for (Function candidate : candidates) {
            List<Type> candidateArgTypes = candidate.getArgTypes();
            
            // 检查是否可以通过类型转换匹配
            if (canMatchWithCast(argTypes, candidateArgTypes)) {
                return candidate;
            }
        }
        
        return null;
    }
    
    private boolean canMatchWithCast(List<Type> fromTypes, List<Type> toTypes) {
        if (fromTypes.size() != toTypes.size()) {
            return false;
        }
        
        for (int i = 0; i < fromTypes.size(); i++) {
            if (!fromTypes.get(i).canCastTo(toTypes.get(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    // 分析聚合函数调用
    public void analyzeAggregateFunction(FunctionCallExpr functionCall, 
                                       ConnectContext context) throws AnalysisException {
        String functionName = functionCall.getFnName().getFunction();
        List<Expr> args = functionCall.getParams().exprs();
        List<Type> argTypes = args.stream().map(Expr::getType).collect(Collectors.toList());
        
        // 解析聚合函数
        AggregateFunction function = (AggregateFunction) resolveFunction(functionName, argTypes);
        if (function == null) {
            throw new AnalysisException("Unknown aggregate function: " + functionName);
        }
        
        // 设置函数信息
        functionCall.setFn(function);
        functionCall.setType(function.getReturnType());
        
        // 检查DISTINCT修饰符
        if (functionCall.getParams().isDistinct()) {
            if (!function.supportsDistinct()) {
                throw new AnalysisException("Function '" + functionName + 
                    "' does not support DISTINCT modifier");
            }
        }
        
        // 验证聚合函数的使用上下文
        validateAggregateContext(functionCall, context);
    }
    
    private void validateAggregateContext(FunctionCallExpr functionCall, 
                                        ConnectContext context) throws AnalysisException {
        // 检查是否在允许聚合函数的上下文中
        if (!context.isInAggregateContext()) {
            throw new AnalysisException("Aggregate function '" + 
                functionCall.getFnName().getFunction() + 
                "' cannot be used outside of aggregate context");
        }
        
        // 检查是否在窗口函数中
        if (context.isInWindowFunction()) {
            throw new AnalysisException("Aggregate function cannot be used in window function");
        }
    }
}
```

## 3.7 查询语义验证

### 3.7.1 语义约束检查

```java
// 语义验证器
public class SemanticValidator {
    
    // 验证SELECT语句
    public void validateSelectStatement(SelectRelation selectRelation) throws AnalysisException {
        // 1. 验证GROUP BY语义
        validateGroupBySemantics(selectRelation);
        
        // 2. 验证聚合函数使用
        validateAggregateUsage(selectRelation);
        
        // 3. 验证窗口函数使用
        validateWindowFunctionUsage(selectRelation);
        
        // 4. 验证ORDER BY语义
        validateOrderBySemantics(selectRelation);
        
        // 5. 验证HAVING子句
        validateHavingClause(selectRelation);
    }
    
    // 验证GROUP BY语义
    private void validateGroupBySemantics(SelectRelation selectRelation) throws AnalysisException {
        GroupByClause groupBy = selectRelation.getGroupByClause();
        if (groupBy == null) {
            return;
        }
        
        List<Expr> groupingExprs = groupBy.getGroupingExprs();
        List<Expr> selectExprs = selectRelation.getSelectList().getItems()
            .stream()
            .map(SelectListItem::getExpr)
            .collect(Collectors.toList());
        
        // 检查SELECT列表中的非聚合表达式是否在GROUP BY中
        for (Expr selectExpr : selectExprs) {
            if (!isAggregateExpr(selectExpr) && !isInGroupBy(selectExpr, groupingExprs)) {
                throw new AnalysisException("Expression '" + selectExpr.toSql() + 
                    "' must appear in GROUP BY clause or be used in aggregate function");
            }
        }
    }
    
    // 验证聚合函数使用
    private void validateAggregateUsage(SelectRelation selectRelation) throws AnalysisException {
        boolean hasGroupBy = selectRelation.getGroupByClause() != null;
        boolean hasAggregateInSelect = hasAggregateFunction(selectRelation.getSelectList());
        
        // 如果SELECT中有聚合函数但没有GROUP BY，则SELECT中所有表达式都必须是聚合函数
        if (hasAggregateInSelect && !hasGroupBy) {
            for (SelectListItem item : selectRelation.getSelectList().getItems()) {
                Expr expr = item.getExpr();
                if (!isAggregateExpr(expr) && !expr.isConstant()) {
                    throw new AnalysisException("Non-aggregate expression '" + expr.toSql() + 
                        "' cannot be used with aggregate function without GROUP BY");
                }
            }
        }
    }
    
    // 验证窗口函数使用
    private void validateWindowFunctionUsage(SelectRelation selectRelation) throws AnalysisException {
        List<Expr> windowFunctions = findWindowFunctions(selectRelation);
        
        for (Expr windowFunc : windowFunctions) {
            AnalyticExpr analyticExpr = (AnalyticExpr) windowFunc;
            
            // 验证窗口规范
            validateWindowSpec(analyticExpr.getWindow());
            
            // 验证函数是否支持窗口操作
            Function function = analyticExpr.getFnCall().getFn();
            if (!function.supportsWindowing()) {
                throw new AnalysisException("Function '" + function.getFunctionName() + 
                    "' does not support window operations");
            }
        }
    }
    
    // 验证ORDER BY语义
    private void validateOrderBySemantics(SelectRelation selectRelation) throws AnalysisException {
        if (selectRelation.getOrderByElements() == null) {
            return;
        }
        
        for (OrderByElement orderBy : selectRelation.getOrderByElements()) {
            Expr expr = orderBy.getExpr();
            
            // ORDER BY表达式必须在SELECT列表中或者是聚合表达式
            if (!isInSelectList(expr, selectRelation.getSelectList()) && 
                !isAggregateExpr(expr)) {
                throw new AnalysisException("ORDER BY expression '" + expr.toSql() + 
                    "' must appear in SELECT list");
            }
            
            // 检查ORDER BY表达式的类型是否可排序
            if (!expr.getType().isComparable()) {
                throw new AnalysisException("ORDER BY expression type '" + expr.getType() + 
                    "' is not comparable");
            }
        }
    }
    
    // 检查表达式是否为聚合表达式
    private boolean isAggregateExpr(Expr expr) {
        AggregateDetector detector = new AggregateDetector();
        return detector.containsAggregate(expr);
    }
    
    // 检查表达式是否在GROUP BY中
    private boolean isInGroupBy(Expr expr, List<Expr> groupingExprs) {
        for (Expr groupingExpr : groupingExprs) {
            if (expr.equals(groupingExpr)) {
                return true;
            }
        }
        return false;
    }
    
    // 聚合检测器
    private static class AggregateDetector implements ExprVisitor<Boolean, Void> {
        public boolean containsAggregate(Expr expr) {
            return expr.accept(this, null);
        }
        
        @Override
        public Boolean visitFunctionCall(FunctionCallExpr node, Void context) {
            if (node.getFn() instanceof AggregateFunction) {
                return true;
            }
            
            // 检查参数中是否包含聚合函数
            for (Expr arg : node.getParams().exprs()) {
                if (arg.accept(this, context)) {
                    return true;
                }
            }
            
            return false;
        }
        
        @Override
        public Boolean visitSlotRef(SlotRef node, Void context) {
            return false;
        }
        
        @Override
        public Boolean visitLiteral(LiteralExpr node, Void context) {
            return false;
        }
        
        // 其他visit方法的默认实现
    }
}
```

## 3.8 分析状态管理

### 3.8.1 AnalyzeState设计

```java
// 分析状态管理器
public class AnalyzeState {
    // 当前作用域
    private Scope currentScope;
    
    // 表引用列表
    private final List<TableRef> tableRefs = new ArrayList<>();
    
    // 列引用列表
    private final List<SlotRef> columnRefs = new ArrayList<>();
    
    // 聚合函数列表
    private final List<FunctionCallExpr> aggregateFunctions = new ArrayList<>();
    
    // 窗口函数列表
    private final List<AnalyticExpr> windowFunctions = new ArrayList<>();
    
    // 子查询列表
    private final List<SubqueryExpr> subqueries = new ArrayList<>();
    
    // 分析上下文栈
    private final Stack<AnalysisContext> contextStack = new Stack<>();
    
    // 错误收集器
    private final List<AnalysisException> errors = new ArrayList<>();
    
    // 当前作用域管理
    public void setScope(Scope scope) {
        this.currentScope = scope;
    }
    
    public Scope getScope() {
        return currentScope;
    }
    
    // 表引用管理
    public void addTableRef(TableRef tableRef) {
        tableRefs.add(tableRef);
    }
    
    public List<TableRef> getTableRefs() {
        return Collections.unmodifiableList(tableRefs);
    }
    
    // 列引用管理
    public void addColumnRef(SlotRef columnRef) {
        columnRefs.add(columnRef);
    }
    
    public List<SlotRef> getColumnRefs() {
        return Collections.unmodifiableList(columnRefs);
    }
    
    // 聚合函数管理
    public void addAggregateFunction(FunctionCallExpr aggregateFunc) {
        aggregateFunctions.add(aggregateFunc);
    }
    
    public boolean hasAggregateFunction() {
        return !aggregateFunctions.isEmpty();
    }
    
    // 上下文管理
    public void pushContext(AnalysisContext context) {
        contextStack.push(context);
    }
    
    public AnalysisContext popContext() {
        return contextStack.pop();
    }
    
    public AnalysisContext getCurrentContext() {
        return contextStack.isEmpty() ? null : contextStack.peek();
    }
    
    public boolean isInAggregateContext() {
        return getCurrentContext() != null && 
               getCurrentContext().getType() == AnalysisContext.Type.AGGREGATE;
    }
    
    public boolean isInSubqueryContext() {
        return getCurrentContext() != null && 
               getCurrentContext().getType() == AnalysisContext.Type.SUBQUERY;
    }
    
    // 错误管理
    public void addError(AnalysisException error) {
        errors.add(error);
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public List<AnalysisException> getErrors() {
        return Collections.unmodifiableList(errors);
    }
    
    // 清理状态
    public void clear() {
        currentScope = null;
        tableRefs.clear();
        columnRefs.clear();
        aggregateFunctions.clear();
        windowFunctions.clear();
        subqueries.clear();
        contextStack.clear();
        errors.clear();
    }
}

// 分析上下文
public class AnalysisContext {
    public enum Type {
        QUERY,      // 查询上下文
        SUBQUERY,   // 子查询上下文
        AGGREGATE,  // 聚合上下文
        WINDOW,     // 窗口函数上下文
        WHERE,      // WHERE子句上下文
        HAVING,     // HAVING子句上下文
        ORDER_BY    // ORDER BY子句上下文
    }
    
    private final Type type;
    private final Map<String, Object> properties = new HashMap<>();
    
    public AnalysisContext(Type type) {
        this.type = type;
    }
    
    public Type getType() {
        return type;
    }
    
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, Class<T> clazz) {
        Object value = properties.get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
}
```

## 总结

语义分析是StarRocks查询处理的核心环节，它将语法正确的AST转换为语义明确、类型安全的中间表示。通过深入分析StarRocks的语义分析器实现，我们可以看到：

1. **完善的架构设计**: 基于访问者模式的多阶段分析架构，职责清晰，易于维护和扩展

2. **强大的元数据管理**: 通过Catalog系统统一管理数据库、表、列等元数据，支持复杂的企业级需求

3. **精确的类型系统**: 支持丰富的数据类型和智能的类型推导，确保查询的类型安全

4. **健全的权限机制**: 细粒度的权限控制，支持用户、角色、多级权限管理

5. **灵活的函数系统**: 支持内置函数、用户定义函数，具有完善的函数解析和类型匹配机制

6. **严格的语义验证**: 全面的语义约束检查，确保SQL语句的正确性

这些设计充分体现了现代数据库系统在语义分析方面的先进理念，为后续的查询优化和执行奠定了坚实的基础。在下一章中，我们将深入分析StarRocks的查询优化器，看看它是如何基于语义分析的结果进行智能的查询优化的。