# 第六章：查询计划生成与分片

## 引言

查询计划生成是将优化后的逻辑计划转换为可执行物理计划的关键步骤。StarRocks采用Fragment-based的分布式执行模型，通过智能的分片策略和计划生成算法，实现了高效的分布式查询执行。本章将深入分析StarRocks的查询计划生成机制和分片策略。

## 6.1 计划生成架构概览

### 6.1.1 生成流程架构

StarRocks的查询计划生成采用自顶向下的转换模式：

```
逻辑计划 → 物理计划 → Fragment分片 → 执行计划 → 调度信息
    ↓        ↓        ↓         ↓        ↓
  优化结果 → 算子转换 → 分布式分片 → 执行单元 → 任务分配
```

### 6.1.2 核心组件分析

基于`StatementPlanner.java`的源码分析：

```java
public class StatementPlanner {
    
    public ExecPlan plan(StatementBase stmt, ConnectContext connectContext) {
        if (stmt instanceof QueryStatement) {
            return planQuery((QueryStatement) stmt, connectContext);
        } else if (stmt instanceof InsertStmt) {
            return planInsert((InsertStmt) stmt, connectContext);
        } else if (stmt instanceof UpdateStmt) {
            return planUpdate((UpdateStmt) stmt, connectContext);
        } else if (stmt instanceof DeleteStmt) {
            return planDelete((DeleteStmt) stmt, connectContext);
        }
        
        throw new StarRocksPlannerException("Unsupported statement type: " + stmt.getClass());
    }
    
    private ExecPlan planQuery(QueryStatement queryStmt, ConnectContext connectContext) {
        // 1. 语义分析
        Analyzer.analyze(queryStmt, connectContext);
        
        // 2. 查询优化
        OptimizerContext optimizerContext = new OptimizerContext(connectContext);
        OptExpression logicalPlan = new LogicalPlan(queryStmt, optimizerContext).getPlan();
        OptExpression optimizedPlan = new Optimizer().optimize(logicalPlan, optimizerContext);
        
        // 3. 物理计划生成
        PhysicalPlan physicalPlan = new PhysicalPlanBuilder().build(optimizedPlan, optimizerContext);
        
        // 4. Fragment分片
        List<PlanFragment> fragments = new Fragmenter().fragment(physicalPlan);
        
        // 5. 执行计划构建
        ExecPlan execPlan = new ExecPlan(connectContext, fragments);
        
        // 6. 计划后处理
        postProcessPlan(execPlan, connectContext);
        
        return execPlan;
    }
    
    private void postProcessPlan(ExecPlan execPlan, ConnectContext connectContext) {
        // 1. 资源估算
        ResourceEstimator.estimate(execPlan);
        
        // 2. 并行度调整
        ParallelismAdjuster.adjust(execPlan, connectContext);
        
        // 3. 调度信息生成
        ScheduleInfoGenerator.generate(execPlan);
    }
}
```

这个计划生成流程体现了分布式查询处理的系统性方法：
- **逻辑到物理转换**: 将逻辑算子转换为物理算子
- **分布式分片**: 将物理计划分解为可并行执行的Fragment
- **资源管理**: 估算和分配执行资源
- **调度优化**: 生成高效的执行调度策略

## 6.2 物理计划生成

### 6.2.1 算子转换机制

逻辑算子到物理算子的转换：

```java
public class PhysicalPlanBuilder {
    
    public PhysicalPlan build(OptExpression logicalPlan, OptimizerContext context) {
        PlanNode physicalRoot = convertToPhysical(logicalPlan, context);
        return new PhysicalPlan(physicalRoot);
    }
    
    private PlanNode convertToPhysical(OptExpression expression, OptimizerContext context) {
        Operator operator = expression.getOp();
        
        if (operator instanceof LogicalScanOperator) {
            return convertScanOperator((LogicalScanOperator) operator, expression, context);
        } else if (operator instanceof LogicalJoinOperator) {
            return convertJoinOperator((LogicalJoinOperator) operator, expression, context);
        } else if (operator instanceof LogicalAggregationOperator) {
            return convertAggregationOperator((LogicalAggregationOperator) operator, expression, context);
        } else if (operator instanceof LogicalProjectOperator) {
            return convertProjectOperator((LogicalProjectOperator) operator, expression, context);
        } else if (operator instanceof LogicalFilterOperator) {
            return convertFilterOperator((LogicalFilterOperator) operator, expression, context);
        }
        
        throw new StarRocksPlannerException("Unsupported logical operator: " + operator);
    }
    
    private PlanNode convertScanOperator(LogicalScanOperator logicalScan,
                                       OptExpression expression,
                                       OptimizerContext context) {
        
        Table table = logicalScan.getTable();
        
        if (table instanceof OlapTable) {
            return createOlapScanNode((OlapTable) table, logicalScan, context);
        } else if (table instanceof ExternalTable) {
            return createExternalScanNode((ExternalTable) table, logicalScan, context);
        } else if (table instanceof MaterializedView) {
            return createMaterializedViewScanNode((MaterializedView) table, logicalScan, context);
        }
        
        throw new StarRocksPlannerException("Unsupported table type: " + table.getClass());
    }
    
    private OlapScanNode createOlapScanNode(OlapTable table,
                                          LogicalScanOperator logicalScan,
                                          OptimizerContext context) {
        
        // 1. 创建扫描节点
        OlapScanNode scanNode = new OlapScanNode(
            context.getNextNodeId(),
            new TupleDescriptor(context.getNextTupleId()),
            table.getName());
        
        // 2. 设置扫描列
        List<SlotDescriptor> slots = createSlotDescriptors(logicalScan.getOutputColumns(), table);
        scanNode.getTupleDesc().setSlots(slots);
        
        // 3. 设置分区裁剪
        List<Long> selectedPartitionIds = selectPartitions(table, logicalScan.getPredicate());
        scanNode.setSelectedPartitionIds(selectedPartitionIds);
        
        // 4. 设置Tablet选择
        List<Long> selectedTabletIds = selectTablets(table, selectedPartitionIds, logicalScan.getPredicate());
        scanNode.setSelectedTabletIds(selectedTabletIds);
        
        // 5. 设置谓词下推
        List<Expr> pushDownPredicates = extractPushDownPredicates(logicalScan.getPredicate());
        scanNode.setConjuncts(pushDownPredicates);
        
        return scanNode;
    }
}
```

### 6.2.2 连接算子转换

连接算子的物理实现选择：

```java
private PlanNode convertJoinOperator(LogicalJoinOperator logicalJoin,
                                   OptExpression expression,
                                   OptimizerContext context) {
    
    // 1. 转换子节点
    PlanNode leftChild = convertToPhysical(expression.getInputs().get(0), context);
    PlanNode rightChild = convertToPhysical(expression.getInputs().get(1), context);
    
    // 2. 选择连接算法
    JoinAlgorithm algorithm = selectJoinAlgorithm(logicalJoin, leftChild, rightChild, context);
    
    // 3. 创建物理连接节点
    switch (algorithm) {
        case HASH_JOIN:
            return createHashJoinNode(logicalJoin, leftChild, rightChild, context);
        case MERGE_JOIN:
            return createMergeJoinNode(logicalJoin, leftChild, rightChild, context);
        case NESTED_LOOP_JOIN:
            return createNestedLoopJoinNode(logicalJoin, leftChild, rightChild, context);
        case BROADCAST_JOIN:
            return createBroadcastJoinNode(logicalJoin, leftChild, rightChild, context);
        case SHUFFLE_JOIN:
            return createShuffleJoinNode(logicalJoin, leftChild, rightChild, context);
        default:
            throw new StarRocksPlannerException("Unsupported join algorithm: " + algorithm);
    }
}

private JoinAlgorithm selectJoinAlgorithm(LogicalJoinOperator logicalJoin,
                                        PlanNode leftChild,
                                        PlanNode rightChild,
                                        OptimizerContext context) {
    
    // 1. 获取统计信息
    long leftRowCount = leftChild.getCardinality();
    long rightRowCount = rightChild.getCardinality();
    
    // 2. 检查连接条件
    List<BinaryPredicate> eqJoinPredicates = extractEqualityPredicates(logicalJoin.getOnPredicate());
    
    if (eqJoinPredicates.isEmpty()) {
        // 没有等值连接条件，使用嵌套循环连接
        return JoinAlgorithm.NESTED_LOOP_JOIN;
    }
    
    // 3. 基于数据量选择算法
    if (rightRowCount < context.getSessionVariable().getBroadcastJoinThreshold()) {
        // 右表较小，使用广播连接
        return JoinAlgorithm.BROADCAST_JOIN;
    } else if (leftRowCount > rightRowCount * 10) {
        // 左表远大于右表，使用Hash连接（右表作为构建端）
        return JoinAlgorithm.HASH_JOIN;
    } else {
        // 数据量相当，使用Shuffle连接
        return JoinAlgorithm.SHUFFLE_JOIN;
    }
}

private HashJoinNode createHashJoinNode(LogicalJoinOperator logicalJoin,
                                       PlanNode leftChild,
                                       PlanNode rightChild,
                                       OptimizerContext context) {
    
    // 1. 创建Hash连接节点
    HashJoinNode joinNode = new HashJoinNode(
        context.getNextNodeId(),
        leftChild,
        rightChild,
        logicalJoin.getJoinType(),
        extractEqualityPredicates(logicalJoin.getOnPredicate()),
        extractOtherPredicates(logicalJoin.getOnPredicate()));
    
    // 2. 设置构建端和探测端
    if (rightChild.getCardinality() < leftChild.getCardinality()) {
        // 右表作为构建端
        joinNode.setBuildSide(HashJoinNode.BuildSide.RIGHT);
    } else {
        // 左表作为构建端
        joinNode.setBuildSide(HashJoinNode.BuildSide.LEFT);
    }
    
    // 3. 设置Hash分布
    List<Expr> hashExprs = extractHashExpressions(logicalJoin.getOnPredicate());
    joinNode.setHashExpressions(hashExprs);
    
    return joinNode;
}
```

### 6.2.3 聚合算子转换

聚合算子的物理实现：

```java
private PlanNode convertAggregationOperator(LogicalAggregationOperator logicalAgg,
                                          OptExpression expression,
                                          OptimizerContext context) {
    
    // 1. 转换子节点
    PlanNode child = convertToPhysical(expression.getInputs().get(0), context);
    
    // 2. 分析聚合类型
    AggregationType aggType = analyzeAggregationType(logicalAgg, child, context);
    
    // 3. 创建聚合节点
    switch (aggType) {
        case SINGLE_PHASE:
            return createSinglePhaseAggNode(logicalAgg, child, context);
        case TWO_PHASE:
            return createTwoPhaseAggNode(logicalAgg, child, context);
        case THREE_PHASE:
            return createThreePhaseAggNode(logicalAgg, child, context);
        default:
            throw new StarRocksPlannerException("Unsupported aggregation type: " + aggType);
    }
}

private AggregationType analyzeAggregationType(LogicalAggregationOperator logicalAgg,
                                             PlanNode child,
                                             OptimizerContext context) {
    
    // 1. 检查是否有GROUP BY
    if (logicalAgg.getGroupingKeys().isEmpty()) {
        // 全局聚合，使用两阶段聚合
        return AggregationType.TWO_PHASE;
    }
    
    // 2. 检查数据分布
    DistributionProperty childDistribution = child.getDistributionProperty();
    List<ColumnRefOperator> groupByColumns = logicalAgg.getGroupingKeys();
    
    if (isDistributedByGroupByColumns(childDistribution, groupByColumns)) {
        // 数据已按GROUP BY列分布，使用单阶段聚合
        return AggregationType.SINGLE_PHASE;
    } else {
        // 需要重新分布数据，使用两阶段聚合
        return AggregationType.TWO_PHASE;
    }
}

private AggregationNode createTwoPhaseAggNode(LogicalAggregationOperator logicalAgg,
                                            PlanNode child,
                                            OptimizerContext context) {
    
    // 1. 创建第一阶段聚合（局部聚合）
    AggregationNode firstPhaseAgg = new AggregationNode(
        context.getNextNodeId(),
        child,
        logicalAgg.getGroupingKeys(),
        createFirstPhaseAggregations(logicalAgg.getAggregations()),
        AggregationNode.AggPhase.FIRST);
    
    // 2. 创建Exchange节点（重新分布数据）
    ExchangeNode exchange = new ExchangeNode(
        context.getNextNodeId(),
        firstPhaseAgg,
        DistributionSpec.createHashDistributionSpec(logicalAgg.getGroupingKeys()));
    
    // 3. 创建第二阶段聚合（全局聚合）
    AggregationNode secondPhaseAgg = new AggregationNode(
        context.getNextNodeId(),
        exchange,
        logicalAgg.getGroupingKeys(),
        createSecondPhaseAggregations(logicalAgg.getAggregations()),
        AggregationNode.AggPhase.SECOND);
    
    return secondPhaseAgg;
}
```

## 6.3 Fragment分片策略

### 6.3.1 分片算法

Fragment分片是分布式执行的核心：

```java
public class Fragmenter {
    
    public List<PlanFragment> fragment(PhysicalPlan physicalPlan) {
        List<PlanFragment> fragments = new ArrayList<>();
        
        // 1. 创建根Fragment
        PlanFragment rootFragment = new PlanFragment(
            generateFragmentId(),
            physicalPlan.getRoot(),
            DataPartition.UNPARTITIONED);
        
        // 2. 递归分片
        fragmentRecursive(rootFragment, fragments);
        
        // 3. 设置Fragment间的依赖关系
        setFragmentDependencies(fragments);
        
        return fragments;
    }
    
    private void fragmentRecursive(PlanFragment currentFragment, List<PlanFragment> allFragments) {
        PlanNode root = currentFragment.getPlanRoot();
        
        // 遍历所有子节点
        for (PlanNode child : root.getChildren()) {
            if (needsNewFragment(child)) {
                // 创建新的Fragment
                PlanFragment childFragment = createChildFragment(child, currentFragment);
                allFragments.add(childFragment);
                
                // 递归处理子Fragment
                fragmentRecursive(childFragment, allFragments);
                
                // 在当前Fragment中创建Exchange节点
                ExchangeNode exchangeNode = createExchangeNode(child, childFragment);
                root.replaceChild(child, exchangeNode);
            } else {
                // 继续在当前Fragment中处理
                fragmentRecursive(currentFragment, allFragments);
            }
        }
    }
    
    private boolean needsNewFragment(PlanNode node) {
        // 1. 扫描节点总是需要新Fragment
        if (node instanceof ScanNode) {
            return true;
        }
        
        // 2. 需要重新分布数据的节点
        if (node instanceof JoinNode) {
            JoinNode joinNode = (JoinNode) node;
            if (joinNode.getDistributionMode() == DistributionMode.SHUFFLE) {
                return true;
            }
        }
        
        // 3. 聚合节点的第二阶段
        if (node instanceof AggregationNode) {
            AggregationNode aggNode = (AggregationNode) node;
            if (aggNode.getAggPhase() == AggregationNode.AggPhase.SECOND) {
                return true;
            }
        }
        
        return false;
    }
}
```

### 6.3.2 数据分布策略

数据分布是分片的关键考虑因素：

```java
public class DistributionPlanner {
    
    public DistributionSpec planDistribution(PlanNode node, List<PlanNode> children) {
        if (node instanceof ScanNode) {
            return planScanDistribution((ScanNode) node);
        } else if (node instanceof JoinNode) {
            return planJoinDistribution((JoinNode) node, children);
        } else if (node instanceof AggregationNode) {
            return planAggregationDistribution((AggregationNode) node, children);
        } else {
            // 默认继承子节点的分布
            return children.isEmpty() ? DistributionSpec.ANY : children.get(0).getDistributionSpec();
        }
    }
    
    private DistributionSpec planScanDistribution(ScanNode scanNode) {
        if (scanNode instanceof OlapScanNode) {
            OlapScanNode olapScan = (OlapScanNode) scanNode;
            OlapTable table = olapScan.getOlapTable();
            
            // 基于表的分布键创建分布规格
            DistributionInfo distributionInfo = table.getDefaultDistributionInfo();
            if (distributionInfo instanceof HashDistributionInfo) {
                HashDistributionInfo hashDist = (HashDistributionInfo) distributionInfo;
                List<Column> distributionColumns = hashDist.getDistributionColumns();
                
                return DistributionSpec.createHashDistributionSpec(
                    convertToColumnRefs(distributionColumns));
            }
        }
        
        return DistributionSpec.createRandomDistributionSpec();
    }
    
    private DistributionSpec planJoinDistribution(JoinNode joinNode, List<PlanNode> children) {
        PlanNode leftChild = children.get(0);
        PlanNode rightChild = children.get(1);
        
        DistributionSpec leftDistribution = leftChild.getDistributionSpec();
        DistributionSpec rightDistribution = rightChild.getDistributionSpec();
        
        // 1. 检查是否可以使用Broadcast Join
        if (canUseBroadcastJoin(joinNode, rightChild)) {
            joinNode.setDistributionMode(DistributionMode.BROADCAST);
            return leftDistribution;
        }
        
        // 2. 检查是否可以使用Colocate Join
        if (canUseColocateJoin(joinNode, leftChild, rightChild)) {
            joinNode.setDistributionMode(DistributionMode.COLOCATE);
            return leftDistribution;
        }
        
        // 3. 使用Shuffle Join
        List<Expr> joinKeys = extractJoinKeys(joinNode.getEqJoinConjuncts());
        joinNode.setDistributionMode(DistributionMode.SHUFFLE);
        return DistributionSpec.createHashDistributionSpec(joinKeys);
    }
    
    private boolean canUseBroadcastJoin(JoinNode joinNode, PlanNode rightChild) {
        // 1. 检查右表大小
        long rightTableSize = estimateTableSize(rightChild);
        if (rightTableSize > SessionVariable.getBroadcastJoinThreshold()) {
            return false;
        }
        
        // 2. 检查连接类型
        JoinOperator.Type joinType = joinNode.getJoinOp();
        if (joinType == JoinOperator.Type.RIGHT_OUTER_JOIN ||
            joinType == JoinOperator.Type.FULL_OUTER_JOIN) {
            return false;
        }
        
        return true;
    }
    
    private boolean canUseColocateJoin(JoinNode joinNode, PlanNode leftChild, PlanNode rightChild) {
        // 1. 检查是否都是OLAP表扫描
        if (!(leftChild instanceof OlapScanNode) || !(rightChild instanceof OlapScanNode)) {
            return false;
        }
        
        OlapScanNode leftScan = (OlapScanNode) leftChild;
        OlapScanNode rightScan = (OlapScanNode) rightChild;
        
        // 2. 检查表是否在同一个Colocate Group中
        ColocateTableIndex colocateIndex = GlobalStateMgr.getCurrentColocateIndex();
        Long leftGroupId = colocateIndex.getGroup(leftScan.getOlapTable().getId());
        Long rightGroupId = colocateIndex.getGroup(rightScan.getOlapTable().getId());
        
        if (leftGroupId == null || rightGroupId == null || !leftGroupId.equals(rightGroupId)) {
            return false;
        }
        
        // 3. 检查连接键是否匹配分布键
        List<Expr> joinKeys = extractJoinKeys(joinNode.getEqJoinConjuncts());
        return matchesDistributionKeys(joinKeys, leftScan, rightScan);
    }
}
```

### 6.3.3 并行度规划

并行度规划决定了Fragment的执行实例数：

```java
public class ParallelismPlanner {
    
    public void planParallelism(List<PlanFragment> fragments, ConnectContext connectContext) {
        for (PlanFragment fragment : fragments) {
            int parallelism = calculateFragmentParallelism(fragment, connectContext);
            fragment.setParallelism(parallelism);
        }
    }
    
    private int calculateFragmentParallelism(PlanFragment fragment, ConnectContext connectContext) {
        PlanNode root = fragment.getPlanRoot();
        
        if (root instanceof ScanNode) {
            return calculateScanParallelism((ScanNode) root, connectContext);
        } else if (root instanceof ExchangeNode) {
            return calculateExchangeParallelism((ExchangeNode) root, connectContext);
        } else {
            // 其他节点基于数据量估算
            return calculateDataBasedParallelism(fragment, connectContext);
        }
    }
    
    private int calculateScanParallelism(ScanNode scanNode, ConnectContext connectContext) {
        if (scanNode instanceof OlapScanNode) {
            OlapScanNode olapScan = (OlapScanNode) scanNode;
            
            // 1. 基于Tablet数量
            int tabletCount = olapScan.getSelectedTabletIds().size();
            int maxTabletParallelism = Math.min(tabletCount, 
                connectContext.getSessionVariable().getMaxScanParallelism());
            
            // 2. 基于数据量
            long dataSize = estimateScanDataSize(olapScan);
            int dataSizeParallelism = (int) Math.ceil(dataSize / 
                connectContext.getSessionVariable().getScanDataSizePerInstance());
            
            // 3. 基于BE节点数
            int beCount = GlobalStateMgr.getCurrentSystemInfo().getBackendIds(true).size();
            int maxBeParallelism = beCount * connectContext.getSessionVariable().getParallelExecInstanceNum();
            
            // 取最小值作为最终并行度
            return Math.min(Math.min(maxTabletParallelism, dataSizeParallelism), maxBeParallelism);
        }
        
        return connectContext.getSessionVariable().getParallelExecInstanceNum();
    }
    
    private int calculateExchangeParallelism(ExchangeNode exchangeNode, ConnectContext connectContext) {
        // Exchange节点的并行度通常等于其子Fragment的并行度
        PlanFragment childFragment = exchangeNode.getChildFragment();
        return childFragment.getParallelism();
    }
    
    private int calculateDataBasedParallelism(PlanFragment fragment, ConnectContext connectContext) {
        // 基于处理的数据量估算并行度
        long estimatedDataSize = estimateFragmentDataSize(fragment);
        int dataBasedParallelism = (int) Math.ceil(estimatedDataSize / 
            connectContext.getSessionVariable().getDataSizePerInstance());
        
        // 限制最大并行度
        int maxParallelism = connectContext.getSessionVariable().getMaxParallelism();
        return Math.min(dataBasedParallelism, maxParallelism);
    }
}
```

## 6.4 执行计划优化

### 6.4.1 计划后处理

执行计划的后处理优化：

```java
public class PlanPostProcessor {
    
    public void postProcess(ExecPlan execPlan, ConnectContext connectContext) {
        // 1. 谓词下推优化
        pushDownPredicates(execPlan);
        
        // 2. 投影下推优化
        pushDownProjections(execPlan);
        
        // 3. Limit下推优化
        pushDownLimits(execPlan);
        
        // 4. 运行时过滤器添加
        addRuntimeFilters(execPlan);
        
        // 5. 内存使用优化
        optimizeMemoryUsage(execPlan, connectContext);
    }
    
    private void addRuntimeFilters(ExecPlan execPlan) {
        for (PlanFragment fragment : execPlan.getFragments()) {
            addRuntimeFiltersToFragment(fragment);
        }
    }
    
    private void addRuntimeFiltersToFragment(PlanFragment fragment) {
        PlanNode root = fragment.getPlanRoot();
        
        if (root instanceof HashJoinNode) {
            HashJoinNode joinNode = (HashJoinNode) root;
            
            // 为Hash Join添加运行时过滤器
            if (canAddRuntimeFilter(joinNode)) {
                RuntimeFilter filter = createRuntimeFilter(joinNode);
                joinNode.addRuntimeFilter(filter);
                
                // 将过滤器下推到扫描节点
                pushRuntimeFilterToScan(filter, fragment);
            }
        }
    }
    
    private boolean canAddRuntimeFilter(HashJoinNode joinNode) {
        // 1. 检查连接类型
        if (joinNode.getJoinOp() != JoinOperator.Type.INNER_JOIN &&
            joinNode.getJoinOp() != JoinOperator.Type.LEFT_SEMI_JOIN) {
            return false;
        }
        
        // 2. 检查构建端大小
        PlanNode buildSide = joinNode.getBuildSide() == HashJoinNode.BuildSide.LEFT ?
            joinNode.getChild(0) : joinNode.getChild(1);
        
        long buildSideCardinality = buildSide.getCardinality();
        if (buildSideCardinality > SessionVariable.getRuntimeFilterMaxSize()) {
            return false;
        }
        
        // 3. 检查选择率
        double selectivity = estimateJoinSelectivity(joinNode);
        if (selectivity > SessionVariable.getRuntimeFilterSelectivityThreshold()) {
            return false;
        }
        
        return true;
    }
}
```

### 6.4.2 资源估算

执行计划的资源估算：

```java
public class ResourceEstimator {
    
    public void estimate(ExecPlan execPlan) {
        for (PlanFragment fragment : execPlan.getFragments()) {
            estimateFragmentResources(fragment);
        }
        
        // 估算整个查询的资源需求
        estimateQueryResources(execPlan);
    }
    
    private void estimateFragmentResources(PlanFragment fragment) {
        PlanNode root = fragment.getPlanRoot();
        
        // 1. 估算内存需求
        long memoryRequirement = estimateMemoryRequirement(root);
        fragment.setMemoryRequirement(memoryRequirement);
        
        // 2. 估算CPU需求
        double cpuRequirement = estimateCpuRequirement(root);
        fragment.setCpuRequirement(cpuRequirement);
        
        // 3. 估算IO需求
        long ioRequirement = estimateIoRequirement(root);
        fragment.setIoRequirement(ioRequirement);
        
        // 4. 估算网络需求
        long networkRequirement = estimateNetworkRequirement(root);
        fragment.setNetworkRequirement(networkRequirement);
    }
    
    private long estimateMemoryRequirement(PlanNode node) {
        long totalMemory = 0;
        
        if (node instanceof HashJoinNode) {
            HashJoinNode joinNode = (HashJoinNode) node;
            
            // Hash表内存
            PlanNode buildSide = joinNode.getBuildSide() == HashJoinNode.BuildSide.LEFT ?
                joinNode.getChild(0) : joinNode.getChild(1);
            
            long buildSideRowCount = buildSide.getCardinality();
            long avgRowSize = estimateAvgRowSize(buildSide);
            totalMemory += buildSideRowCount * avgRowSize * 2; // Hash表开销
            
        } else if (node instanceof AggregationNode) {
            AggregationNode aggNode = (AggregationNode) node;
            
            // 聚合状态内存
            long groupCount = estimateGroupCount(aggNode);
            long avgGroupSize = estimateAvgGroupSize(aggNode);
            totalMemory += groupCount * avgGroupSize;
            
        } else if (node instanceof SortNode) {
            SortNode sortNode = (SortNode) node;
            
            // 排序缓冲区内存
            long inputRowCount = sortNode.getChild(0).getCardinality();
            long avgRowSize = estimateAvgRowSize(sortNode.getChild(0));
            totalMemory += inputRowCount * avgRowSize;
        }
        
        // 递归估算子节点内存
        for (PlanNode child : node.getChildren()) {
            totalMemory += estimateMemoryRequirement(child);
        }
        
        return totalMemory;
    }
}
```

## 小结

StarRocks的查询计划生成与分片系统实现了高效的分布式查询处理，通过智能的算子转换、Fragment分片策略和资源估算机制，能够生成高质量的执行计划。其设计特点包括：

1. **系统化的计划生成**: 从逻辑计划到物理计划的完整转换流程
2. **智能的分片策略**: 基于数据分布和算子特征的Fragment分片
3. **灵活的并行度规划**: 考虑多种因素的并行度计算算法
4. **全面的资源估算**: 内存、CPU、IO、网络等多维度资源评估
5. **丰富的优化技术**: 运行时过滤器、谓词下推等执行优化

在下一章中，我们将深入分析FE查询协调与调度机制，了解StarRocks如何协调分布式查询的执行。
