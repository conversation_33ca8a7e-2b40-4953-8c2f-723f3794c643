# 第十三章：容错与高可用机制

## 引言

容错与高可用是分布式数据库系统的核心要求，确保系统在面对各种故障时能够持续提供服务。StarRocks采用了多层次的容错机制，包括FE高可用、BE故障检测与恢复、数据副本管理、查询容错等技术，构建了完整的高可用体系。本章将深入分析StarRocks的容错与高可用机制。

## 13.1 高可用架构概览

### 13.1.1 多层容错架构

StarRocks的容错机制采用分层设计：

```
┌─────────────────────────────────────────────────────────┐
│                    应用层容错                            │
├─────────────────────────────────────────────────────────┤
│                    查询层容错                            │
├─────────────────────────────────────────────────────────┤
│                    服务层容错                            │
├─────────────────────────────────────────────────────────┤
│                    数据层容错                            │
├─────────────────────────────────────────────────────────┤
│                    网络层容错                            │
└─────────────────────────────────────────────────────────┘
```

### 13.1.2 核心组件分析

基于`SystemInfoService.java`的源码分析：

```java
public class SystemInfoService {
    private final Map<Long, Frontend> frontends = new ConcurrentHashMap<>();
    private final Map<Long, Backend> backends = new ConcurrentHashMap<>();
    private final Map<Long, Broker> brokers = new ConcurrentHashMap<>();
    
    // 故障检测
    private final ScheduledExecutorService heartbeatExecutor = 
        Executors.newScheduledThreadPool(Config.heartbeat_mgr_threads_num);
    
    // 高可用状态
    private volatile boolean isReady = false;
    private volatile Frontend leader = null;
    
    public void initialize() {
        // 1. 启动心跳检测
        startHeartbeatDetection();
        
        // 2. 启动故障恢复
        startFailureRecovery();
        
        // 3. 启动负载均衡
        startLoadBalancing();
    }
    
    private void startHeartbeatDetection() {
        // FE心跳检测
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            checkFrontendHeartbeat();
        }, 0, Config.heartbeat_interval_second, TimeUnit.SECONDS);
        
        // BE心跳检测
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            checkBackendHeartbeat();
        }, 0, Config.heartbeat_interval_second, TimeUnit.SECONDS);
        
        // Broker心跳检测
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            checkBrokerHeartbeat();
        }, 0, Config.heartbeat_interval_second, TimeUnit.SECONDS);
    }
    
    private void checkBackendHeartbeat() {
        for (Backend backend : backends.values()) {
            try {
                // 发送心跳请求
                HeartbeatResponse response = sendHeartbeat(backend);
                
                if (response.isSuccess()) {
                    // 更新后端状态
                    updateBackendStatus(backend, response);
                } else {
                    // 处理心跳失败
                    handleHeartbeatFailure(backend);
                }
                
            } catch (Exception e) {
                LOG.warn("Heartbeat failed for backend: " + backend.getId(), e);
                handleHeartbeatFailure(backend);
            }
        }
    }
    
    private void handleHeartbeatFailure(Backend backend) {
        backend.incrementFailureCount();
        
        if (backend.getFailureCount() >= Config.max_backend_heartbeat_failure_tolerance_count) {
            // 标记后端为不可用
            backend.setAlive(false);
            
            // 触发故障恢复
            triggerFailureRecovery(backend);
            
            LOG.warn("Backend {} marked as dead due to heartbeat failures", backend.getId());
        }
    }
    
    private void triggerFailureRecovery(Backend failedBackend) {
        // 异步执行故障恢复
        CompletableFuture.runAsync(() -> {
            try {
                recoverFromBackendFailure(failedBackend);
            } catch (Exception e) {
                LOG.error("Failed to recover from backend failure", e);
            }
        });
    }
}
```

这个系统信息服务体现了高可用系统的核心设计：
- **主动监控**: 定期心跳检测各组件状态
- **故障检测**: 多重检测机制确保故障及时发现
- **自动恢复**: 故障发生时自动触发恢复流程
- **状态管理**: 实时维护集群组件状态

## 13.2 FE高可用机制

### 13.2.1 Leader选举机制

FE集群的Leader选举实现：

```java
public class LeaderElection {
    private final BDBEnvironment bdbEnvironment;
    private volatile boolean isLeader = false;
    private volatile String currentLeader = null;
    
    // 选举状态
    private final AtomicReference<ElectionState> state = 
        new AtomicReference<>(ElectionState.FOLLOWER);
    
    public void startElection() {
        try {
            // 1. 参与选举
            participateInElection();
            
            // 2. 等待选举结果
            waitForElectionResult();
            
            // 3. 处理选举结果
            handleElectionResult();
            
        } catch (Exception e) {
            LOG.error("Election failed", e);
            scheduleReelection();
        }
    }
    
    private void participateInElection() throws Exception {
        // 使用BDB的选举机制
        ReplicationConfig repConfig = bdbEnvironment.getRepConfig();
        
        // 设置选举参数
        repConfig.setElectableGroupSizeOverride(Config.metadata_failure_recovery);
        repConfig.setDesignatedPrimary(Config.enable_bdbje_debug_mode);
        
        // 启动复制环境
        ReplicatedEnvironment repEnv = bdbEnvironment.getReplicatedEnvironment();
        
        // 监听状态变化
        repEnv.setStateChangeListener(new StateChangeListener() {
            @Override
            public void stateChange(StateChangeEvent stateChangeEvent) {
                handleStateChange(stateChangeEvent);
            }
        });
    }
    
    private void handleStateChange(StateChangeEvent event) {
        ReplicatedEnvironment.State newState = event.getState();
        
        switch (newState) {
            case MASTER:
                becomeLeader();
                break;
            case REPLICA:
                becomeFollower();
                break;
            case UNKNOWN:
                handleUnknownState();
                break;
        }
    }
    
    private void becomeLeader() {
        if (!isLeader) {
            LOG.info("Becoming leader");
            
            isLeader = true;
            state.set(ElectionState.LEADER);
            
            // 1. 初始化Leader服务
            initializeLeaderServices();
            
            // 2. 通知其他FE节点
            notifyOtherFEs();
            
            // 3. 开始处理请求
            startServingRequests();
        }
    }
    
    private void becomeFollower() {
        if (isLeader) {
            LOG.info("Stepping down from leader");
            
            isLeader = false;
            state.set(ElectionState.FOLLOWER);
            
            // 1. 停止Leader服务
            stopLeaderServices();
            
            // 2. 切换到Follower模式
            switchToFollowerMode();
        }
    }
    
    private void initializeLeaderServices() {
        // 启动元数据管理服务
        GlobalStateMgr.getCurrentState().setLeader();
        
        // 启动调度服务
        startSchedulerServices();
        
        // 启动后台任务
        startBackgroundTasks();
    }
    
    private void startSchedulerServices() {
        // 启动Tablet调度器
        TabletScheduler.getInstance().start();
        
        // 启动Compaction调度器
        CompactionScheduler.getInstance().start();
        
        // 启动Schema Change调度器
        SchemaChangeScheduler.getInstance().start();
    }
}

public enum ElectionState {
    FOLLOWER,
    CANDIDATE,
    LEADER
}
```

### 13.2.2 元数据同步机制

FE集群间的元数据同步：

```java
public class MetadataReplication {
    private final BDBEnvironment bdbEnvironment;
    private final ReplicationGroupAdmin repGroupAdmin;
    
    public void replicateMetadata(EditLog editLog) throws Exception {
        if (!GlobalStateMgr.getCurrentState().isLeader()) {
            throw new NotLeaderException("Only leader can replicate metadata");
        }
        
        // 1. 序列化编辑日志
        byte[] logData = editLog.serialize();
        
        // 2. 写入BDB
        Database database = bdbEnvironment.getEditLogDatabase();
        DatabaseEntry key = new DatabaseEntry(editLog.getLogId().getBytes());
        DatabaseEntry value = new DatabaseEntry(logData);
        
        Transaction txn = bdbEnvironment.beginTransaction();
        try {
            OperationStatus status = database.put(txn, key, value);
            
            if (status == OperationStatus.SUCCESS) {
                // 3. 提交事务（自动同步到Follower）
                txn.commit();
                
                // 4. 等待同步确认
                waitForReplicationAck(editLog.getLogId());
                
            } else {
                txn.abort();
                throw new MetadataException("Failed to write edit log");
            }
            
        } catch (Exception e) {
            txn.abort();
            throw e;
        }
    }
    
    private void waitForReplicationAck(long logId) throws Exception {
        // 等待大多数节点确认
        int requiredAcks = (Config.metadata_failure_recovery + 1) / 2;
        int receivedAcks = 0;
        
        long startTime = System.currentTimeMillis();
        long timeout = Config.bdbje_replica_ack_timeout_second * 1000;
        
        while (receivedAcks < requiredAcks) {
            if (System.currentTimeMillis() - startTime > timeout) {
                throw new TimeoutException("Replication acknowledgment timeout");
            }
            
            // 检查复制状态
            ReplicationStats stats = bdbEnvironment.getReplicationStats();
            receivedAcks = countAcknowledgedReplicas(stats, logId);
            
            Thread.sleep(10); // 短暂等待
        }
    }
    
    public void handleFollowerReplication() {
        // Follower节点处理复制
        while (GlobalStateMgr.getCurrentState().isFollower()) {
            try {
                // 1. 从Leader接收编辑日志
                EditLog editLog = receiveEditLogFromLeader();
                
                if (editLog != null) {
                    // 2. 应用编辑日志
                    applyEditLog(editLog);
                    
                    // 3. 发送确认
                    sendAcknowledgment(editLog.getLogId());
                }
                
            } catch (Exception e) {
                LOG.error("Failed to handle replication", e);
                handleReplicationError(e);
            }
        }
    }
    
    private void applyEditLog(EditLog editLog) throws Exception {
        // 根据编辑日志类型执行相应操作
        switch (editLog.getOpCode()) {
            case OP_CREATE_DB:
                applyCreateDatabase(editLog);
                break;
            case OP_CREATE_TABLE:
                applyCreateTable(editLog);
                break;
            case OP_ADD_PARTITION:
                applyAddPartition(editLog);
                break;
            case OP_DROP_TABLE:
                applyDropTable(editLog);
                break;
            default:
                LOG.warn("Unknown edit log operation: " + editLog.getOpCode());
        }
    }
}
```

## 13.3 BE故障检测与恢复

### 13.3.1 多维度故障检测

BE节点的多维度故障检测：

```java
public class BackendFailureDetector {
    private final Map<Long, BackendHealthChecker> healthCheckers = new ConcurrentHashMap<>();
    private final ScheduledExecutorService detectionExecutor = 
        Executors.newScheduledThreadPool(Config.backend_failure_detection_threads);
    
    public void startDetection() {
        // 为每个BE节点创建健康检查器
        for (Backend backend : SystemInfoService.getCurrentSystemInfo().getBackends()) {
            BackendHealthChecker checker = new BackendHealthChecker(backend);
            healthCheckers.put(backend.getId(), checker);
            
            // 启动定期检测
            detectionExecutor.scheduleAtFixedRate(checker, 0, 
                Config.backend_health_check_interval_ms, TimeUnit.MILLISECONDS);
        }
    }
    
    private class BackendHealthChecker implements Runnable {
        private final Backend backend;
        private final AtomicInteger consecutiveFailures = new AtomicInteger(0);
        private final AtomicLong lastSuccessTime = new AtomicLong(System.currentTimeMillis());
        
        public BackendHealthChecker(Backend backend) {
            this.backend = backend;
        }
        
        @Override
        public void run() {
            try {
                // 1. 心跳检测
                boolean heartbeatOk = checkHeartbeat();
                
                // 2. 服务可用性检测
                boolean serviceOk = checkServiceAvailability();
                
                // 3. 资源状态检测
                boolean resourceOk = checkResourceStatus();
                
                // 4. 综合判断健康状态
                boolean isHealthy = heartbeatOk && serviceOk && resourceOk;
                
                if (isHealthy) {
                    handleHealthyBackend();
                } else {
                    handleUnhealthyBackend();
                }
                
            } catch (Exception e) {
                LOG.error("Health check failed for backend " + backend.getId(), e);
                handleUnhealthyBackend();
            }
        }
        
        private boolean checkHeartbeat() {
            try {
                HeartbeatRequest request = new HeartbeatRequest();
                request.setTimestamp(System.currentTimeMillis());
                
                HeartbeatResponse response = BackendServiceClient.getInstance()
                    .sendHeartbeat(backend.getAddress(), request);
                
                return response != null && response.isSuccess();
                
            } catch (Exception e) {
                LOG.debug("Heartbeat failed for backend " + backend.getId(), e);
                return false;
            }
        }
        
        private boolean checkServiceAvailability() {
            try {
                // 发送简单的查询请求测试服务可用性
                PingRequest request = new PingRequest();
                PingResponse response = BackendServiceClient.getInstance()
                    .ping(backend.getAddress(), request);
                
                return response != null && response.getStatus().getStatusCode() == TStatusCode.OK;
                
            } catch (Exception e) {
                LOG.debug("Service availability check failed for backend " + backend.getId(), e);
                return false;
            }
        }
        
        private boolean checkResourceStatus() {
            try {
                // 检查磁盘空间
                if (backend.getDiskUsagePercent() > Config.storage_flood_stage_usage_percent) {
                    LOG.warn("Backend {} disk usage too high: {}%", 
                            backend.getId(), backend.getDiskUsagePercent());
                    return false;
                }
                
                // 检查内存使用
                if (backend.getMemUsagePercent() > Config.memory_flood_stage_usage_percent) {
                    LOG.warn("Backend {} memory usage too high: {}%", 
                            backend.getId(), backend.getMemUsagePercent());
                    return false;
                }
                
                return true;
                
            } catch (Exception e) {
                LOG.debug("Resource status check failed for backend " + backend.getId(), e);
                return false;
            }
        }
        
        private void handleHealthyBackend() {
            consecutiveFailures.set(0);
            lastSuccessTime.set(System.currentTimeMillis());
            
            if (!backend.isAlive()) {
                // 恢复后端状态
                backend.setAlive(true);
                LOG.info("Backend {} recovered and marked as alive", backend.getId());
                
                // 触发恢复流程
                triggerBackendRecovery(backend);
            }
        }
        
        private void handleUnhealthyBackend() {
            int failures = consecutiveFailures.incrementAndGet();
            
            if (failures >= Config.max_backend_heartbeat_failure_tolerance_count) {
                if (backend.isAlive()) {
                    // 标记后端为不可用
                    backend.setAlive(false);
                    LOG.warn("Backend {} marked as dead after {} consecutive failures", 
                            backend.getId(), failures);
                    
                    // 触发故障处理
                    triggerBackendFailure(backend);
                }
            }
        }
    }
}
```

### 13.3.2 故障恢复机制

BE故障的自动恢复机制：

```java
public class BackendFailureRecovery {
    private final TabletScheduler tabletScheduler;
    private final ReplicaManager replicaManager;
    
    public void recoverFromBackendFailure(Backend failedBackend) {
        LOG.info("Starting recovery from backend failure: {}", failedBackend.getId());
        
        try {
            // 1. 收集受影响的Tablet
            List<Tablet> affectedTablets = collectAffectedTablets(failedBackend);
            
            // 2. 评估数据可用性
            Map<Tablet, AvailabilityStatus> availabilityMap = 
                evaluateDataAvailability(affectedTablets);
            
            // 3. 执行恢复策略
            executeRecoveryStrategy(availabilityMap, failedBackend);
            
            // 4. 监控恢复进度
            monitorRecoveryProgress(affectedTablets);
            
        } catch (Exception e) {
            LOG.error("Failed to recover from backend failure", e);
            handleRecoveryFailure(failedBackend, e);
        }
    }
    
    private List<Tablet> collectAffectedTablets(Backend failedBackend) {
        List<Tablet> affectedTablets = new ArrayList<>();
        
        // 遍历所有数据库和表
        for (Database db : GlobalStateMgr.getCurrentState().getDbs()) {
            for (Table table : db.getTables()) {
                if (table instanceof OlapTable) {
                    OlapTable olapTable = (OlapTable) table;
                    
                    // 检查表的所有分区
                    for (Partition partition : olapTable.getPartitions()) {
                        for (MaterializedIndex index : partition.getMaterializedIndices()) {
                            for (Tablet tablet : index.getTablets()) {
                                // 检查Tablet是否有副本在故障BE上
                                if (hasReplicaOnBackend(tablet, failedBackend.getId())) {
                                    affectedTablets.add(tablet);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return affectedTablets;
    }
    
    private Map<Tablet, AvailabilityStatus> evaluateDataAvailability(List<Tablet> tablets) {
        Map<Tablet, AvailabilityStatus> availabilityMap = new HashMap<>();
        
        for (Tablet tablet : tablets) {
            List<Replica> aliveReplicas = getAliveReplicas(tablet);
            
            if (aliveReplicas.isEmpty()) {
                // 数据完全不可用
                availabilityMap.put(tablet, AvailabilityStatus.UNAVAILABLE);
            } else if (aliveReplicas.size() < tablet.getReplicationNum()) {
                // 数据可用但副本不足
                availabilityMap.put(tablet, AvailabilityStatus.UNDER_REPLICATED);
            } else {
                // 数据完全可用
                availabilityMap.put(tablet, AvailabilityStatus.AVAILABLE);
            }
        }
        
        return availabilityMap;
    }
    
    private void executeRecoveryStrategy(Map<Tablet, AvailabilityStatus> availabilityMap,
                                       Backend failedBackend) {
        
        for (Map.Entry<Tablet, AvailabilityStatus> entry : availabilityMap.entrySet()) {
            Tablet tablet = entry.getKey();
            AvailabilityStatus status = entry.getValue();
            
            switch (status) {
                case UNAVAILABLE:
                    handleUnavailableTablet(tablet, failedBackend);
                    break;
                    
                case UNDER_REPLICATED:
                    handleUnderReplicatedTablet(tablet, failedBackend);
                    break;
                    
                case AVAILABLE:
                    handleAvailableTablet(tablet, failedBackend);
                    break;
            }
        }
    }
    
    private void handleUnderReplicatedTablet(Tablet tablet, Backend failedBackend) {
        try {
            // 1. 选择新的BE节点
            Backend newBackend = selectBackendForReplica(tablet);
            
            if (newBackend == null) {
                LOG.error("No available backend for tablet replica: {}", tablet.getId());
                return;
            }
            
            // 2. 创建新副本
            Replica newReplica = createNewReplica(tablet, newBackend);
            
            // 3. 启动副本同步
            startReplicaSync(tablet, newReplica);
            
            // 4. 调度副本创建任务
            TabletSchedCtx schedCtx = new TabletSchedCtx(
                TabletSchedCtx.Type.REPAIR, 
                tablet.getId(), 
                newBackend.getId());
            
            tabletScheduler.addTablet(schedCtx);
            
        } catch (Exception e) {
            LOG.error("Failed to handle under-replicated tablet: " + tablet.getId(), e);
        }
    }
    
    private Backend selectBackendForReplica(Tablet tablet) {
        // 选择策略：
        // 1. 排除已有副本的BE
        // 2. 选择负载最低的BE
        // 3. 考虑机架感知
        
        Set<Long> excludeBackends = tablet.getReplicas().stream()
            .map(Replica::getBackendId)
            .collect(Collectors.toSet());
        
        return SystemInfoService.getCurrentSystemInfo()
            .getBackends()
            .stream()
            .filter(be -> be.isAlive() && !excludeBackends.contains(be.getId()))
            .filter(be -> be.getDiskUsagePercent() < Config.storage_high_watermark_usage_percent)
            .min(Comparator.comparing(Backend::getLoadScore))
            .orElse(null);
    }
}

public enum AvailabilityStatus {
    AVAILABLE,      // 数据完全可用
    UNDER_REPLICATED, // 副本不足但数据可用
    UNAVAILABLE     // 数据不可用
}
```

## 13.4 查询容错机制

### 13.4.1 查询级故障处理

查询执行过程中的故障处理：

```java
public class QueryFailureHandler {
    private final Map<String, QueryRetryContext> retryContexts = new ConcurrentHashMap<>();
    
    public void handleQueryFailure(String queryId, Exception failure, 
                                 DefaultCoordinator coordinator) {
        
        QueryRetryContext retryContext = retryContexts.computeIfAbsent(queryId, 
            k -> new QueryRetryContext(queryId, coordinator));
        
        // 1. 分析故障类型
        FailureType failureType = analyzeFailureType(failure);
        
        // 2. 决定是否重试
        if (shouldRetryQuery(retryContext, failureType)) {
            retryQuery(retryContext, failureType);
        } else {
            failQuery(retryContext, failure);
        }
    }
    
    private FailureType analyzeFailureType(Exception failure) {
        if (failure instanceof NetworkException) {
            return FailureType.NETWORK_ERROR;
        } else if (failure instanceof BackendUnavailableException) {
            return FailureType.BACKEND_UNAVAILABLE;
        } else if (failure instanceof MemoryLimitExceededException) {
            return FailureType.MEMORY_LIMIT_EXCEEDED;
        } else if (failure instanceof TimeoutException) {
            return FailureType.TIMEOUT;
        } else {
            return FailureType.UNKNOWN_ERROR;
        }
    }
    
    private boolean shouldRetryQuery(QueryRetryContext context, FailureType failureType) {
        // 1. 检查重试次数
        if (context.getRetryCount() >= Config.max_query_retry_count) {
            return false;
        }
        
        // 2. 检查故障类型是否可重试
        switch (failureType) {
            case NETWORK_ERROR:
            case BACKEND_UNAVAILABLE:
            case TIMEOUT:
                return true;
            case MEMORY_LIMIT_EXCEEDED:
                return context.getRetryCount() == 0; // 只重试一次
            case UNKNOWN_ERROR:
            default:
                return false;
        }
    }
    
    private void retryQuery(QueryRetryContext context, FailureType failureType) {
        context.incrementRetryCount();
        
        LOG.info("Retrying query {} (attempt {}) due to {}", 
                context.getQueryId(), context.getRetryCount(), failureType);
        
        try {
            // 1. 调整查询参数
            adjustQueryParameters(context, failureType);
            
            // 2. 重新选择BE节点
            if (failureType == FailureType.BACKEND_UNAVAILABLE) {
                reSelectBackends(context);
            }
            
            // 3. 重新执行查询
            context.getCoordinator().retry();
            
        } catch (Exception e) {
            LOG.error("Failed to retry query " + context.getQueryId(), e);
            failQuery(context, e);
        }
    }
    
    private void adjustQueryParameters(QueryRetryContext context, FailureType failureType) {
        DefaultCoordinator coordinator = context.getCoordinator();
        
        switch (failureType) {
            case MEMORY_LIMIT_EXCEEDED:
                // 增加内存限制
                long currentMemLimit = coordinator.getQueryOptions().getMemLimit();
                long newMemLimit = Math.min(currentMemLimit * 2, Config.exec_mem_limit);
                coordinator.getQueryOptions().setMemLimit(newMemLimit);
                break;
                
            case TIMEOUT:
                // 增加超时时间
                int currentTimeout = coordinator.getQueryOptions().getQueryTimeout();
                int newTimeout = Math.min(currentTimeout * 2, Config.query_timeout);
                coordinator.getQueryOptions().setQueryTimeout(newTimeout);
                break;
                
            case NETWORK_ERROR:
                // 减少并行度
                int currentParallelism = coordinator.getQueryOptions().getParallelism();
                int newParallelism = Math.max(currentParallelism / 2, 1);
                coordinator.getQueryOptions().setParallelism(newParallelism);
                break;
        }
    }
}

public class QueryRetryContext {
    private final String queryId;
    private final DefaultCoordinator coordinator;
    private final long startTime;
    private int retryCount = 0;
    private final List<Exception> failures = new ArrayList<>();
    
    public QueryRetryContext(String queryId, DefaultCoordinator coordinator) {
        this.queryId = queryId;
        this.coordinator = coordinator;
        this.startTime = System.currentTimeMillis();
    }
    
    public void addFailure(Exception failure) {
        failures.add(failure);
    }
    
    public void incrementRetryCount() {
        retryCount++;
    }
    
    public boolean hasExceededMaxRetryTime() {
        return System.currentTimeMillis() - startTime > Config.max_query_retry_time_ms;
    }
}

public enum FailureType {
    NETWORK_ERROR,
    BACKEND_UNAVAILABLE,
    MEMORY_LIMIT_EXCEEDED,
    TIMEOUT,
    UNKNOWN_ERROR
}
```

### 13.4.2 Fragment级容错

Fragment执行的容错机制：

```java
public class FragmentFailureHandler {
    
    public void handleFragmentFailure(FragmentInstanceExecution instance, 
                                    Exception failure) {
        
        String fragmentId = instance.getFragmentId();
        LOG.warn("Fragment {} failed: {}", fragmentId, failure.getMessage());
        
        // 1. 分析故障影响
        FailureImpact impact = analyzeFailureImpact(instance, failure);
        
        // 2. 执行容错策略
        switch (impact) {
            case RECOVERABLE:
                recoverFragment(instance);
                break;
            case QUERY_LEVEL:
                failEntireQuery(instance.getQueryId(), failure);
                break;
            case SYSTEM_LEVEL:
                handleSystemLevelFailure(failure);
                break;
        }
    }
    
    private void recoverFragment(FragmentInstanceExecution instance) {
        try {
            // 1. 选择新的BE节点
            Backend newBackend = selectAlternativeBackend(instance);
            
            if (newBackend == null) {
                throw new RuntimeException("No alternative backend available");
            }
            
            // 2. 重新调度Fragment
            rescheduleFragment(instance, newBackend);
            
            // 3. 重建数据流连接
            rebuildDataStreams(instance);
            
        } catch (Exception e) {
            LOG.error("Failed to recover fragment " + instance.getFragmentId(), e);
            failEntireQuery(instance.getQueryId(), e);
        }
    }
    
    private Backend selectAlternativeBackend(FragmentInstanceExecution instance) {
        // 排除故障BE，选择可用的替代BE
        Set<Long> excludeBackends = new HashSet<>();
        excludeBackends.add(instance.getBackendId());
        
        return SystemInfoService.getCurrentSystemInfo()
            .getBackends()
            .stream()
            .filter(be -> be.isAlive() && !excludeBackends.contains(be.getId()))
            .filter(be -> hasRequiredResources(be, instance))
            .min(Comparator.comparing(Backend::getLoadScore))
            .orElse(null);
    }
    
    private boolean hasRequiredResources(Backend backend, FragmentInstanceExecution instance) {
        // 检查内存资源
        long requiredMemory = instance.getEstimatedMemoryUsage();
        if (backend.getAvailableMemory() < requiredMemory) {
            return false;
        }
        
        // 检查CPU资源
        if (backend.getCpuUsagePercent() > Config.backend_cpu_usage_threshold) {
            return false;
        }
        
        // 检查磁盘空间
        if (backend.getDiskUsagePercent() > Config.storage_high_watermark_usage_percent) {
            return false;
        }
        
        return true;
    }
}

public enum FailureImpact {
    RECOVERABLE,    // 可恢复的故障
    QUERY_LEVEL,    // 查询级故障
    SYSTEM_LEVEL    // 系统级故障
}
```

## 小结

StarRocks的容错与高可用机制构建了完整的多层次容错体系，通过FE Leader选举、BE故障检测、数据副本管理、查询容错等技术，确保系统在各种故障场景下的高可用性。其设计特点包括：

1. **多层次容错**: 从网络层到应用层的全方位容错保护
2. **主动监控**: 实时的健康检查和故障检测机制
3. **自动恢复**: 故障发生时的自动恢复和修复流程
4. **智能重试**: 基于故障类型的智能查询重试策略
5. **数据保护**: 完善的数据副本管理和一致性保证

在下一章中，我们将深入分析资源管理与工作负载隔离的设计与实现。
