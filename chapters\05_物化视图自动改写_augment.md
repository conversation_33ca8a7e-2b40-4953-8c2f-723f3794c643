# 第五章：物化视图自动改写

## 引言

物化视图自动改写是现代OLAP数据库的核心优化技术，它能够自动将查询重写为使用预计算的物化视图，从而显著提升查询性能。StarRocks实现了先进的物化视图改写算法，支持复杂的SPJG(Select-Project-Join-GroupBy)查询模式匹配和智能改写策略。本章将深入分析StarRocks物化视图自动改写的核心机制。

## 5.1 物化视图改写架构

### 5.1.1 改写流程架构

StarRocks的物化视图改写采用多阶段处理模式：

```
查询计划 → 候选视图收集 → 模式匹配 → 改写验证 → 代价比较 → 最优计划选择
    ↓         ↓          ↓        ↓        ↓         ↓
  逻辑计划 → 视图过滤 → SPJG匹配 → 语义检查 → 代价估算 → 计划替换
```

### 5.1.2 核心组件分析

基于`MaterializedViewRewriter.java`的源码分析：

```java
public class MaterializedViewRewriter {
    private final OptimizerContext optimizerContext;
    private final List<MaterializedView> candidateViews;
    
    public List<OptExpression> rewrite(OptExpression queryPlan,
                                     OptimizerContext context) {
        List<OptExpression> rewrittenPlans = new ArrayList<>();
        
        // 1. 收集候选物化视图
        List<MaterializedView> candidates = collectCandidateViews(queryPlan, context);
        
        // 2. 对每个候选视图尝试改写
        for (MaterializedView mv : candidates) {
            try {
                OptExpression rewrittenPlan = rewriteWithMaterializedView(queryPlan, mv, context);
                if (rewrittenPlan != null) {
                    rewrittenPlans.add(rewrittenPlan);
                }
            } catch (Exception e) {
                // 改写失败，继续尝试其他视图
                LOG.debug("Failed to rewrite with materialized view: " + mv.getName(), e);
            }
        }
        
        // 3. 添加原始计划作为备选
        rewrittenPlans.add(queryPlan);
        
        return rewrittenPlans;
    }
    
    private OptExpression rewriteWithMaterializedView(OptExpression queryPlan,
                                                    MaterializedView mv,
                                                    OptimizerContext context) {
        
        // 1. 模式匹配检查
        SPJGMatcher matcher = new SPJGMatcher();
        if (!matcher.match(queryPlan, mv)) {
            return null;
        }
        
        // 2. 谓词改写检查
        PredicateRewriter predicateRewriter = new PredicateRewriter();
        if (!predicateRewriter.canRewrite(queryPlan, mv)) {
            return null;
        }
        
        // 3. 执行改写
        QueryRewriter queryRewriter = new QueryRewriter(mv, context);
        OptExpression rewrittenPlan = queryRewriter.rewrite(queryPlan);
        
        // 4. 验证改写结果
        if (validateRewrittenPlan(rewrittenPlan, queryPlan)) {
            return rewrittenPlan;
        }
        
        return null;
    }
}
```

这个改写流程体现了物化视图优化的系统性方法：
- **候选视图收集**: 基于查询特征过滤相关视图
- **模式匹配**: 检查查询和视图的结构兼容性
- **语义验证**: 确保改写的语义正确性
- **代价评估**: 选择最优的改写方案

## 5.2 SPJG模式匹配算法

### 5.2.1 SPJG结构提取

SPJG模式提取是改写的基础：

```java
public class SPJGExtractor {
    public SPJGPattern extract(OptExpression expression) {
        SPJGPattern pattern = new SPJGPattern();
        
        // 递归遍历表达式树，提取SPJG组件
        extractSPJGComponents(expression, pattern);
        
        return pattern;
    }
    
    private void extractSPJGComponents(OptExpression expression, SPJGPattern pattern) {
        Operator operator = expression.getOp();
        
        if (operator instanceof LogicalScanOperator) {
            // S: 扫描操作
            LogicalScanOperator scan = (LogicalScanOperator) operator;
            pattern.addTable(scan.getTable());
            
        } else if (operator instanceof LogicalProjectOperator) {
            // P: 投影操作
            LogicalProjectOperator project = (LogicalProjectOperator) operator;
            pattern.setProjections(project.getColumnRefMap());
            
            // 递归处理子节点
            extractSPJGComponents(expression.getInputs().get(0), pattern);
            
        } else if (operator instanceof LogicalJoinOperator) {
            // J: 连接操作
            LogicalJoinOperator join = (LogicalJoinOperator) operator;
            pattern.addJoinCondition(join.getOnPredicate());
            pattern.setJoinType(join.getJoinType());
            
            // 递归处理左右子树
            extractSPJGComponents(expression.getInputs().get(0), pattern);
            extractSPJGComponents(expression.getInputs().get(1), pattern);
            
        } else if (operator instanceof LogicalAggregationOperator) {
            // G: 分组聚合操作
            LogicalAggregationOperator agg = (LogicalAggregationOperator) operator;
            pattern.setGroupByColumns(agg.getGroupingKeys());
            pattern.setAggregations(agg.getAggregations());
            
            // 递归处理子节点
            extractSPJGComponents(expression.getInputs().get(0), pattern);
            
        } else if (operator instanceof LogicalFilterOperator) {
            // 过滤条件
            LogicalFilterOperator filter = (LogicalFilterOperator) operator;
            pattern.addPredicate(filter.getPredicate());
            
            // 递归处理子节点
            extractSPJGComponents(expression.getInputs().get(0), pattern);
        }
    }
}

public class SPJGPattern {
    private Set<Table> tables = new HashSet<>();
    private Map<ColumnRefOperator, ScalarOperator> projections = new HashMap<>();
    private List<ScalarOperator> joinConditions = new ArrayList<>();
    private JoinOperator.Type joinType;
    private List<ColumnRefOperator> groupByColumns = new ArrayList<>();
    private Map<ColumnRefOperator, CallOperator> aggregations = new HashMap<>();
    private List<ScalarOperator> predicates = new ArrayList<>();
    
    // 表匹配检查
    public boolean isTableMatched(SPJGPattern other) {
        return this.tables.containsAll(other.tables);
    }
    
    // 连接条件匹配检查
    public boolean isJoinMatched(SPJGPattern other) {
        // 检查连接类型
        if (this.joinType != other.joinType) {
            return false;
        }
        
        // 检查连接条件包含关系
        for (ScalarOperator otherCondition : other.joinConditions) {
            boolean found = false;
            for (ScalarOperator thisCondition : this.joinConditions) {
                if (isEquivalentCondition(thisCondition, otherCondition)) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                return false;
            }
        }
        
        return true;
    }
    
    // 投影匹配检查
    public boolean isProjectionMatched(SPJGPattern other) {
        for (Map.Entry<ColumnRefOperator, ScalarOperator> entry : other.projections.entrySet()) {
            ColumnRefOperator column = entry.getKey();
            ScalarOperator expr = entry.getValue();
            
            // 检查是否可以从当前投影中推导出目标投影
            if (!canDeriveProjection(column, expr)) {
                return false;
            }
        }
        
        return true;
    }
}
```

### 5.2.2 模式匹配算法

模式匹配算法的核心实现：

```java
public class SPJGMatcher {
    public MatchResult match(SPJGPattern queryPattern, SPJGPattern mvPattern) {
        MatchResult result = new MatchResult();
        
        // 1. 表匹配
        if (!matchTables(queryPattern, mvPattern, result)) {
            return MatchResult.NO_MATCH;
        }
        
        // 2. 连接匹配
        if (!matchJoins(queryPattern, mvPattern, result)) {
            return MatchResult.NO_MATCH;
        }
        
        // 3. 投影匹配
        if (!matchProjections(queryPattern, mvPattern, result)) {
            return MatchResult.NO_MATCH;
        }
        
        // 4. 分组匹配
        if (!matchGroupBy(queryPattern, mvPattern, result)) {
            return MatchResult.NO_MATCH;
        }
        
        // 5. 谓词匹配
        if (!matchPredicates(queryPattern, mvPattern, result)) {
            return MatchResult.NO_MATCH;
        }
        
        return result;
    }
    
    private boolean matchTables(SPJGPattern queryPattern, SPJGPattern mvPattern,
                              MatchResult result) {
        
        // 查询中的表必须是物化视图表的子集
        if (!mvPattern.getTables().containsAll(queryPattern.getTables())) {
            return false;
        }
        
        // 建立表映射关系
        for (Table queryTable : queryPattern.getTables()) {
            for (Table mvTable : mvPattern.getTables()) {
                if (queryTable.equals(mvTable)) {
                    result.addTableMapping(queryTable, mvTable);
                    break;
                }
            }
        }
        
        return true;
    }
    
    private boolean matchJoins(SPJGPattern queryPattern, SPJGPattern mvPattern,
                             MatchResult result) {
        
        // 连接类型必须匹配
        if (queryPattern.getJoinType() != mvPattern.getJoinType()) {
            return false;
        }
        
        // 查询的连接条件必须被物化视图包含
        for (ScalarOperator queryCondition : queryPattern.getJoinConditions()) {
            boolean found = false;
            
            for (ScalarOperator mvCondition : mvPattern.getJoinConditions()) {
                if (isEquivalentJoinCondition(queryCondition, mvCondition, result)) {
                    result.addJoinMapping(queryCondition, mvCondition);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                return false;
            }
        }
        
        return true;
    }
    
    private boolean isEquivalentJoinCondition(ScalarOperator queryCondition,
                                            ScalarOperator mvCondition,
                                            MatchResult result) {
        
        if (queryCondition instanceof BinaryPredicateOperator &&
            mvCondition instanceof BinaryPredicateOperator) {
            
            BinaryPredicateOperator queryPred = (BinaryPredicateOperator) queryCondition;
            BinaryPredicateOperator mvPred = (BinaryPredicateOperator) mvCondition;
            
            // 检查操作符类型
            if (queryPred.getBinaryType() != mvPred.getBinaryType()) {
                return false;
            }
            
            // 检查操作数等价性
            return isEquivalentOperand(queryPred.getChild(0), mvPred.getChild(0), result) &&
                   isEquivalentOperand(queryPred.getChild(1), mvPred.getChild(1), result);
        }
        
        return false;
    }
}
```

### 5.2.3 聚合匹配算法

聚合函数的匹配是SPJG匹配的重点：

```java
public class AggregationMatcher {
    public boolean matchAggregations(Map<ColumnRefOperator, CallOperator> queryAggs,
                                   Map<ColumnRefOperator, CallOperator> mvAggs,
                                   MatchResult result) {
        
        for (Map.Entry<ColumnRefOperator, CallOperator> queryEntry : queryAggs.entrySet()) {
            ColumnRefOperator queryColumn = queryEntry.getKey();
            CallOperator queryAgg = queryEntry.getValue();
            
            boolean found = false;
            
            for (Map.Entry<ColumnRefOperator, CallOperator> mvEntry : mvAggs.entrySet()) {
                ColumnRefOperator mvColumn = mvEntry.getKey();
                CallOperator mvAgg = mvEntry.getValue();
                
                if (isCompatibleAggregation(queryAgg, mvAgg)) {
                    result.addAggregationMapping(queryColumn, mvColumn);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                // 检查是否可以通过组合现有聚合函数得到
                if (!canDeriveAggregation(queryAgg, mvAggs)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    private boolean isCompatibleAggregation(CallOperator queryAgg, CallOperator mvAgg) {
        String queryFuncName = queryAgg.getFnName();
        String mvFuncName = mvAgg.getFnName();
        
        // 1. 完全匹配
        if (queryFuncName.equals(mvFuncName)) {
            return isEquivalentArguments(queryAgg.getArguments(), mvAgg.getArguments());
        }
        
        // 2. 兼容性匹配
        return isCompatibleFunction(queryFuncName, mvFuncName);
    }
    
    private boolean canDeriveAggregation(CallOperator queryAgg,
                                       Map<ColumnRefOperator, CallOperator> mvAggs) {
        
        String queryFuncName = queryAgg.getFnName();
        
        // AVG可以通过SUM和COUNT推导
        if ("avg".equals(queryFuncName)) {
            boolean hasSum = false;
            boolean hasCount = false;
            
            for (CallOperator mvAgg : mvAggs.values()) {
                if ("sum".equals(mvAgg.getFnName()) &&
                    isEquivalentArguments(queryAgg.getArguments(), mvAgg.getArguments())) {
                    hasSum = true;
                }
                if ("count".equals(mvAgg.getFnName()) &&
                    isEquivalentArguments(queryAgg.getArguments(), mvAgg.getArguments())) {
                    hasCount = true;
                }
            }
            
            return hasSum && hasCount;
        }
        
        // VARIANCE可以通过SUM、SUM_SQUARES和COUNT推导
        if ("variance".equals(queryFuncName)) {
            return hasRequiredAggregationsForVariance(queryAgg, mvAggs);
        }
        
        return false;
    }
}
```

## 5.3 查询改写算法

### 5.3.1 改写策略

查询改写的核心策略：

```java
public class QueryRewriter {
    private final MaterializedView materializedView;
    private final MatchResult matchResult;
    
    public OptExpression rewrite(OptExpression queryPlan) {
        // 1. 构建物化视图扫描节点
        OptExpression mvScan = createMaterializedViewScan();
        
        // 2. 添加补偿投影
        OptExpression compensatedPlan = addCompensationProjection(mvScan);
        
        // 3. 添加补偿过滤
        OptExpression filteredPlan = addCompensationFilter(compensatedPlan);
        
        // 4. 添加补偿聚合
        OptExpression aggregatedPlan = addCompensationAggregation(filteredPlan);
        
        // 5. 添加最终投影
        OptExpression finalPlan = addFinalProjection(aggregatedPlan);
        
        return finalPlan;
    }
    
    private OptExpression createMaterializedViewScan() {
        // 创建物化视图的扫描算子
        LogicalScanOperator scanOp = new LogicalScanOperator(
            materializedView.getTable(),
            materializedView.getOutputColumns(),
            materializedView.getPartitionPredicate(),
            Operator.DEFAULT_LIMIT,
            null);
        
        return OptExpression.create(scanOp);
    }
    
    private OptExpression addCompensationProjection(OptExpression input) {
        Map<ColumnRefOperator, ScalarOperator> projectionMap = new HashMap<>();
        
        // 1. 映射查询需要的列到物化视图的列
        for (ColumnRefOperator queryColumn : getRequiredColumns()) {
            ColumnRefOperator mvColumn = matchResult.getColumnMapping(queryColumn);
            if (mvColumn != null) {
                projectionMap.put(queryColumn, mvColumn);
            } else {
                // 需要通过表达式计算得到
                ScalarOperator expr = deriveExpression(queryColumn);
                projectionMap.put(queryColumn, expr);
            }
        }
        
        // 2. 创建投影算子
        LogicalProjectOperator projectOp = new LogicalProjectOperator(projectionMap);
        return OptExpression.create(projectOp, input);
    }
    
    private OptExpression addCompensationFilter(OptExpression input) {
        List<ScalarOperator> compensationPredicates = new ArrayList<>();
        
        // 1. 找出查询中有但物化视图中没有的谓词
        for (ScalarOperator queryPredicate : getQueryPredicates()) {
            if (!isCoveredByMaterializedView(queryPredicate)) {
                // 将谓词转换为基于物化视图列的表达式
                ScalarOperator compensationPredicate = 
                    translatePredicate(queryPredicate, matchResult);
                compensationPredicates.add(compensationPredicate);
            }
        }
        
        // 2. 如果有补偿谓词，添加过滤算子
        if (!compensationPredicates.isEmpty()) {
            ScalarOperator combinedPredicate = Utils.compoundAnd(compensationPredicates);
            LogicalFilterOperator filterOp = new LogicalFilterOperator(combinedPredicate);
            return OptExpression.create(filterOp, input);
        }
        
        return input;
    }
}
```

### 5.3.2 聚合改写

聚合函数的改写处理：

```java
public class AggregationRewriter {
    public OptExpression rewriteAggregation(OptExpression input,
                                          Map<ColumnRefOperator, CallOperator> queryAggs,
                                          List<ColumnRefOperator> queryGroupBy) {
        
        Map<ColumnRefOperator, CallOperator> rewrittenAggs = new HashMap<>();
        
        for (Map.Entry<ColumnRefOperator, CallOperator> entry : queryAggs.entrySet()) {
            ColumnRefOperator queryColumn = entry.getKey();
            CallOperator queryAgg = entry.getValue();
            
            CallOperator rewrittenAgg = rewriteAggregationFunction(queryAgg);
            rewrittenAggs.put(queryColumn, rewrittenAgg);
        }
        
        // 处理GROUP BY列
        List<ColumnRefOperator> rewrittenGroupBy = new ArrayList<>();
        for (ColumnRefOperator groupByColumn : queryGroupBy) {
            ColumnRefOperator mvColumn = matchResult.getColumnMapping(groupByColumn);
            if (mvColumn != null) {
                rewrittenGroupBy.add(mvColumn);
            }
        }
        
        LogicalAggregationOperator aggOp = new LogicalAggregationOperator(
            AggType.GLOBAL, rewrittenGroupBy, rewrittenAggs);
        
        return OptExpression.create(aggOp, input);
    }
    
    private CallOperator rewriteAggregationFunction(CallOperator queryAgg) {
        String funcName = queryAgg.getFnName();
        
        if ("avg".equals(funcName)) {
            // AVG改写为SUM/COUNT
            return rewriteAvgFunction(queryAgg);
        } else if ("variance".equals(funcName)) {
            // VARIANCE改写为复杂表达式
            return rewriteVarianceFunction(queryAgg);
        } else {
            // 直接映射
            return mapAggregationFunction(queryAgg);
        }
    }
    
    private CallOperator rewriteAvgFunction(CallOperator avgAgg) {
        // 找到对应的SUM和COUNT聚合
        ColumnRefOperator sumColumn = findCorrespondingAggregation("sum", avgAgg);
        ColumnRefOperator countColumn = findCorrespondingAggregation("count", avgAgg);
        
        if (sumColumn != null && countColumn != null) {
            // 创建SUM/COUNT表达式
            BinaryOperator divideOp = new BinaryOperator(
                BinaryType.DIVIDE, Type.DOUBLE, sumColumn, countColumn);
            
            return new CallOperator("divide", Type.DOUBLE, 
                                   Lists.newArrayList(sumColumn, countColumn));
        }
        
        throw new OptimizerException("Cannot rewrite AVG function");
    }
}
```

### 5.3.3 谓词改写

谓词条件的改写处理：

```java
public class PredicateRewriter {
    public ScalarOperator rewritePredicate(ScalarOperator queryPredicate,
                                         MatchResult matchResult) {
        
        if (queryPredicate instanceof BinaryPredicateOperator) {
            return rewriteBinaryPredicate((BinaryPredicateOperator) queryPredicate, matchResult);
        } else if (queryPredicate instanceof CompoundPredicateOperator) {
            return rewriteCompoundPredicate((CompoundPredicateOperator) queryPredicate, matchResult);
        } else if (queryPredicate instanceof InPredicateOperator) {
            return rewriteInPredicate((InPredicateOperator) queryPredicate, matchResult);
        }
        
        return queryPredicate;
    }
    
    private ScalarOperator rewriteBinaryPredicate(BinaryPredicateOperator predicate,
                                                MatchResult matchResult) {
        
        ScalarOperator left = predicate.getChild(0);
        ScalarOperator right = predicate.getChild(1);
        
        // 重写左操作数
        ScalarOperator rewrittenLeft = rewriteOperand(left, matchResult);
        
        // 重写右操作数
        ScalarOperator rewrittenRight = rewriteOperand(right, matchResult);
        
        return new BinaryPredicateOperator(predicate.getBinaryType(),
                                         rewrittenLeft, rewrittenRight);
    }
    
    private ScalarOperator rewriteOperand(ScalarOperator operand, MatchResult matchResult) {
        if (operand instanceof ColumnRefOperator) {
            // 列引用映射
            ColumnRefOperator column = (ColumnRefOperator) operand;
            ColumnRefOperator mvColumn = matchResult.getColumnMapping(column);
            return mvColumn != null ? mvColumn : operand;
            
        } else if (operand instanceof CallOperator) {
            // 函数调用重写
            CallOperator call = (CallOperator) operand;
            List<ScalarOperator> rewrittenArgs = new ArrayList<>();
            
            for (ScalarOperator arg : call.getArguments()) {
                rewrittenArgs.add(rewriteOperand(arg, matchResult));
            }
            
            return new CallOperator(call.getFnName(), call.getType(), rewrittenArgs);
        }
        
        return operand;
    }
    
    public boolean canRewritePredicate(ScalarOperator queryPredicate,
                                     List<ScalarOperator> mvPredicates) {
        
        // 1. 检查查询谓词是否被物化视图谓词蕴含
        for (ScalarOperator mvPredicate : mvPredicates) {
            if (implies(mvPredicate, queryPredicate)) {
                return true;
            }
        }
        
        // 2. 检查是否可以通过组合物化视图谓词推导出查询谓词
        return canDeriveFromCombination(queryPredicate, mvPredicates);
    }
    
    private boolean implies(ScalarOperator mvPredicate, ScalarOperator queryPredicate) {
        // 实现谓词蕴含检查逻辑
        // 例如：x > 10 蕴含 x > 5
        
        if (mvPredicate instanceof BinaryPredicateOperator &&
            queryPredicate instanceof BinaryPredicateOperator) {
            
            BinaryPredicateOperator mvBinary = (BinaryPredicateOperator) mvPredicate;
            BinaryPredicateOperator queryBinary = (BinaryPredicateOperator) queryPredicate;
            
            // 检查列是否相同
            if (!mvBinary.getChild(0).equals(queryBinary.getChild(0))) {
                return false;
            }
            
            // 检查操作符和值的蕴含关系
            return checkImplication(mvBinary, queryBinary);
        }
        
        return false;
    }
}
```

## 5.4 代价评估与选择

### 5.4.1 改写代价模型

```java
public class MaterializedViewCostEstimator {
    public double estimateRewrittenPlanCost(OptExpression rewrittenPlan,
                                          MaterializedView mv,
                                          OptimizerContext context) {
        
        // 1. 物化视图扫描代价
        double scanCost = estimateMVScanCost(mv);
        
        // 2. 补偿操作代价
        double compensationCost = estimateCompensationCost(rewrittenPlan, context);
        
        // 3. 网络传输代价（如果物化视图在远程）
        double networkCost = estimateNetworkCost(mv);
        
        return scanCost + compensationCost + networkCost;
    }
    
    private double estimateMVScanCost(MaterializedView mv) {
        // 基于物化视图的大小和存储格式估算扫描代价
        long mvSize = mv.getDataSize();
        double ioRate = CostConstants.DISK_IO_RATE;
        
        return mvSize / ioRate * CostConstants.IO_COST_PER_BYTE;
    }
    
    private double estimateCompensationCost(OptExpression plan,
                                          OptimizerContext context) {
        
        double totalCost = 0;
        
        // 递归计算每个算子的代价
        Operator operator = plan.getOp();
        
        if (operator instanceof LogicalFilterOperator) {
            totalCost += estimateFilterCost((LogicalFilterOperator) operator, plan);
        } else if (operator instanceof LogicalAggregationOperator) {
            totalCost += estimateAggregationCost((LogicalAggregationOperator) operator, plan);
        } else if (operator instanceof LogicalProjectOperator) {
            totalCost += estimateProjectionCost((LogicalProjectOperator) operator, plan);
        }
        
        // 递归计算子节点代价
        for (OptExpression child : plan.getInputs()) {
            totalCost += estimateCompensationCost(child, context);
        }
        
        return totalCost;
    }
}
```

### 5.4.2 改写方案选择

```java
public class RewritePlanSelector {
    public OptExpression selectBestPlan(List<OptExpression> candidates,
                                       OptimizerContext context) {
        
        double lowestCost = Double.MAX_VALUE;
        OptExpression bestPlan = null;
        
        for (OptExpression candidate : candidates) {
            double cost = estimatePlanCost(candidate, context);
            
            if (cost < lowestCost) {
                lowestCost = cost;
                bestPlan = candidate;
            }
        }
        
        return bestPlan;
    }
    
    private double estimatePlanCost(OptExpression plan, OptimizerContext context) {
        CostEstimator costEstimator = context.getCostEstimator();
        return costEstimator.calculateCost(plan);
    }
    
    public boolean shouldUseRewrittenPlan(OptExpression originalPlan,
                                        OptExpression rewrittenPlan,
                                        OptimizerContext context) {
        
        double originalCost = estimatePlanCost(originalPlan, context);
        double rewrittenCost = estimatePlanCost(rewrittenPlan, context);
        
        // 考虑改写的收益阈值
        double threshold = context.getSessionVariable().getMVRewriteThreshold();
        
        return rewrittenCost < originalCost * threshold;
    }
}
```

## 5.5 高级改写技术

### 5.5.1 多表物化视图改写

```java
public class MultiTableMVRewriter {
    public OptExpression rewriteWithMultiTableMV(OptExpression queryPlan,
                                                MaterializedView mv) {
        
        // 1. 分析查询的连接结构
        JoinGraph queryJoinGraph = extractJoinGraph(queryPlan);
        JoinGraph mvJoinGraph = extractJoinGraph(mv.getQueryPlan());
        
        // 2. 检查连接图的包含关系
        if (!mvJoinGraph.contains(queryJoinGraph)) {
            return null;
        }
        
        // 3. 构建表映射关系
        Map<Table, Table> tableMapping = buildTableMapping(queryJoinGraph, mvJoinGraph);
        
        // 4. 执行改写
        return performMultiTableRewrite(queryPlan, mv, tableMapping);
    }
    
    private JoinGraph extractJoinGraph(OptExpression plan) {
        JoinGraph graph = new JoinGraph();
        extractJoinNodes(plan, graph);
        return graph;
    }
    
    private void extractJoinNodes(OptExpression expression, JoinGraph graph) {
        Operator operator = expression.getOp();
        
        if (operator instanceof LogicalScanOperator) {
            LogicalScanOperator scan = (LogicalScanOperator) operator;
            graph.addTable(scan.getTable());
            
        } else if (operator instanceof LogicalJoinOperator) {
            LogicalJoinOperator join = (LogicalJoinOperator) operator;
            
            // 递归处理左右子树
            extractJoinNodes(expression.getInputs().get(0), graph);
            extractJoinNodes(expression.getInputs().get(1), graph);
            
            // 添加连接边
            graph.addJoinEdge(join.getOnPredicate());
        }
    }
}
```

### 5.5.2 嵌套查询改写

```java
public class NestedQueryRewriter {
    public OptExpression rewriteNestedQuery(OptExpression queryPlan,
                                          List<MaterializedView> mvs) {
        
        // 1. 识别子查询
        List<OptExpression> subqueries = findSubqueries(queryPlan);
        
        // 2. 对每个子查询尝试改写
        for (OptExpression subquery : subqueries) {
            for (MaterializedView mv : mvs) {
                OptExpression rewrittenSubquery = tryRewriteSubquery(subquery, mv);
                if (rewrittenSubquery != null) {
                    // 替换原始子查询
                    queryPlan = replaceSubquery(queryPlan, subquery, rewrittenSubquery);
                    break;
                }
            }
        }
        
        return queryPlan;
    }
    
    private List<OptExpression> findSubqueries(OptExpression plan) {
        List<OptExpression> subqueries = new ArrayList<>();
        findSubqueriesRecursive(plan, subqueries);
        return subqueries;
    }
    
    private void findSubqueriesRecursive(OptExpression expression,
                                       List<OptExpression> subqueries) {
        
        Operator operator = expression.getOp();
        
        if (operator instanceof LogicalApplyOperator) {
            // 找到Apply算子，其右子树是子查询
            subqueries.add(expression.getInputs().get(1));
        }
        
        // 递归处理子节点
        for (OptExpression child : expression.getInputs()) {
            findSubqueriesRecursive(child, subqueries);
        }
    }
}
```

## 小结

StarRocks的物化视图自动改写系统实现了先进的查询优化技术，通过SPJG模式匹配、智能改写算法和代价评估机制，能够自动识别和利用物化视图来优化查询性能。其设计特点包括：

1. **完整的SPJG支持**: 支持复杂的Select-Project-Join-GroupBy查询模式
2. **智能模式匹配**: 精确的结构和语义匹配算法
3. **灵活的改写策略**: 支持补偿投影、过滤、聚合等多种改写技术
4. **代价驱动选择**: 基于代价模型选择最优的改写方案
5. **高级改写技术**: 支持多表视图、嵌套查询等复杂场景

在下一章中，我们将深入分析查询计划生成与分片策略，了解StarRocks如何将优化后的逻辑计划转换为可执行的物理计划。
