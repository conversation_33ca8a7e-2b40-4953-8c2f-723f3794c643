# 第一章：StarRocks整体架构概览与设计哲学

## 1. 引言

StarRocks作为一个为分析型场景设计的MPP（Massively Parallel Processing）数据库，其核心目标是提供极致的查询性能和实时的数据分析能力。本章将从最高层次剖析StarRocks的整体架构，探讨其背后的核心设计哲学，并解释这些设计选择如何共同作用，支撑起其强大的功能和性能。我们将深入分析其FE/BE分离的无共享（Shared-Nothing）架构、MPP并行计算模型、以及面向OLAP场景的创新思想。

## 2. 核心设计理念

StarRocks的设计哲学可以概括为：**简洁、高效、开放**。

*   **简洁**：架构清晰，FE和BE职责分明，易于理解和运维。不依赖Hadoop等外部组件，力求自包含，降低部署和维护复杂性。
*   **高效**：从查询引擎到存储引擎，全链路追求极致性能。采用全向量化执行、Pipeline并行、CBO优化、物化视图等先进技术，最大化利用硬件资源。
*   **开放**：拥抱开源生态，兼容MySQL协议，支持标准SQL，并与Spark, Flink, Kafka等大数据工具无缝集成。

## 3. 源码分析：架构的基石

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/StarRocksFE.java`
*   `be/src/agent/heartbeat_server.cpp`
*   `docs/zh/developers/trace-tools/query_trace_profile.md`

### 3.1. FE-BE分离架构

StarRocks采用经典的计算与存储分离、FE与BE分离的架构。

*   **Frontend (FE)**：查询的入口和大脑。负责接收SQL请求、SQL解析、语义分析、查询优化、生成执行计划、管理元数据（通过BDBJE实现高可用）以及调度查询执行。FE是轻量级的，可以水平扩展以提高查询并发和可用性。`StarRocksFE.java`是FE进程的启动类，它初始化了所有FE服务，如`QeService`（查询执行）、`Catalog`（元数据管理）等。

*   **Backend (BE)**：数据的存储和计算核心。负责存储数据（列式存储）、执行FE下发的物理计划（Fragment）、管理本地数据副本。BE是重型的，通过水平扩展可以线性提升数据存储容量和查询性能。BE节点之间通过心跳机制（`HeartbeatServer`）向FE汇报状态，FE据此进行副本管理和查询调度。

这种架构的优势在于：
1.  **权责清晰**：FE专注于查询优化和调度，BE专注于数据存储和计算，易于独立演进和优化。
2.  **弹性伸缩**：FE和BE可以根据负载独立扩展。计算密集型场景可以增加BE，高并发场景可以增加FE。
3.  **高可用**：FE通过BDBJE实现多副本选举，保证元数据服务高可用。BE数据通过多副本存储，保证数据高可用。

### 3.2. MPP与无共享（Shared-Nothing）

StarRocks是一个典型的MPP数据库。当一个查询计划生成后，它会被切分成多个可以在BE上并行执行的**Fragment**。每个Fragment负责处理一部分数据。BE节点之间在查询执行期间通常不直接交换数据（除非必要的数据Shuffle），而是独立完成计算任务，最后由上层节点或FE进行结果汇聚。这就是**Shared-Nothing**架构，它避免了资源争抢，保证了系统的高可扩展性。

### 3.3. 设计思想与权衡

StarRocks的设计充满了对性能和易用性的权衡。例如，不依赖HDFS等外部存储，而是自己管理数据，这增加了系统的复杂性，但换来了对数据布局的完全掌控，从而可以进行更深度的存储和查询优化（如Colocate Join）。FE的元数据管理采用成熟的BDBJE，而不是自研Raft，这是在可靠性和研发成本之间的权-衡。

## 4. 总结

StarRocks的架构设计是其高性能的基石。FE/BE分离的MPP架构提供了强大的并行处理能力和弹性伸缩性。从代码实现中，我们可以看到其对简洁、高效原则的贯彻。理解了这些顶层设计，我们才能在后续章节中更好地理解SQL解析、优化、执行等各个环节的具体实现。
