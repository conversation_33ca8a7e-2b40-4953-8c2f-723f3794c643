# 第十三章：容错与高可用机制

## 1. 引言

对于一个企业级的分布式数据库系统而言，除了高性能，**可靠性**是其安身立命之本。在由成百上千台服务器组成的集群中，硬件故障、网络分区、软件Bug等异常是常态而非偶然。一个健壮的系统必须能够在这种不完美的环境中，自动地检测故障、进行恢复，并保证数据的一致性和服务的持续可用。本章将深入探讨StarRocks的容错（Fault Tolerance）与高可用（High Availability, HA）机制，分析其如何在FE和BE两个层面，通过多副本、心跳检测、自动故障转移等技术，构建一个稳定可靠的系统。

## 2. 高可用的核心挑战

分布式系统的高可用主要面临以下挑战：

1.  **单点故障（Single Point of Failure, SPOF）**：系统中是否存在某个组件，一旦它失效，整个系统就无法服务。
2.  **数据一致性（Data Consistency）**：在发生故障或进行数据复制时，如何保证用户读到的数据是一致的、正确的。
3.  **故障检测（Failure Detection）**：如何快速、准确地判断一个节点是真的宕机了，还是仅仅是网络暂时抖动。
4.  **自动恢复（Automatic Recovery）**：在检测到故障后，系统能否在无人干预的情况下，自动完成修复或切换，对外持续提供服务。

## 3. FE的高可用机制

FE作为系统的“大脑”，负责元数据管理和查询协调，其高可用至关重要。

### 3.1. 多副本与BDBJE

StarRocks通过部署多个FE节点（通常是1个Master，2个Follower）来消除单点故障。FE的元数据（包括库、表、分区、副本位置等信息）通过**Berkeley DB Java Edition (BDBJE)**进行持久化和复制。

*   BDBJE是一个嵌入式的、支持事务的Java数据库。StarRocks利用其内置的**HA协议**（一种类Raft/Paxos的协议）来保证元数据在多个FE节点之间的一致性。
*   所有对元数据的修改操作，都必须先在Master FE上写入BDBJE日志，然后这条日志会被同步到超过半数的Follower FE节点上，之后这个修改才算成功提交。
*   这种机制保证了即使Master FE宕机，其元数据也已经安全地存在于其他Follower节点上。

### 3.2. Master选举与故障转移

*   **选举**：当集群启动或现有的Master FE失联时，剩下的Follower FE会基于BDBJE的选举机制，自动选举出一个新的Master。选举的原则是拥有最新BDBJE日志的节点获胜。
*   **故障转移**：一旦新的Master选举成功，它就会接管所有服务。对于外部用户（如JDBC/ODBC连接）而言，通常会配置所有FE节点的地址。当连接当前Master失败时，客户端会自动尝试连接列表中的下一个地址，直到找到新的Master。这个过程对上层应用是透明的。

## 4. BE的高可用与数据容错

BE负责存储数据和执行计算，其高可用主要体现在数据的安全性和服务的持续性上。

### 4.1. 多副本存储（Replication）

StarRocks的数据容错基础是**多副本存储**。在创建表时，可以指定副本数量（通常为3）。StarRocks会将表的每个Tablet，都复制3份，并根据一定的策略（如考虑机架、物理机位置），将这些副本分散到不同的BE节点上。

### 4.2. 写操作的一致性

StarRocks的写操作采用一种基于**Paxos的多数派写入**协议来保证数据一致性。
1.  一次写入请求（如一次Stream Load）会先发送给Tablet的Leader副本所在的BE。
2.  Leader BE将数据写入本地，并将写操作日志同步给所有的Follower副本。
3.  只有当包括Leader在内的、超过半数的副本都确认写入成功后，Leader才会向客户端确认写入成功。
4.  这个机制保证了即使有少数副本（小于N/2）写入失败或失联，数据也能成功写入，并且所有成功的写入在各个副本之间是一致的。

### 4.3. 故障检测与副本修复

*   **心跳机制**：BE节点会定期向所有FE节点发送心跳包。心跳包中包含了BE的健康状况、磁盘使用率、Tablet副本列表及其版本等信息。
*   **故障判定**：如果FE在一段时间内（可配置，如30秒）没有收到某个BE的心跳，FE就会将该BE标记为`DEAD`。
*   **自动副本修复**：当一个BE被标记为`DEAD`后，该BE上所有的Tablet副本就变为不可用。FE中的**TabletChecker**和**TabletScheduler**后台线程会检测到这些副本的健康状况不佳（副本数少于指定数量）。它们会自动触发**克隆（Clone）**任务，从该Tablet的其他健康副本中，选择一个作为源，在一个新的、健康的BE上复制出一个新的副本，从而使副本数量恢复到预设值。这个过程完全自动化，无需人工干预。

## 5. 查询层面的容错

在查询执行期间，如果某个BE节点突然宕机，`DefaultCoordinator`会收到RPC失败或超时的错误。此时，当前查询会失败，Coordinator会立即取消所有其他正在执行的Fragment，并向客户端返回错误。StarRocks目前主要通过快速失败（Fail-Fast）来处理查询时故障，保证了资源的及时释放。对于一些ETL场景，用户可以通过上层调度系统（如DolphinScheduler）来进行失败重试。

## 6. 总结

StarRocks通过一套精心设计的多层级高可用机制，确保了系统的稳定可靠。在FE层，通过BDBJE实现了元数据的强一致性复制和Master的自动选举。在BE层，通过多副本存储和多数派写入协议保证了数据的安全与一致，并通过心跳检测和自动副本修复机制，实现了对节点故障的自动容错。这些机制共同作用，使得StarRocks能够抵御常见的单点故障和硬件异常，为用户提供一个真正企业级的、7x24小时高可用的数据分析服务。
