# 第十二章：性能监控与调试工具

## 引言

性能监控与调试工具是数据库系统运维和优化的重要支撑，帮助开发者和运维人员深入理解查询执行过程、识别性能瓶颈并进行针对性优化。StarRocks提供了完整的性能监控体系，包括Query Trace Profile、Runtime Profile、执行统计等多层次的监控和调试工具。本章将深入分析这些工具的设计原理和使用方法。

## 12.1 性能监控架构

### 12.1.1 监控体系概览

StarRocks的性能监控采用分层架构：

```
┌─────────────────────────────────────────────────────────┐
│                    用户界面层                            │
├─────────────────────────────────────────────────────────┤
│                    数据展示层                            │
├─────────────────────────────────────────────────────────┤
│                    数据聚合层                            │
├─────────────────────────────────────────────────────────┤
│                    数据收集层                            │
├─────────────────────────────────────────────────────────┤
│                    数据生成层                            │
└─────────────────────────────────────────────────────────┘
```

### 12.1.2 核心组件分析

性能监控的核心组件：

```cpp
class RuntimeProfile {
private:
    std::string _name;
    std::map<std::string, Counter*> _counters;
    std::map<std::string, std::string> _info_strings;
    std::vector<std::unique_ptr<RuntimeProfile>> _children;
    RuntimeProfile* _parent = nullptr;
    
    // 时间统计
    std::chrono::steady_clock::time_point _start_time;
    std::chrono::steady_clock::time_point _end_time;
    
    // 线程安全
    mutable std::mutex _mutex;
    
public:
    RuntimeProfile(const std::string& name) : _name(name) {
        _start_time = std::chrono::steady_clock::now();
    }
    
    // 计数器管理
    Counter* add_counter(const std::string& name, TUnit::type unit) {
        std::lock_guard<std::mutex> lock(_mutex);
        
        auto counter = std::make_unique<Counter>(unit);
        Counter* counter_ptr = counter.get();
        _counters[name] = counter_ptr;
        
        return counter_ptr;
    }
    
    void update_counter(const std::string& name, int64_t value) {
        std::lock_guard<std::mutex> lock(_mutex);
        
        auto it = _counters.find(name);
        if (it != _counters.end()) {
            it->second->update(value);
        }
    }
    
    // 信息字符串管理
    void add_info_string(const std::string& key, const std::string& value) {
        std::lock_guard<std::mutex> lock(_mutex);
        _info_strings[key] = value;
    }
    
    // 子Profile管理
    RuntimeProfile* create_child(const std::string& name) {
        std::lock_guard<std::mutex> lock(_mutex);
        
        auto child = std::make_unique<RuntimeProfile>(name);
        child->_parent = this;
        RuntimeProfile* child_ptr = child.get();
        _children.push_back(std::move(child));
        
        return child_ptr;
    }
    
    // Profile合并
    void merge(const RuntimeProfile& other) {
        std::lock_guard<std::mutex> lock(_mutex);
        
        // 合并计数器
        for (const auto& [name, counter] : other._counters) {
            auto it = _counters.find(name);
            if (it != _counters.end()) {
                it->second->merge(*counter);
            } else {
                _counters[name] = new Counter(*counter);
            }
        }
        
        // 合并信息字符串
        for (const auto& [key, value] : other._info_strings) {
            _info_strings[key] = value;
        }
        
        // 递归合并子Profile
        for (const auto& other_child : other._children) {
            bool found = false;
            for (auto& my_child : _children) {
                if (my_child->_name == other_child->_name) {
                    my_child->merge(*other_child);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                auto new_child = std::make_unique<RuntimeProfile>(*other_child);
                _children.push_back(std::move(new_child));
            }
        }
    }
    
    // 序列化为文本
    std::string to_string(int indent = 0) const {
        std::lock_guard<std::mutex> lock(_mutex);
        
        std::stringstream ss;
        std::string indent_str(indent * 2, ' ');
        
        // Profile名称和总时间
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            _end_time - _start_time).count();
        ss << indent_str << _name << " (Total: " << total_time << "us)\n";
        
        // 计数器信息
        for (const auto& [name, counter] : _counters) {
            ss << indent_str << "  " << name << ": " << counter->to_string() << "\n";
        }
        
        // 信息字符串
        for (const auto& [key, value] : _info_strings) {
            ss << indent_str << "  " << key << ": " << value << "\n";
        }
        
        // 递归输出子Profile
        for (const auto& child : _children) {
            ss << child->to_string(indent + 1);
        }
        
        return ss.str();
    }
};

class Counter {
private:
    TUnit::type _unit;
    std::atomic<int64_t> _value{0};
    std::atomic<int64_t> _count{0};
    
public:
    Counter(TUnit::type unit) : _unit(unit) {}
    
    void update(int64_t value) {
        _value.fetch_add(value);
        _count.fetch_add(1);
    }
    
    void set(int64_t value) {
        _value.store(value);
        _count.store(1);
    }
    
    int64_t value() const { return _value.load(); }
    int64_t count() const { return _count.load(); }
    
    void merge(const Counter& other) {
        _value.fetch_add(other._value.load());
        _count.fetch_add(other._count.load());
    }
    
    std::string to_string() const {
        int64_t val = _value.load();
        
        switch (_unit) {
            case TUnit::TIME_NS:
                return format_time_ns(val);
            case TUnit::BYTES:
                return format_bytes(val);
            case TUnit::UNIT:
                return std::to_string(val);
            default:
                return std::to_string(val);
        }
    }
    
private:
    std::string format_time_ns(int64_t ns) const {
        if (ns < 1000) return std::to_string(ns) + "ns";
        if (ns < 1000000) return std::to_string(ns / 1000) + "us";
        if (ns < 1000000000) return std::to_string(ns / 1000000) + "ms";
        return std::to_string(ns / 1000000000) + "s";
    }
    
    std::string format_bytes(int64_t bytes) const {
        if (bytes < 1024) return std::to_string(bytes) + "B";
        if (bytes < 1024 * 1024) return std::to_string(bytes / 1024) + "KB";
        if (bytes < 1024 * 1024 * 1024) return std::to_string(bytes / (1024 * 1024)) + "MB";
        return std::to_string(bytes / (1024 * 1024 * 1024)) + "GB";
    }
};
```

这个Runtime Profile系统体现了性能监控的核心设计：
- **层次化结构**: 支持嵌套的Profile树形结构
- **多维度统计**: 时间、内存、IO、网络等多种指标
- **线程安全**: 支持多线程并发更新
- **可合并性**: 支持分布式Profile的合并

## 12.2 Query Trace Profile

### 12.2.1 查询跟踪机制

基于文档`docs/zh/developers/trace-tools/query_trace_profile.md`的分析：

```java
public class QueryTraceProfileManager {
    private final Map<String, QueryTraceProfile> _active_traces = new ConcurrentHashMap<>();
    private final ExecutorService _trace_executor = Executors.newFixedThreadPool(4);
    
    public void startTrace(String queryId, TraceOptions options) {
        QueryTraceProfile trace = new QueryTraceProfile(queryId, options);
        _active_traces.put(queryId, trace);
        
        // 异步收集跟踪信息
        _trace_executor.submit(() -> collectTraceInfo(trace));
    }
    
    public QueryTraceProfile getTrace(String queryId) {
        return _active_traces.get(queryId);
    }
    
    public void finishTrace(String queryId) {
        QueryTraceProfile trace = _active_traces.remove(queryId);
        if (trace != null) {
            trace.finish();
        }
    }
    
    private void collectTraceInfo(QueryTraceProfile trace) {
        try {
            // 1. 收集查询计划信息
            collectPlanInfo(trace);
            
            // 2. 收集执行统计信息
            collectExecutionStats(trace);
            
            // 3. 收集资源使用信息
            collectResourceUsage(trace);
            
            // 4. 收集错误信息
            collectErrorInfo(trace);
            
        } catch (Exception e) {
            LOG.warn("Failed to collect trace info for query " + trace.getQueryId(), e);
        }
    }
}

public class QueryTraceProfile {
    private final String _query_id;
    private final TraceOptions _options;
    private final long _start_time;
    private long _end_time;
    
    // 跟踪数据
    private final List<TraceEvent> _events = new ArrayList<>();
    private final Map<String, Object> _metadata = new HashMap<>();
    private final List<PlanNode> _plan_nodes = new ArrayList<>();
    private final Map<String, RuntimeProfile> _fragment_profiles = new HashMap<>();
    
    public void addEvent(TraceEvent event) {
        synchronized (_events) {
            _events.add(event);
        }
    }
    
    public void addMetadata(String key, Object value) {
        synchronized (_metadata) {
            _metadata.put(key, value);
        }
    }
    
    public void addPlanNode(PlanNode node) {
        synchronized (_plan_nodes) {
            _plan_nodes.add(node);
        }
    }
    
    public void addFragmentProfile(String fragmentId, RuntimeProfile profile) {
        synchronized (_fragment_profiles) {
            _fragment_profiles.put(fragmentId, profile);
        }
    }
    
    public String generateReport() {
        StringBuilder report = new StringBuilder();
        
        // 1. 查询基本信息
        report.append("Query ID: ").append(_query_id).append("\n");
        report.append("Start Time: ").append(new Date(_start_time)).append("\n");
        report.append("End Time: ").append(new Date(_end_time)).append("\n");
        report.append("Duration: ").append(_end_time - _start_time).append("ms\n\n");
        
        // 2. 查询计划
        report.append("=== Query Plan ===\n");
        for (PlanNode node : _plan_nodes) {
            report.append(node.toString()).append("\n");
        }
        report.append("\n");
        
        // 3. 执行事件
        report.append("=== Execution Events ===\n");
        for (TraceEvent event : _events) {
            report.append(event.toString()).append("\n");
        }
        report.append("\n");
        
        // 4. Fragment性能
        report.append("=== Fragment Profiles ===\n");
        for (Map.Entry<String, RuntimeProfile> entry : _fragment_profiles.entrySet()) {
            report.append("Fragment ").append(entry.getKey()).append(":\n");
            report.append(entry.getValue().toString()).append("\n");
        }
        
        return report.toString();
    }
}

public class TraceEvent {
    private final long _timestamp;
    private final String _event_type;
    private final String _description;
    private final Map<String, Object> _attributes;
    
    public TraceEvent(String eventType, String description) {
        this._timestamp = System.currentTimeMillis();
        this._event_type = eventType;
        this._description = description;
        this._attributes = new HashMap<>();
    }
    
    public void addAttribute(String key, Object value) {
        _attributes.put(key, value);
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s: %s %s", 
                           new Date(_timestamp), _event_type, _description, _attributes);
    }
}
```

### 12.2.2 执行阶段跟踪

详细的执行阶段跟踪：

```java
public class ExecutionTracker {
    private final QueryTraceProfile _trace;
    
    public void trackPlanningPhase(OptExpression plan) {
        TraceEvent event = new TraceEvent("PLANNING", "Query planning completed");
        event.addAttribute("plan_nodes", plan.getInputs().size());
        event.addAttribute("plan_depth", calculatePlanDepth(plan));
        _trace.addEvent(event);
    }
    
    public void trackOptimizationPhase(OptExpression originalPlan, OptExpression optimizedPlan) {
        TraceEvent event = new TraceEvent("OPTIMIZATION", "Query optimization completed");
        event.addAttribute("original_cost", estimateCost(originalPlan));
        event.addAttribute("optimized_cost", estimateCost(optimizedPlan));
        event.addAttribute("optimization_rules_applied", getAppliedRules());
        _trace.addEvent(event);
    }
    
    public void trackFragmentExecution(String fragmentId, FragmentExecParams params) {
        TraceEvent event = new TraceEvent("FRAGMENT_START", "Fragment execution started");
        event.addAttribute("fragment_id", fragmentId);
        event.addAttribute("instance_count", params.instanceExecParams.size());
        event.addAttribute("scan_ranges", params.scanRangeParams.size());
        _trace.addEvent(event);
    }
    
    public void trackFragmentCompletion(String fragmentId, RuntimeProfile profile) {
        TraceEvent event = new TraceEvent("FRAGMENT_COMPLETE", "Fragment execution completed");
        event.addAttribute("fragment_id", fragmentId);
        event.addAttribute("rows_processed", profile.getCounter("RowsReturned").value());
        event.addAttribute("execution_time", profile.getCounter("TotalTime").value());
        _trace.addEvent(event);
        
        // 添加详细的Profile信息
        _trace.addFragmentProfile(fragmentId, profile);
    }
    
    public void trackDataTransfer(String sourceFragment, String destFragment, 
                                long bytesTransferred, long rowsTransferred) {
        TraceEvent event = new TraceEvent("DATA_TRANSFER", "Data transfer between fragments");
        event.addAttribute("source_fragment", sourceFragment);
        event.addAttribute("dest_fragment", destFragment);
        event.addAttribute("bytes_transferred", bytesTransferred);
        event.addAttribute("rows_transferred", rowsTransferred);
        _trace.addEvent(event);
    }
}
```

## 12.3 Runtime Profile详细分析

### 12.3.1 算子级性能统计

算子级别的详细性能统计：

```cpp
class OperatorProfile {
private:
    RuntimeProfile* _runtime_profile;
    
    // 基础计数器
    Counter* _rows_returned_counter;
    Counter* _bytes_returned_counter;
    Counter* _total_time_counter;
    Counter* _cpu_time_counter;
    Counter* _wait_time_counter;
    
    // 内存计数器
    Counter* _peak_memory_usage_counter;
    Counter* _memory_allocated_counter;
    Counter* _memory_freed_counter;
    
    // IO计数器
    Counter* _bytes_read_counter;
    Counter* _bytes_written_counter;
    Counter* _io_time_counter;
    Counter* _io_count_counter;
    
    // 网络计数器
    Counter* _network_bytes_sent_counter;
    Counter* _network_bytes_received_counter;
    Counter* _network_time_counter;
    
public:
    OperatorProfile(const std::string& name) {
        _runtime_profile = new RuntimeProfile(name);
        
        // 初始化计数器
        _rows_returned_counter = _runtime_profile->add_counter("RowsReturned", TUnit::UNIT);
        _bytes_returned_counter = _runtime_profile->add_counter("BytesReturned", TUnit::BYTES);
        _total_time_counter = _runtime_profile->add_counter("TotalTime", TUnit::TIME_NS);
        _cpu_time_counter = _runtime_profile->add_counter("CpuTime", TUnit::TIME_NS);
        _wait_time_counter = _runtime_profile->add_counter("WaitTime", TUnit::TIME_NS);
        
        _peak_memory_usage_counter = _runtime_profile->add_counter("PeakMemoryUsage", TUnit::BYTES);
        _memory_allocated_counter = _runtime_profile->add_counter("MemoryAllocated", TUnit::BYTES);
        _memory_freed_counter = _runtime_profile->add_counter("MemoryFreed", TUnit::BYTES);
        
        _bytes_read_counter = _runtime_profile->add_counter("BytesRead", TUnit::BYTES);
        _bytes_written_counter = _runtime_profile->add_counter("BytesWritten", TUnit::BYTES);
        _io_time_counter = _runtime_profile->add_counter("IoTime", TUnit::TIME_NS);
        _io_count_counter = _runtime_profile->add_counter("IoCount", TUnit::UNIT);
        
        _network_bytes_sent_counter = _runtime_profile->add_counter("NetworkBytesSent", TUnit::BYTES);
        _network_bytes_received_counter = _runtime_profile->add_counter("NetworkBytesReceived", TUnit::BYTES);
        _network_time_counter = _runtime_profile->add_counter("NetworkTime", TUnit::TIME_NS);
    }
    
    void update_rows_returned(int64_t rows) {
        _rows_returned_counter->update(rows);
    }
    
    void update_bytes_returned(int64_t bytes) {
        _bytes_returned_counter->update(bytes);
    }
    
    void update_execution_time(int64_t time_ns) {
        _total_time_counter->update(time_ns);
    }
    
    void update_memory_usage(int64_t peak_usage, int64_t allocated, int64_t freed) {
        _peak_memory_usage_counter->set(peak_usage);
        _memory_allocated_counter->update(allocated);
        _memory_freed_counter->update(freed);
    }
    
    void update_io_stats(int64_t bytes_read, int64_t bytes_written, 
                        int64_t io_time, int64_t io_count) {
        _bytes_read_counter->update(bytes_read);
        _bytes_written_counter->update(bytes_written);
        _io_time_counter->update(io_time);
        _io_count_counter->update(io_count);
    }
    
    RuntimeProfile* get_runtime_profile() const {
        return _runtime_profile;
    }
};

// 扫描算子的专用Profile
class ScanOperatorProfile : public OperatorProfile {
private:
    Counter* _tablets_scanned_counter;
    Counter* _segments_scanned_counter;
    Counter* _rows_filtered_counter;
    Counter* _bloom_filter_filtered_counter;
    Counter* _zone_map_filtered_counter;
    Counter* _index_filter_time_counter;
    Counter* _decompress_time_counter;
    Counter* _decode_time_counter;
    
public:
    ScanOperatorProfile(const std::string& name) : OperatorProfile(name) {
        auto* profile = get_runtime_profile();
        
        _tablets_scanned_counter = profile->add_counter("TabletsScanned", TUnit::UNIT);
        _segments_scanned_counter = profile->add_counter("SegmentsScanned", TUnit::UNIT);
        _rows_filtered_counter = profile->add_counter("RowsFiltered", TUnit::UNIT);
        _bloom_filter_filtered_counter = profile->add_counter("BloomFilterFiltered", TUnit::UNIT);
        _zone_map_filtered_counter = profile->add_counter("ZoneMapFiltered", TUnit::UNIT);
        _index_filter_time_counter = profile->add_counter("IndexFilterTime", TUnit::TIME_NS);
        _decompress_time_counter = profile->add_counter("DecompressTime", TUnit::TIME_NS);
        _decode_time_counter = profile->add_counter("DecodeTime", TUnit::TIME_NS);
    }
    
    void update_scan_stats(int64_t tablets, int64_t segments, int64_t rows_filtered) {
        _tablets_scanned_counter->update(tablets);
        _segments_scanned_counter->update(segments);
        _rows_filtered_counter->update(rows_filtered);
    }
    
    void update_filter_stats(int64_t bloom_filtered, int64_t zone_map_filtered, 
                           int64_t filter_time) {
        _bloom_filter_filtered_counter->update(bloom_filtered);
        _zone_map_filtered_counter->update(zone_map_filtered);
        _index_filter_time_counter->update(filter_time);
    }
    
    void update_decode_stats(int64_t decompress_time, int64_t decode_time) {
        _decompress_time_counter->update(decompress_time);
        _decode_time_counter->update(decode_time);
    }
};
```

### 12.3.2 系统资源监控

系统级资源使用监控：

```cpp
class SystemResourceMonitor {
private:
    std::thread _monitor_thread;
    std::atomic<bool> _stop_monitoring{false};
    RuntimeProfile* _system_profile;
    
    // 系统资源计数器
    Counter* _cpu_usage_counter;
    Counter* _memory_usage_counter;
    Counter* _disk_io_counter;
    Counter* _network_io_counter;
    Counter* _thread_count_counter;
    Counter* _file_descriptor_count_counter;
    
public:
    SystemResourceMonitor() {
        _system_profile = new RuntimeProfile("SystemResources");
        
        _cpu_usage_counter = _system_profile->add_counter("CpuUsagePercent", TUnit::UNIT);
        _memory_usage_counter = _system_profile->add_counter("MemoryUsageBytes", TUnit::BYTES);
        _disk_io_counter = _system_profile->add_counter("DiskIoBytes", TUnit::BYTES);
        _network_io_counter = _system_profile->add_counter("NetworkIoBytes", TUnit::BYTES);
        _thread_count_counter = _system_profile->add_counter("ThreadCount", TUnit::UNIT);
        _file_descriptor_count_counter = _system_profile->add_counter("FileDescriptorCount", TUnit::UNIT);
    }
    
    void start_monitoring() {
        _monitor_thread = std::thread([this]() {
            monitor_loop();
        });
    }
    
    void stop_monitoring() {
        _stop_monitoring = true;
        if (_monitor_thread.joinable()) {
            _monitor_thread.join();
        }
    }
    
private:
    void monitor_loop() {
        while (!_stop_monitoring) {
            // 1. 收集CPU使用率
            double cpu_usage = get_cpu_usage();
            _cpu_usage_counter->set(static_cast<int64_t>(cpu_usage * 100));
            
            // 2. 收集内存使用
            size_t memory_usage = get_memory_usage();
            _memory_usage_counter->set(memory_usage);
            
            // 3. 收集磁盘IO
            size_t disk_io = get_disk_io();
            _disk_io_counter->update(disk_io);
            
            // 4. 收集网络IO
            size_t network_io = get_network_io();
            _network_io_counter->update(network_io);
            
            // 5. 收集线程数
            int thread_count = get_thread_count();
            _thread_count_counter->set(thread_count);
            
            // 6. 收集文件描述符数
            int fd_count = get_file_descriptor_count();
            _file_descriptor_count_counter->set(fd_count);
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    double get_cpu_usage() {
        // 读取/proc/stat获取CPU使用率
        static long long last_total = 0, last_idle = 0;
        
        std::ifstream file("/proc/stat");
        std::string line;
        std::getline(file, line);
        
        std::istringstream iss(line);
        std::string cpu;
        long long user, nice, system, idle, iowait, irq, softirq, steal;
        
        iss >> cpu >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
        
        long long total = user + nice + system + idle + iowait + irq + softirq + steal;
        long long total_diff = total - last_total;
        long long idle_diff = idle - last_idle;
        
        double cpu_usage = 0.0;
        if (total_diff > 0) {
            cpu_usage = 1.0 - static_cast<double>(idle_diff) / total_diff;
        }
        
        last_total = total;
        last_idle = idle;
        
        return cpu_usage;
    }
    
    size_t get_memory_usage() {
        // 读取/proc/self/status获取内存使用
        std::ifstream file("/proc/self/status");
        std::string line;
        
        while (std::getline(file, line)) {
            if (line.find("VmRSS:") == 0) {
                std::istringstream iss(line);
                std::string key, value, unit;
                iss >> key >> value >> unit;
                
                size_t memory_kb = std::stoull(value);
                return memory_kb * 1024; // 转换为字节
            }
        }
        
        return 0;
    }
};
```

## 12.4 性能分析工具

### 12.4.1 查询性能分析器

查询性能的深度分析工具：

```java
public class QueryPerformanceAnalyzer {
    
    public PerformanceReport analyzeQuery(String queryId) {
        QueryTraceProfile trace = getQueryTrace(queryId);
        if (trace == null) {
            throw new IllegalArgumentException("Query trace not found: " + queryId);
        }
        
        PerformanceReport report = new PerformanceReport(queryId);
        
        // 1. 分析总体性能
        analyzeOverallPerformance(trace, report);
        
        // 2. 分析瓶颈
        analyzeBottlenecks(trace, report);
        
        // 3. 分析资源使用
        analyzeResourceUsage(trace, report);
        
        // 4. 生成优化建议
        generateOptimizationSuggestions(trace, report);
        
        return report;
    }
    
    private void analyzeOverallPerformance(QueryTraceProfile trace, PerformanceReport report) {
        long totalTime = trace.getEndTime() - trace.getStartTime();
        
        // 分析各阶段耗时
        Map<String, Long> phaseTimings = new HashMap<>();
        phaseTimings.put("Planning", calculatePlanningTime(trace));
        phaseTimings.put("Optimization", calculateOptimizationTime(trace));
        phaseTimings.put("Execution", calculateExecutionTime(trace));
        phaseTimings.put("ResultFetching", calculateResultFetchingTime(trace));
        
        report.setOverallMetrics(totalTime, phaseTimings);
        
        // 计算并行度
        int maxParallelism = calculateMaxParallelism(trace);
        double avgParallelism = calculateAvgParallelism(trace);
        report.setParallelismMetrics(maxParallelism, avgParallelism);
    }
    
    private void analyzeBottlenecks(QueryTraceProfile trace, PerformanceReport report) {
        List<Bottleneck> bottlenecks = new ArrayList<>();
        
        // 1. 分析Fragment级瓶颈
        for (Map.Entry<String, RuntimeProfile> entry : trace.getFragmentProfiles().entrySet()) {
            String fragmentId = entry.getKey();
            RuntimeProfile profile = entry.getValue();
            
            // 检查执行时间异常
            long executionTime = profile.getCounter("TotalTime").value();
            if (executionTime > getExecutionTimeThreshold()) {
                bottlenecks.add(new Bottleneck(BottleneckType.SLOW_FRAGMENT, 
                                             fragmentId, executionTime));
            }
            
            // 检查内存使用异常
            long memoryUsage = profile.getCounter("PeakMemoryUsage").value();
            if (memoryUsage > getMemoryUsageThreshold()) {
                bottlenecks.add(new Bottleneck(BottleneckType.HIGH_MEMORY_USAGE, 
                                             fragmentId, memoryUsage));
            }
            
            // 检查IO瓶颈
            long ioTime = profile.getCounter("IoTime").value();
            if (ioTime > getIoTimeThreshold()) {
                bottlenecks.add(new Bottleneck(BottleneckType.IO_BOTTLENECK, 
                                             fragmentId, ioTime));
            }
        }
        
        // 2. 分析数据倾斜
        analyzeDataSkew(trace, bottlenecks);
        
        // 3. 分析网络瓶颈
        analyzeNetworkBottlenecks(trace, bottlenecks);
        
        report.setBottlenecks(bottlenecks);
    }
    
    private void generateOptimizationSuggestions(QueryTraceProfile trace, PerformanceReport report) {
        List<OptimizationSuggestion> suggestions = new ArrayList<>();
        
        // 基于瓶颈生成建议
        for (Bottleneck bottleneck : report.getBottlenecks()) {
            switch (bottleneck.getType()) {
                case SLOW_FRAGMENT:
                    suggestions.add(new OptimizationSuggestion(
                        "Consider increasing parallelism for fragment " + bottleneck.getLocation(),
                        SuggestionType.PARALLELISM));
                    break;
                    
                case HIGH_MEMORY_USAGE:
                    suggestions.add(new OptimizationSuggestion(
                        "Consider increasing memory limit or optimizing memory usage",
                        SuggestionType.MEMORY));
                    break;
                    
                case IO_BOTTLENECK:
                    suggestions.add(new OptimizationSuggestion(
                        "Consider adding indexes or optimizing storage layout",
                        SuggestionType.STORAGE));
                    break;
                    
                case DATA_SKEW:
                    suggestions.add(new OptimizationSuggestion(
                        "Consider using different distribution keys to reduce data skew",
                        SuggestionType.DISTRIBUTION));
                    break;
            }
        }
        
        // 基于查询模式生成建议
        analyzeQueryPattern(trace, suggestions);
        
        report.setSuggestions(suggestions);
    }
}

public class PerformanceReport {
    private final String queryId;
    private long totalExecutionTime;
    private Map<String, Long> phaseTimings;
    private List<Bottleneck> bottlenecks;
    private List<OptimizationSuggestion> suggestions;
    private Map<String, Object> resourceMetrics;
    
    public String generateHtmlReport() {
        StringBuilder html = new StringBuilder();
        
        html.append("<html><head><title>Query Performance Report</title></head><body>");
        html.append("<h1>Query Performance Report</h1>");
        html.append("<h2>Query ID: ").append(queryId).append("</h2>");
        
        // 总体性能
        html.append("<h3>Overall Performance</h3>");
        html.append("<p>Total Execution Time: ").append(totalExecutionTime).append("ms</p>");
        
        // 阶段耗时
        html.append("<h4>Phase Timings</h4>");
        html.append("<table border='1'>");
        html.append("<tr><th>Phase</th><th>Time (ms)</th><th>Percentage</th></tr>");
        for (Map.Entry<String, Long> entry : phaseTimings.entrySet()) {
            double percentage = (double) entry.getValue() / totalExecutionTime * 100;
            html.append("<tr><td>").append(entry.getKey()).append("</td>");
            html.append("<td>").append(entry.getValue()).append("</td>");
            html.append("<td>").append(String.format("%.2f%%", percentage)).append("</td></tr>");
        }
        html.append("</table>");
        
        // 瓶颈分析
        html.append("<h3>Bottlenecks</h3>");
        if (bottlenecks.isEmpty()) {
            html.append("<p>No significant bottlenecks detected.</p>");
        } else {
            html.append("<ul>");
            for (Bottleneck bottleneck : bottlenecks) {
                html.append("<li>").append(bottleneck.toString()).append("</li>");
            }
            html.append("</ul>");
        }
        
        // 优化建议
        html.append("<h3>Optimization Suggestions</h3>");
        if (suggestions.isEmpty()) {
            html.append("<p>No optimization suggestions available.</p>");
        } else {
            html.append("<ol>");
            for (OptimizationSuggestion suggestion : suggestions) {
                html.append("<li>").append(suggestion.getDescription()).append("</li>");
            }
            html.append("</ol>");
        }
        
        html.append("</body></html>");
        
        return html.toString();
    }
}
```

## 小结

StarRocks的性能监控与调试工具提供了全面的查询性能分析能力，通过多层次的监控体系、详细的性能统计和智能的分析工具，帮助用户深入理解查询执行过程并进行针对性优化。其设计特点包括：

1. **分层监控体系**: 从系统级到算子级的全方位监控
2. **详细的性能统计**: Runtime Profile提供丰富的性能指标
3. **智能分析工具**: 自动识别瓶颈和生成优化建议
4. **可视化报告**: 直观的性能分析报告和图表
5. **实时监控**: 支持查询执行过程的实时跟踪

在下一章中，我们将深入分析容错与高可用机制的设计与实现。
