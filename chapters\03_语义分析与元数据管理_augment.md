# 第三章：语义分析与元数据管理

## 引言

语义分析是SQL查询处理的关键阶段，它在语法分析生成的AST基础上，进行语义检查、类型推导、表和列的绑定等工作。StarRocks的语义分析器采用访问者模式，通过`AnalyzerVisitor`对AST进行深度遍历和分析。本章将深入探讨StarRocks的语义分析机制和元数据管理体系。

## 3.1 语义分析架构概览

### 3.1.1 分析流程架构

StarRocks的语义分析采用多阶段处理模式：

```
AST → 符号绑定 → 类型检查 → 权限验证 → 语义验证 → 逻辑计划
 ↓       ↓        ↓        ↓        ↓         ↓
解析树 → 表/列绑定 → 类型推导 → 权限检查 → 语义规则 → 查询计划
```

### 3.1.2 核心组件分析

基于`AnalyzerVisitor.java`的源码分析，我们可以看到分析器的核心结构：

```java
public class AnalyzerVisitor implements AstVisitor<Void, ConnectContext> {
    
    public Void visitQueryStatement(QueryStatement statement, ConnectContext context) {
        // 1. 分析查询表达式
        visit(statement.getQueryRelation(), context);
        
        // 2. 分析ORDER BY子句
        if (statement.hasOrderByClause()) {
            analyzeOrderByClause(statement.getOrderBy(), context);
        }
        
        // 3. 分析LIMIT子句
        if (statement.hasLimitClause()) {
            analyzeLimitClause(statement.getLimit(), context);
        }
        
        return null;
    }
    
    public Void visitSelectRelation(SelectRelation selectRelation, ConnectContext context) {
        // 1. 分析FROM子句
        if (selectRelation.hasFromClause()) {
            visit(selectRelation.getRelation(), context);
        }
        
        // 2. 分析WHERE子句
        if (selectRelation.hasWhereClause()) {
            analyzeExpression(selectRelation.getWhereClause(), context);
        }
        
        // 3. 分析SELECT列表
        analyzeSelectList(selectRelation.getSelectList(), context);
        
        // 4. 分析GROUP BY子句
        if (selectRelation.hasGroupByClause()) {
            analyzeGroupByClause(selectRelation.getGroupBy(), context);
        }
        
        // 5. 分析HAVING子句
        if (selectRelation.hasHavingClause()) {
            analyzeExpression(selectRelation.getHavingClause(), context);
        }
        
        return null;
    }
}
```

这个分析流程体现了语义分析的系统性方法：
- **自底向上分析**: 先分析子表达式，再分析父表达式
- **上下文传递**: 通过ConnectContext传递分析上下文
- **分阶段处理**: 按照SQL子句的逻辑顺序进行分析

## 3.2 符号绑定机制

### 3.2.1 表绑定分析

表绑定是语义分析的第一步，需要将表名解析为具体的表对象：

```java
public class TableResolver {
    public Table resolveTable(TableName tableName, ConnectContext context) {
        // 1. 获取数据库名
        String dbName = tableName.getDb();
        if (dbName == null) {
            dbName = context.getDatabase();
        }
        
        // 2. 从Catalog中查找表
        Database database = GlobalStateMgr.getCurrentState()
                                         .getDb(dbName);
        if (database == null) {
            throw new SemanticException("Database '" + dbName + "' not found");
        }
        
        Table table = database.getTable(tableName.getTbl());
        if (table == null) {
            throw new SemanticException("Table '" + tableName.getTbl() + "' not found");
        }
        
        // 3. 权限检查
        checkTablePrivilege(table, context);
        
        return table;
    }
    
    private void checkTablePrivilege(Table table, ConnectContext context) {
        if (!GlobalStateMgr.getCurrentState().getAuth()
                          .checkTblPriv(context, table.getName(), PrivPredicate.SELECT)) {
            throw new SemanticException("Access denied for table: " + table.getName());
        }
    }
}
```

### 3.2.2 列绑定分析

列绑定需要将列引用解析为具体的列对象：

```java
public class ColumnResolver {
    public Column resolveColumn(SlotRef slotRef, Scope scope) {
        String columnName = slotRef.getColumnName();
        String tableName = slotRef.getTblNameWithoutAnalyzed();
        
        // 1. 在当前作用域中查找列
        List<Column> candidates = new ArrayList<>();
        
        for (TableRef tableRef : scope.getTableRefs()) {
            Table table = tableRef.getTable();
            
            // 如果指定了表名，只在该表中查找
            if (tableName != null && !tableName.equals(table.getName())) {
                continue;
            }
            
            Column column = table.getColumn(columnName);
            if (column != null) {
                candidates.add(column);
            }
        }
        
        // 2. 处理查找结果
        if (candidates.isEmpty()) {
            throw new SemanticException("Column '" + columnName + "' not found");
        } else if (candidates.size() > 1) {
            throw new SemanticException("Column '" + columnName + "' is ambiguous");
        }
        
        Column column = candidates.get(0);
        
        // 3. 设置列的元数据信息
        slotRef.setColumn(column);
        slotRef.setType(column.getType());
        
        return column;
    }
}
```

### 3.2.3 作用域管理

作用域管理是符号绑定的基础：

```java
public class Scope {
    private final Scope parent;
    private final List<TableRef> tableRefs;
    private final Map<String, Expr> aliases;
    
    public Scope(Scope parent) {
        this.parent = parent;
        this.tableRefs = new ArrayList<>();
        this.aliases = new HashMap<>();
    }
    
    public void addTableRef(TableRef tableRef) {
        tableRefs.add(tableRef);
    }
    
    public void addAlias(String alias, Expr expr) {
        aliases.put(alias, expr);
    }
    
    public Column resolveColumn(String columnName, String tableName) {
        // 1. 在当前作用域查找
        Column column = resolveInCurrentScope(columnName, tableName);
        if (column != null) {
            return column;
        }
        
        // 2. 在父作用域查找
        if (parent != null) {
            return parent.resolveColumn(columnName, tableName);
        }
        
        return null;
    }
}
```

## 3.3 类型系统与类型推导

### 3.3.1 类型系统架构

StarRocks的类型系统支持丰富的数据类型：

```java
public abstract class Type {
    public enum PrimitiveType {
        BOOLEAN,
        TINYINT, SMALLINT, INT, BIGINT,
        LARGEINT, FLOAT, DOUBLE, DECIMAL,
        CHAR, VARCHAR, STRING,
        DATE, DATETIME, TIME,
        ARRAY, MAP, STRUCT,
        JSON, HLL, BITMAP
    }
    
    public abstract boolean isAssignableFrom(Type other);
    public abstract Type getCommonType(Type other);
    public abstract int getSlotSize();
    public abstract String toSql();
}

public class ScalarType extends Type {
    private final PrimitiveType type;
    private final int precision;
    private final int scale;
    
    public ScalarType(PrimitiveType type, int precision, int scale) {
        this.type = type;
        this.precision = precision;
        this.scale = scale;
    }
    
    @Override
    public boolean isAssignableFrom(Type other) {
        if (!(other instanceof ScalarType)) {
            return false;
        }
        
        ScalarType otherScalar = (ScalarType) other;
        
        // 数值类型的兼容性检查
        if (isNumericType() && otherScalar.isNumericType()) {
            return getNumericPrecedence() >= otherScalar.getNumericPrecedence();
        }
        
        // 字符串类型的兼容性检查
        if (isStringType() && otherScalar.isStringType()) {
            return true;
        }
        
        // 精确匹配
        return type == otherScalar.type;
    }
}
```

### 3.3.2 表达式类型推导

表达式的类型推导是语义分析的核心：

```java
public class TypeAnalyzer {
    public Type analyzeExpression(Expr expr, Scope scope) {
        if (expr instanceof SlotRef) {
            return analyzeSlotRef((SlotRef) expr, scope);
        } else if (expr instanceof LiteralExpr) {
            return analyzeLiteral((LiteralExpr) expr);
        } else if (expr instanceof BinaryPredicate) {
            return analyzeBinaryPredicate((BinaryPredicate) expr, scope);
        } else if (expr instanceof FunctionCallExpr) {
            return analyzeFunctionCall((FunctionCallExpr) expr, scope);
        } else if (expr instanceof CastExpr) {
            return analyzeCastExpr((CastExpr) expr, scope);
        }
        
        throw new SemanticException("Unsupported expression type: " + expr.getClass());
    }
    
    private Type analyzeBinaryPredicate(BinaryPredicate predicate, Scope scope) {
        // 1. 分析左右操作数的类型
        Type leftType = analyzeExpression(predicate.getChild(0), scope);
        Type rightType = analyzeExpression(predicate.getChild(1), scope);
        
        // 2. 检查类型兼容性
        Type commonType = getCommonType(leftType, rightType);
        if (commonType == null) {
            throw new SemanticException(
                "Incompatible types: " + leftType + " and " + rightType);
        }
        
        // 3. 插入类型转换
        if (!leftType.equals(commonType)) {
            predicate.setChild(0, new CastExpr(commonType, predicate.getChild(0)));
        }
        if (!rightType.equals(commonType)) {
            predicate.setChild(1, new CastExpr(commonType, predicate.getChild(1)));
        }
        
        // 4. 比较操作的结果类型总是BOOLEAN
        return Type.BOOLEAN;
    }
}
```

### 3.3.3 函数重载解析

函数重载解析需要根据参数类型选择最佳匹配：

```java
public class FunctionResolver {
    public Function resolveFunction(String functionName, 
                                  List<Type> argTypes) {
        // 1. 获取候选函数
        List<Function> candidates = FunctionSet.getFunctions(functionName);
        if (candidates.isEmpty()) {
            throw new SemanticException("Function '" + functionName + "' not found");
        }
        
        // 2. 精确匹配
        Function exactMatch = findExactMatch(candidates, argTypes);
        if (exactMatch != null) {
            return exactMatch;
        }
        
        // 3. 类型提升匹配
        Function promotionMatch = findPromotionMatch(candidates, argTypes);
        if (promotionMatch != null) {
            return promotionMatch;
        }
        
        // 4. 类型转换匹配
        Function castMatch = findCastMatch(candidates, argTypes);
        if (castMatch != null) {
            return castMatch;
        }
        
        throw new SemanticException(
            "No matching function for " + functionName + 
            " with arguments " + argTypes);
    }
    
    private Function findPromotionMatch(List<Function> candidates, 
                                      List<Type> argTypes) {
        for (Function candidate : candidates) {
            if (candidate.getArgs().size() != argTypes.size()) {
                continue;
            }
            
            boolean canPromote = true;
            for (int i = 0; i < argTypes.size(); i++) {
                Type argType = argTypes.get(i);
                Type paramType = candidate.getArgs().get(i);
                
                if (!canPromoteType(argType, paramType)) {
                    canPromote = false;
                    break;
                }
            }
            
            if (canPromote) {
                return candidate;
            }
        }
        
        return null;
    }
}
```

## 3.4 元数据管理体系

### 3.4.1 Catalog架构

StarRocks的元数据管理采用分层的Catalog架构：

```java
public class GlobalStateMgr {
    private static final GlobalStateMgr INSTANCE = new GlobalStateMgr();
    
    // 数据库管理
    private final ConcurrentHashMap<Long, Database> idToDb = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Database> fullNameToDb = new ConcurrentHashMap<>();
    
    // 表管理
    private final TabletInvertedIndex tabletInvertedIndex = new TabletInvertedIndex();
    
    // 函数管理
    private final FunctionSet functionSet = new FunctionSet();
    
    // 权限管理
    private final Auth auth = new Auth();
    
    public Database getDb(String name) {
        return fullNameToDb.get(name);
    }
    
    public Database getDb(long dbId) {
        return idToDb.get(dbId);
    }
    
    public void createDb(String dbName) throws DdlException {
        if (fullNameToDb.containsKey(dbName)) {
            throw new DdlException("Database '" + dbName + "' already exists");
        }
        
        Database db = new Database(getNextId(), dbName);
        idToDb.put(db.getId(), db);
        fullNameToDb.put(dbName, db);
        
        // 持久化元数据
        EditLog.getInstance().logCreateDb(db);
    }
}
```

### 3.4.2 数据库元数据

数据库级别的元数据管理：

```java
public class Database {
    private final long id;
    private final String name;
    private final ConcurrentHashMap<Long, Table> idToTable = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Table> nameToTable = new ConcurrentHashMap<>();
    
    public Database(long id, String name) {
        this.id = id;
        this.name = name;
    }
    
    public Table getTable(String tableName) {
        return nameToTable.get(tableName);
    }
    
    public Table getTable(long tableId) {
        return idToTable.get(tableId);
    }
    
    public void createTable(Table table) throws DdlException {
        if (nameToTable.containsKey(table.getName())) {
            throw new DdlException("Table '" + table.getName() + "' already exists");
        }
        
        idToTable.put(table.getId(), table);
        nameToTable.put(table.getName(), table);
        
        // 持久化表元数据
        EditLog.getInstance().logCreateTable(table);
    }
    
    public List<Table> getTables() {
        return new ArrayList<>(nameToTable.values());
    }
}
```

### 3.4.3 表元数据

表级别的元数据管理：

```java
public abstract class Table {
    protected long id;
    protected String name;
    protected TableType type;
    protected List<Column> baseSchema = new ArrayList<>();
    protected Map<String, String> properties = new HashMap<>();
    
    public Table(long id, String name, TableType type, List<Column> schema) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.baseSchema = schema;
    }
    
    public Column getColumn(String columnName) {
        for (Column column : baseSchema) {
            if (column.getName().equalsIgnoreCase(columnName)) {
                return column;
            }
        }
        return null;
    }
    
    public List<Column> getColumns() {
        return new ArrayList<>(baseSchema);
    }
    
    public abstract long getRowCount();
    public abstract long getDataSize();
}

public class OlapTable extends Table {
    private PartitionInfo partitionInfo;
    private DistributionInfo defaultDistributionInfo;
    private Map<Long, Partition> idToPartition = new ConcurrentHashMap<>();
    
    public OlapTable(long id, String name, List<Column> schema,
                     KeysType keysType, PartitionInfo partitionInfo,
                     DistributionInfo distributionInfo) {
        super(id, name, TableType.OLAP, schema);
        this.partitionInfo = partitionInfo;
        this.defaultDistributionInfo = distributionInfo;
    }
    
    @Override
    public long getRowCount() {
        long totalRows = 0;
        for (Partition partition : idToPartition.values()) {
            totalRows += partition.getRowCount();
        }
        return totalRows;
    }
}
```

## 3.5 语义验证规则

### 3.5.1 SELECT语句验证

SELECT语句的语义验证：

```java
public class SelectStatementValidator {
    public void validate(SelectRelation selectRelation, ConnectContext context) {
        // 1. 验证SELECT列表
        validateSelectList(selectRelation.getSelectList(), context);
        
        // 2. 验证GROUP BY子句
        if (selectRelation.hasGroupByClause()) {
            validateGroupByClause(selectRelation, context);
        }
        
        // 3. 验证HAVING子句
        if (selectRelation.hasHavingClause()) {
            validateHavingClause(selectRelation, context);
        }
        
        // 4. 验证ORDER BY子句
        if (selectRelation.hasOrderByClause()) {
            validateOrderByClause(selectRelation, context);
        }
    }
    
    private void validateGroupByClause(SelectRelation selectRelation, 
                                     ConnectContext context) {
        List<Expr> groupByExprs = selectRelation.getGroupBy().getGroupingExprs();
        List<SelectListItem> selectItems = selectRelation.getSelectList().getItems();
        
        // 检查SELECT列表中的非聚合表达式是否都在GROUP BY中
        for (SelectListItem item : selectItems) {
            Expr expr = item.getExpr();
            
            if (!isAggregateExpr(expr) && !containsExpr(groupByExprs, expr)) {
                throw new SemanticException(
                    "Column '" + expr.toSql() + 
                    "' must appear in GROUP BY clause or be used in aggregate function");
            }
        }
    }
}
```

### 3.5.2 子查询验证

子查询的语义验证：

```java
public class SubqueryValidator {
    public void validateSubquery(Subquery subquery, ConnectContext context) {
        QueryStatement queryStmt = subquery.getQueryStatement();
        
        // 1. 验证标量子查询
        if (subquery.getType() == Subquery.Type.SCALAR) {
            validateScalarSubquery(queryStmt);
        }
        
        // 2. 验证EXISTS子查询
        if (subquery.getType() == Subquery.Type.EXISTS) {
            validateExistsSubquery(queryStmt);
        }
        
        // 3. 验证IN子查询
        if (subquery.getType() == Subquery.Type.IN) {
            validateInSubquery(queryStmt, subquery.getLeftExpr());
        }
    }
    
    private void validateScalarSubquery(QueryStatement queryStmt) {
        SelectRelation selectRelation = (SelectRelation) queryStmt.getQueryRelation();
        
        // 标量子查询只能返回一列
        if (selectRelation.getSelectList().getItems().size() != 1) {
            throw new SemanticException(
                "Scalar subquery must return exactly one column");
        }
        
        // 标量子查询不能包含聚合函数（除非有GROUP BY）
        if (!selectRelation.hasGroupByClause()) {
            for (SelectListItem item : selectRelation.getSelectList().getItems()) {
                if (containsAggregateFunction(item.getExpr())) {
                    throw new SemanticException(
                        "Scalar subquery cannot contain aggregate functions without GROUP BY");
                }
            }
        }
    }
}
```

### 3.5.3 窗口函数验证

窗口函数的语义验证：

```java
public class WindowFunctionValidator {
    public void validateWindowFunction(AnalyticExpr analyticExpr, 
                                     ConnectContext context) {
        FunctionCallExpr functionCall = analyticExpr.getFnCall();
        
        // 1. 验证窗口函数类型
        if (!isWindowFunction(functionCall.getFnName())) {
            throw new SemanticException(
                "Function '" + functionCall.getFnName() + 
                "' is not a window function");
        }
        
        // 2. 验证PARTITION BY子句
        if (analyticExpr.getPartitionExprs() != null) {
            for (Expr partitionExpr : analyticExpr.getPartitionExprs()) {
                validatePartitionExpression(partitionExpr);
            }
        }
        
        // 3. 验证ORDER BY子句
        if (analyticExpr.getOrderByElements() != null) {
            for (OrderByElement orderByElement : analyticExpr.getOrderByElements()) {
                validateOrderByExpression(orderByElement.getExpr());
            }
        }
        
        // 4. 验证窗口框架
        if (analyticExpr.getWindow() != null) {
            validateWindowFrame(analyticExpr.getWindow(), analyticExpr);
        }
    }
}
```

## 3.6 权限验证机制

### 3.6.1 权限模型

StarRocks的权限模型：

```java
public class Auth {
    private final Map<String, User> nameToUser = new ConcurrentHashMap<>();
    private final Map<String, Role> nameToRole = new ConcurrentHashMap<>();
    
    public boolean checkTblPriv(ConnectContext context, String tableName, 
                               PrivPredicate predicate) {
        User user = getUser(context.getQualifiedUser());
        if (user == null) {
            return false;
        }
        
        // 1. 检查用户直接权限
        if (user.hasPrivilege(tableName, predicate)) {
            return true;
        }
        
        // 2. 检查角色权限
        for (String roleName : user.getRoles()) {
            Role role = getRole(roleName);
            if (role != null && role.hasPrivilege(tableName, predicate)) {
                return true;
            }
        }
        
        return false;
    }
    
    public boolean checkDbPriv(ConnectContext context, String dbName, 
                              PrivPredicate predicate) {
        User user = getUser(context.getQualifiedUser());
        if (user == null) {
            return false;
        }
        
        return user.hasDbPrivilege(dbName, predicate);
    }
}
```

### 3.6.2 细粒度权限控制

列级别的权限控制：

```java
public class ColumnPrivilegeChecker {
    public void checkColumnPrivilege(SlotRef slotRef, ConnectContext context) {
        String tableName = slotRef.getTblNameWithoutAnalyzed();
        String columnName = slotRef.getColumnName();
        
        // 检查列级别的SELECT权限
        if (!GlobalStateMgr.getCurrentState().getAuth()
                          .checkColumnPriv(context, tableName, columnName, 
                                         PrivPredicate.SELECT)) {
            throw new SemanticException(
                "Access denied for column: " + tableName + "." + columnName);
        }
    }
    
    public void checkUpdatePrivilege(UpdateStmt updateStmt, ConnectContext context) {
        String tableName = updateStmt.getTableName().getTbl();
        
        // 检查被更新列的UPDATE权限
        for (Expr assignmentExpr : updateStmt.getAssignmentExprs()) {
            if (assignmentExpr instanceof SlotRef) {
                SlotRef slotRef = (SlotRef) assignmentExpr;
                String columnName = slotRef.getColumnName();
                
                if (!GlobalStateMgr.getCurrentState().getAuth()
                                  .checkColumnPriv(context, tableName, columnName, 
                                                 PrivPredicate.UPDATE)) {
                    throw new SemanticException(
                        "Access denied for updating column: " + 
                        tableName + "." + columnName);
                }
            }
        }
    }
}
```

## 3.7 性能优化策略

### 3.7.1 元数据缓存

```java
public class MetadataCache {
    private final Cache<String, Table> tableCache = 
        CacheBuilder.newBuilder()
                   .maximumSize(10000)
                   .expireAfterWrite(30, TimeUnit.MINUTES)
                   .build();
    
    private final Cache<String, Database> databaseCache = 
        CacheBuilder.newBuilder()
                   .maximumSize(1000)
                   .expireAfterWrite(60, TimeUnit.MINUTES)
                   .build();
    
    public Table getTable(String dbName, String tableName) {
        String key = dbName + "." + tableName;
        return tableCache.getIfPresent(key);
    }
    
    public void putTable(String dbName, String tableName, Table table) {
        String key = dbName + "." + tableName;
        tableCache.put(key, table);
    }
    
    public void invalidateTable(String dbName, String tableName) {
        String key = dbName + "." + tableName;
        tableCache.invalidate(key);
    }
}
```

### 3.7.2 并行语义分析

```java
public class ParallelAnalyzer {
    private final ExecutorService executor = 
        Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    
    public void analyzeInParallel(List<Expr> expressions, ConnectContext context) {
        List<Future<Void>> futures = new ArrayList<>();
        
        for (Expr expr : expressions) {
            Future<Void> future = executor.submit(() -> {
                AnalyzerVisitor visitor = new AnalyzerVisitor();
                visitor.visit(expr, context);
                return null;
            });
            futures.add(future);
        }
        
        // 等待所有分析完成
        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                throw new SemanticException("Parallel analysis failed", e);
            }
        }
    }
}
```

## 小结

StarRocks的语义分析系统采用了系统化的分析方法，通过符号绑定、类型推导、语义验证等多个阶段，确保SQL查询的语义正确性。其设计特点包括：

1. **模块化设计**: 符号绑定、类型检查、权限验证分离
2. **类型系统完善**: 支持丰富的数据类型和类型转换
3. **元数据管理高效**: 分层的Catalog架构和缓存机制
4. **权限控制细粒度**: 支持表级和列级权限控制
5. **性能优化全面**: 缓存、并行分析等优化策略

在下一章中，我们将深入分析查询优化器的核心原理，了解StarRocks如何在语义分析的基础上进行查询优化。
