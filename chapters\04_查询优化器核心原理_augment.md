# 第四章：查询优化器核心原理

## 引言

查询优化器是数据库系统的核心组件，负责将逻辑查询计划转换为高效的物理执行计划。StarRocks采用基于代价的优化器(CBO)，结合了Memo数据结构和动态规划搜索算法，实现了先进的查询优化技术。本章将深入分析StarRocks查询优化器的核心原理和实现机制。

## 4.1 优化器架构概览

### 4.1.1 优化流程架构

StarRocks的查询优化采用多阶段优化模式：

```
逻辑计划 → RBO优化 → CBO优化 → 物理计划生成 → 后处理优化
    ↓        ↓        ↓         ↓           ↓
  语义分析 → 规则优化 → 代价优化 → 算子选择 → 计划调整
```

### 4.1.2 核心组件分析

基于`Memo.java`的源码分析，我们可以看到优化器的核心数据结构：

```java
public class Memo {
    // Group管理
    private final List<Group> groups = new ArrayList<>();
    private final Map<GroupExpression, Group> groupExpressionToGroup = new HashMap<>();
    
    // 根Group
    private Group rootGroup;
    
    // 统计信息
    private final ColumnStatistic columnStatistics = new ColumnStatistic();
    
    public Group insertGroupExpression(GroupExpression groupExpression, 
                                     Group targetGroup) {
        // 1. 检查是否已存在相同的GroupExpression
        Group existingGroup = groupExpressionToGroup.get(groupExpression);
        if (existingGroup != null) {
            return existingGroup;
        }
        
        // 2. 创建新Group或使用目标Group
        Group group = targetGroup;
        if (group == null) {
            group = new Group(groups.size());
            groups.add(group);
        }
        
        // 3. 添加GroupExpression到Group
        group.addGroupExpression(groupExpression);
        groupExpressionToGroup.put(groupExpression, group);
        
        // 4. 设置逻辑属性
        if (group.getLogicalProperty() == null) {
            LogicalProperty logicalProperty = 
                deriveLogicalProperty(groupExpression);
            group.setLogicalProperty(logicalProperty);
        }
        
        return group;
    }
    
    public void optimize(OptimizerContext context) {
        // 1. RBO优化阶段
        applyRuleBasedOptimization(context);
        
        // 2. CBO优化阶段
        applyCostBasedOptimization(context);
        
        // 3. 后处理优化
        applyPostOptimization(context);
    }
}
```

这个Memo结构体现了现代查询优化器的核心设计：
- **Group**: 表示等价的表达式集合
- **GroupExpression**: 表示具体的算子表达式
- **逻辑属性**: 表示输出的逻辑特征
- **物理属性**: 表示执行的物理特征

## 4.2 Memo数据结构深度解析

### 4.2.1 Group设计

Group是Memo结构的核心概念，表示逻辑等价的表达式集合：

```java
public class Group {
    private final int id;
    private final List<GroupExpression> logicalExpressions = new ArrayList<>();
    private final List<GroupExpression> physicalExpressions = new ArrayList<>();
    
    // 逻辑属性（所有表达式共享）
    private LogicalProperty logicalProperty;
    
    // 最优计划缓存
    private final Map<PhysicalProperty, GroupExpression> bestExpressionMap = 
        new HashMap<>();
    private final Map<PhysicalProperty, Double> lowestCostMap = new HashMap<>();
    
    public Group(int id) {
        this.id = id;
    }
    
    public void addGroupExpression(GroupExpression groupExpression) {
        if (groupExpression.getOperator().isLogical()) {
            logicalExpressions.add(groupExpression);
        } else {
            physicalExpressions.add(groupExpression);
        }
        groupExpression.setGroup(this);
    }
    
    public GroupExpression getBestExpression(PhysicalProperty physicalProperty) {
        return bestExpressionMap.get(physicalProperty);
    }
    
    public void setBestExpression(PhysicalProperty physicalProperty,
                                GroupExpression expression, double cost) {
        bestExpressionMap.put(physicalProperty, expression);
        lowestCostMap.put(physicalProperty, cost);
    }
}
```

### 4.2.2 GroupExpression设计

GroupExpression表示具体的算子表达式：

```java
public class GroupExpression {
    private final Operator operator;
    private final List<Group> childGroups;
    private Group group;
    
    // 代价信息
    private double cost = -1;
    private Statistics statistics;
    
    // 优化状态
    private boolean hasExplored = false;
    private boolean hasOptimized = false;
    
    public GroupExpression(Operator operator, List<Group> childGroups) {
        this.operator = operator;
        this.childGroups = childGroups;
    }
    
    public OptExpression extractOptExpression() {
        List<OptExpression> childExpressions = new ArrayList<>();
        
        for (Group childGroup : childGroups) {
            // 递归提取子表达式
            GroupExpression bestChild = childGroup.getBestExpression(
                getRequiredProperty());
            childExpressions.add(bestChild.extractOptExpression());
        }
        
        return OptExpression.create(operator, childExpressions);
    }
    
    public double getCost(PhysicalProperty physicalProperty) {
        if (cost < 0) {
            // 计算代价
            cost = calculateCost(physicalProperty);
        }
        return cost;
    }
}
```

### 4.2.3 逻辑属性推导

逻辑属性的推导是优化器的基础：

```java
public class LogicalPropertyDeriver {
    public LogicalProperty derive(GroupExpression groupExpression) {
        Operator operator = groupExpression.getOperator();
        
        if (operator instanceof LogicalScanOperator) {
            return deriveForScan((LogicalScanOperator) operator);
        } else if (operator instanceof LogicalJoinOperator) {
            return deriveForJoin((LogicalJoinOperator) operator, groupExpression);
        } else if (operator instanceof LogicalProjectOperator) {
            return deriveForProject((LogicalProjectOperator) operator, groupExpression);
        } else if (operator instanceof LogicalAggregationOperator) {
            return deriveForAggregation((LogicalAggregationOperator) operator, groupExpression);
        }
        
        throw new OptimizerException("Unsupported operator: " + operator);
    }
    
    private LogicalProperty deriveForJoin(LogicalJoinOperator joinOp,
                                        GroupExpression groupExpression) {
        // 1. 获取子节点的逻辑属性
        LogicalProperty leftProperty = groupExpression.getChildGroups().get(0)
                                                     .getLogicalProperty();
        LogicalProperty rightProperty = groupExpression.getChildGroups().get(1)
                                                      .getLogicalProperty();
        
        // 2. 推导输出列
        ColumnRefSet outputColumns = new ColumnRefSet();
        outputColumns.union(leftProperty.getOutputColumns());
        outputColumns.union(rightProperty.getOutputColumns());
        
        // 3. 推导函数依赖
        FunctionalDependency fd = new FunctionalDependency();
        fd.addAll(leftProperty.getFunctionalDependency());
        fd.addAll(rightProperty.getFunctionalDependency());
        
        // 4. 推导唯一键
        Set<ColumnRefSet> uniqueKeys = new HashSet<>();
        if (joinOp.getJoinType() == JoinOperator.Type.INNER) {
            uniqueKeys.addAll(leftProperty.getUniqueKeys());
            uniqueKeys.addAll(rightProperty.getUniqueKeys());
        }
        
        return new LogicalProperty(outputColumns, fd, uniqueKeys);
    }
}
```

## 4.3 基于规则的优化(RBO)

### 4.3.1 优化规则框架

StarRocks的RBO采用规则模式匹配和转换：

```java
public abstract class Rule {
    private final RuleType ruleType;
    private final Pattern pattern;
    
    public Rule(RuleType ruleType, Pattern pattern) {
        this.ruleType = ruleType;
        this.pattern = pattern;
    }
    
    public abstract List<OptExpression> transform(OptExpression input,
                                                OptimizerContext context);
    
    public boolean check(OptExpression expression, OptimizerContext context) {
        return pattern.matches(expression);
    }
}

// 谓词下推规则示例
public class PredicatePushDownRule extends Rule {
    public PredicatePushDownRule() {
        super(RuleType.TF_PREDICATE_PUSH_DOWN,
              Pattern.create(OperatorType.LOGICAL_FILTER)
                     .addChildren(Pattern.create(OperatorType.LOGICAL_JOIN)));
    }
    
    @Override
    public List<OptExpression> transform(OptExpression input,
                                       OptimizerContext context) {
        LogicalFilterOperator filter = (LogicalFilterOperator) input.getOp();
        OptExpression joinExpr = input.getInputs().get(0);
        LogicalJoinOperator join = (LogicalJoinOperator) joinExpr.getOp();
        
        // 1. 分析谓词
        List<ScalarOperator> predicates = Utils.extractConjuncts(filter.getPredicate());
        List<ScalarOperator> leftPredicates = new ArrayList<>();
        List<ScalarOperator> rightPredicates = new ArrayList<>();
        List<ScalarOperator> joinPredicates = new ArrayList<>();
        
        for (ScalarOperator predicate : predicates) {
            ColumnRefSet usedColumns = predicate.getUsedColumns();
            ColumnRefSet leftColumns = joinExpr.getInputs().get(0)
                                              .getLogicalProperty().getOutputColumns();
            ColumnRefSet rightColumns = joinExpr.getInputs().get(1)
                                               .getLogicalProperty().getOutputColumns();
            
            if (leftColumns.containsAll(usedColumns)) {
                leftPredicates.add(predicate);
            } else if (rightColumns.containsAll(usedColumns)) {
                rightPredicates.add(predicate);
            } else {
                joinPredicates.add(predicate);
            }
        }
        
        // 2. 构建新的表达式树
        OptExpression leftChild = joinExpr.getInputs().get(0);
        OptExpression rightChild = joinExpr.getInputs().get(1);
        
        // 下推左侧谓词
        if (!leftPredicates.isEmpty()) {
            ScalarOperator leftPredicate = Utils.compoundAnd(leftPredicates);
            leftChild = OptExpression.create(
                new LogicalFilterOperator(leftPredicate),
                leftChild);
        }
        
        // 下推右侧谓词
        if (!rightPredicates.isEmpty()) {
            ScalarOperator rightPredicate = Utils.compoundAnd(rightPredicates);
            rightChild = OptExpression.create(
                new LogicalFilterOperator(rightPredicate),
                rightChild);
        }
        
        // 3. 重构Join
        OptExpression newJoin = OptExpression.create(join, leftChild, rightChild);
        
        // 4. 处理剩余谓词
        if (!joinPredicates.isEmpty()) {
            ScalarOperator remainingPredicate = Utils.compoundAnd(joinPredicates);
            return Lists.newArrayList(OptExpression.create(
                new LogicalFilterOperator(remainingPredicate), newJoin));
        } else {
            return Lists.newArrayList(newJoin);
        }
    }
}
```

### 4.3.2 常见优化规则

**投影下推规则**:
```java
public class ProjectionPushDownRule extends Rule {
    @Override
    public List<OptExpression> transform(OptExpression input,
                                       OptimizerContext context) {
        LogicalProjectOperator project = (LogicalProjectOperator) input.getOp();
        OptExpression child = input.getInputs().get(0);
        
        // 计算所需的列
        ColumnRefSet requiredColumns = new ColumnRefSet();
        for (ColumnRefOperator column : project.getColumnRefMap().keySet()) {
            ScalarOperator expr = project.getColumnRefMap().get(column);
            requiredColumns.union(expr.getUsedColumns());
        }
        
        // 下推投影
        return pushProjection(child, requiredColumns);
    }
}
```

**连接重排序规则**:
```java
public class JoinReorderRule extends Rule {
    @Override
    public List<OptExpression> transform(OptExpression input,
                                       OptimizerContext context) {
        // 1. 提取连接树
        List<OptExpression> joinNodes = extractJoinNodes(input);
        
        // 2. 生成所有可能的连接顺序
        List<List<OptExpression>> permutations = generatePermutations(joinNodes);
        
        // 3. 为每种顺序生成连接树
        List<OptExpression> alternatives = new ArrayList<>();
        for (List<OptExpression> permutation : permutations) {
            OptExpression joinTree = buildJoinTree(permutation);
            alternatives.add(joinTree);
        }
        
        return alternatives;
    }
}
```

### 4.3.3 规则应用引擎

```java
public class RuleBasedOptimizer {
    private final List<Rule> transformationRules;
    private final List<Rule> implementationRules;
    
    public OptExpression optimize(OptExpression root, OptimizerContext context) {
        // 1. 应用转换规则
        OptExpression transformed = applyTransformationRules(root, context);
        
        // 2. 应用实现规则
        OptExpression implemented = applyImplementationRules(transformed, context);
        
        return implemented;
    }
    
    private OptExpression applyTransformationRules(OptExpression root,
                                                  OptimizerContext context) {
        boolean changed = true;
        OptExpression current = root;
        
        while (changed) {
            changed = false;
            
            for (Rule rule : transformationRules) {
                if (rule.check(current, context)) {
                    List<OptExpression> alternatives = rule.transform(current, context);
                    
                    if (!alternatives.isEmpty()) {
                        // 选择第一个替代方案（RBO不考虑代价）
                        current = alternatives.get(0);
                        changed = true;
                        break;
                    }
                }
            }
        }
        
        return current;
    }
}
```

## 4.4 基于代价的优化(CBO)

### 4.4.1 代价模型

StarRocks的代价模型考虑CPU、IO、网络等多个因素：

```java
public class CostEstimator {
    public double calculateCost(GroupExpression groupExpression,
                              List<PhysicalProperty> childrenProperties,
                              PhysicalProperty requiredProperty) {
        
        Operator operator = groupExpression.getOperator();
        
        if (operator instanceof PhysicalScanOperator) {
            return calculateScanCost((PhysicalScanOperator) operator);
        } else if (operator instanceof PhysicalJoinOperator) {
            return calculateJoinCost((PhysicalJoinOperator) operator,
                                   groupExpression, childrenProperties);
        } else if (operator instanceof PhysicalAggregationOperator) {
            return calculateAggregationCost((PhysicalAggregationOperator) operator,
                                          groupExpression);
        }
        
        return 0.0;
    }
    
    private double calculateJoinCost(PhysicalJoinOperator joinOp,
                                   GroupExpression groupExpression,
                                   List<PhysicalProperty> childrenProperties) {
        
        Statistics leftStats = groupExpression.getChildGroups().get(0).getStatistics();
        Statistics rightStats = groupExpression.getChildGroups().get(1).getStatistics();
        
        double leftRowCount = leftStats.getRowCount();
        double rightRowCount = rightStats.getRowCount();
        
        // 根据连接算法计算代价
        if (joinOp.getJoinType() == JoinOperator.Type.HASH_JOIN) {
            // Hash Join代价 = 构建Hash表代价 + 探测代价
            double buildCost = rightRowCount * CostConstants.CPU_COST_PER_ROW;
            double probeCost = leftRowCount * CostConstants.CPU_COST_PER_ROW;
            
            // 考虑选择率
            double selectivity = calculateJoinSelectivity(joinOp, leftStats, rightStats);
            double outputRows = leftRowCount * rightRowCount * selectivity;
            double outputCost = outputRows * CostConstants.CPU_COST_PER_ROW;
            
            return buildCost + probeCost + outputCost;
            
        } else if (joinOp.getJoinType() == JoinOperator.Type.MERGE_JOIN) {
            // Merge Join代价 = 排序代价 + 合并代价
            double sortCost = (leftRowCount * Math.log(leftRowCount) + 
                             rightRowCount * Math.log(rightRowCount)) * 
                             CostConstants.CPU_COST_PER_ROW;
            double mergeCost = (leftRowCount + rightRowCount) * 
                             CostConstants.CPU_COST_PER_ROW;
            
            return sortCost + mergeCost;
        }
        
        return Double.MAX_VALUE; // 不支持的连接类型
    }
}
```

### 4.4.2 统计信息管理

统计信息是CBO的基础：

```java
public class Statistics {
    private double rowCount;
    private Map<ColumnRefOperator, ColumnStatistic> columnStatistics;
    
    public Statistics(double rowCount) {
        this.rowCount = rowCount;
        this.columnStatistics = new HashMap<>();
    }
    
    public double getRowCount() {
        return rowCount;
    }
    
    public ColumnStatistic getColumnStatistic(ColumnRefOperator column) {
        return columnStatistics.get(column);
    }
    
    public void addColumnStatistic(ColumnRefOperator column, 
                                 ColumnStatistic statistic) {
        columnStatistics.put(column, statistic);
    }
    
    // 连接统计信息估算
    public static Statistics estimateJoin(Statistics leftStats, 
                                        Statistics rightStats,
                                        List<BinaryPredicateOperator> joinConditions) {
        
        double selectivity = 1.0;
        
        for (BinaryPredicateOperator condition : joinConditions) {
            ColumnRefOperator leftColumn = (ColumnRefOperator) condition.getChild(0);
            ColumnRefOperator rightColumn = (ColumnRefOperator) condition.getChild(1);
            
            ColumnStatistic leftColStats = leftStats.getColumnStatistic(leftColumn);
            ColumnStatistic rightColStats = rightStats.getColumnStatistic(rightColumn);
            
            // 等值连接选择率估算
            if (condition.getBinaryType() == BinaryType.EQ) {
                double leftNdv = leftColStats.getDistinctValuesCount();
                double rightNdv = rightColStats.getDistinctValuesCount();
                selectivity *= 1.0 / Math.max(leftNdv, rightNdv);
            }
        }
        
        double outputRowCount = leftStats.getRowCount() * rightStats.getRowCount() * selectivity;
        return new Statistics(outputRowCount);
    }
}

public class ColumnStatistic {
    private double minValue;
    private double maxValue;
    private double nullsFraction;
    private double distinctValuesCount;
    private double averageRowSize;
    
    public ColumnStatistic(double minValue, double maxValue, 
                          double nullsFraction, double distinctValuesCount,
                          double averageRowSize) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.nullsFraction = nullsFraction;
        this.distinctValuesCount = distinctValuesCount;
        this.averageRowSize = averageRowSize;
    }
    
    // 过滤条件选择率估算
    public double estimateSelectivity(BinaryPredicateOperator predicate) {
        if (predicate.getBinaryType() == BinaryType.EQ) {
            return 1.0 / distinctValuesCount;
        } else if (predicate.getBinaryType() == BinaryType.LT ||
                   predicate.getBinaryType() == BinaryType.LE) {
            LiteralExpr literal = (LiteralExpr) predicate.getChild(1);
            double value = literal.getDoubleValue();
            return (value - minValue) / (maxValue - minValue);
        } else if (predicate.getBinaryType() == BinaryType.GT ||
                   predicate.getBinaryType() == BinaryType.GE) {
            LiteralExpr literal = (LiteralExpr) predicate.getChild(1);
            double value = literal.getDoubleValue();
            return (maxValue - value) / (maxValue - minValue);
        }
        
        return 0.1; // 默认选择率
    }
}
```

### 4.4.3 动态规划搜索

CBO使用动态规划算法搜索最优计划：

```java
public class CascadesOptimizer {
    private Memo memo;
    private OptimizerContext context;
    
    public OptExpression optimize(OptExpression root, OptimizerContext context) {
        this.context = context;
        this.memo = new Memo();
        
        // 1. 将逻辑计划插入Memo
        Group rootGroup = memo.copyIn(root);
        
        // 2. 优化根Group
        optimizeGroup(rootGroup, PhysicalProperty.ANY);
        
        // 3. 提取最优计划
        return rootGroup.getBestExpression(PhysicalProperty.ANY)
                       .extractOptExpression();
    }
    
    private double optimizeGroup(Group group, PhysicalProperty requiredProperty) {
        // 检查是否已经优化过
        if (group.hasBestExpression(requiredProperty)) {
            return group.getLowestCost(requiredProperty);
        }
        
        double lowestCost = Double.MAX_VALUE;
        GroupExpression bestExpression = null;
        
        // 1. 探索逻辑表达式（应用转换规则）
        exploreGroup(group);
        
        // 2. 实现逻辑表达式（生成物理表达式）
        implementGroup(group);
        
        // 3. 优化物理表达式
        for (GroupExpression physicalExpr : group.getPhysicalExpressions()) {
            double cost = optimizeExpression(physicalExpr, requiredProperty);
            
            if (cost < lowestCost) {
                lowestCost = cost;
                bestExpression = physicalExpr;
            }
        }
        
        // 4. 缓存最优结果
        group.setBestExpression(requiredProperty, bestExpression, lowestCost);
        
        return lowestCost;
    }
    
    private double optimizeExpression(GroupExpression expression,
                                    PhysicalProperty requiredProperty) {
        
        // 1. 推导子节点需要的物理属性
        List<PhysicalProperty> childrenProperties = 
            deriveChildrenProperties(expression, requiredProperty);
        
        // 2. 递归优化子节点
        double childrenCost = 0;
        for (int i = 0; i < expression.getChildGroups().size(); i++) {
            Group childGroup = expression.getChildGroups().get(i);
            PhysicalProperty childProperty = childrenProperties.get(i);
            
            childrenCost += optimizeGroup(childGroup, childProperty);
        }
        
        // 3. 计算当前算子的代价
        double operatorCost = context.getCostEstimator()
                                    .calculateCost(expression, childrenProperties, 
                                                 requiredProperty);
        
        return childrenCost + operatorCost;
    }
}
```

## 4.5 物化视图优化

### 4.5.1 物化视图改写框架

基于`MaterializedViewOptimizer.java`的分析：

```java
public class MaterializedViewOptimizer {
    private final List<MaterializedView> candidateViews;
    
    public OptExpression optimize(OptExpression queryPlan, OptimizerContext context) {
        // 1. 收集候选物化视图
        List<MaterializedView> candidates = collectCandidateViews(queryPlan);
        
        // 2. 尝试改写查询
        for (MaterializedView mv : candidates) {
            OptExpression rewrittenPlan = tryRewrite(queryPlan, mv, context);
            if (rewrittenPlan != null) {
                // 3. 比较代价，选择更优的计划
                double originalCost = estimateCost(queryPlan, context);
                double rewrittenCost = estimateCost(rewrittenPlan, context);
                
                if (rewrittenCost < originalCost) {
                    queryPlan = rewrittenPlan;
                }
            }
        }
        
        return queryPlan;
    }
    
    private OptExpression tryRewrite(OptExpression queryPlan, 
                                   MaterializedView mv,
                                   OptimizerContext context) {
        
        // 1. 检查查询模式是否匹配
        if (!isPatternMatched(queryPlan, mv)) {
            return null;
        }
        
        // 2. 检查谓词是否可以改写
        if (!canRewritePredicates(queryPlan, mv)) {
            return null;
        }
        
        // 3. 执行改写
        return performRewrite(queryPlan, mv, context);
    }
}
```

### 4.5.2 SPJG模式匹配

SPJG(Select-Project-Join-GroupBy)模式匹配是物化视图改写的核心：

```java
public class SPJGMatcher {
    public boolean match(OptExpression query, MaterializedView mv) {
        // 1. 提取查询的SPJG结构
        SPJGPattern queryPattern = extractSPJGPattern(query);
        SPJGPattern mvPattern = extractSPJGPattern(mv.getQueryPlan());
        
        // 2. 检查表匹配
        if (!isTableMatched(queryPattern.getTables(), mvPattern.getTables())) {
            return false;
        }
        
        // 3. 检查连接条件匹配
        if (!isJoinMatched(queryPattern.getJoinConditions(), 
                          mvPattern.getJoinConditions())) {
            return false;
        }
        
        // 4. 检查投影匹配
        if (!isProjectionMatched(queryPattern.getProjections(), 
                                mvPattern.getProjections())) {
            return false;
        }
        
        // 5. 检查分组匹配
        if (!isGroupByMatched(queryPattern.getGroupBy(), 
                             mvPattern.getGroupBy())) {
            return false;
        }
        
        return true;
    }
    
    private SPJGPattern extractSPJGPattern(OptExpression expression) {
        SPJGPattern pattern = new SPJGPattern();
        
        // 递归遍历表达式树，提取SPJG信息
        extractPattern(expression, pattern);
        
        return pattern;
    }
}
```

## 小结

StarRocks的查询优化器采用了现代数据库的先进技术，通过Memo数据结构、RBO和CBO相结合的优化策略，以及物化视图自动改写等技术，实现了高效的查询优化。其设计特点包括：

1. **Memo结构**: 高效的搜索空间表示和管理
2. **多阶段优化**: RBO和CBO相结合的优化策略
3. **代价模型完善**: 考虑多种资源的代价估算
4. **统计信息驱动**: 基于统计信息的智能优化决策
5. **物化视图支持**: 自动的查询改写和优化

在下一章中，我们将深入分析物化视图自动改写的具体实现机制。
