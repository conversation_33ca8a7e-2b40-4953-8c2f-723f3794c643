# 第六章：查询计划生成与分片策略

## 1. 引言

经过查询优化器的千挑万选，我们得到了一个成本最低的、由物理算子组成的执行计划树。然而，这个计划仍然是一个逻辑上的、集中的“树”，它还没有考虑分布式环境下的并行执行问题。计划生成器（Planner）的职责，就是将这个物理计划树，转换为一个可以在MPP集群上真正执行的、由多个并行片段（Fragment）组成的分布式物理计划。本章将深入StarRocks的计划生成模块，探讨从物理计划树到分布式执行计划的转换过程，以及其核心的分片策略。

## 2. 计划生成的核心任务

计划生成器的核心任务是将单机的物理计划**“分布式化”**。这主要包含两个方面：

1.  **计划分片（Plan Fragmentation）**：根据数据交换（Data Exchange）的需求，将物理计划树切割成若干个可以独立执行的子计划，即**PlanFragment**。每个Fragment内部没有数据交换，可以作为一个整体在BE节点上执行。
2.  **数据交换（Data Exchange）**：在Fragment的切分边界，插入用于数据传输的`ExchangeNode`。`ExchangeNode`负责将下游Fragment的计算结果，通过网络发送给上游Fragment。它定义了数据分发的方式（如广播、哈希重分布）。

最终生成的分布式计划，是一个由PlanFragment组成的有向无环图（DAG），Fragment之间通过Exchange操作连接。

## 3. 源码分析：从树到DAG

核心参考源码：
*   `fe/fe-core/src/main/java/com/starrocks/sql/StatementPlanner.java`
*   `fe/fe-core/src/main/java/com/starrocks/planner/Planner.java`
*   `fe/fe-core/src/main/java/com/starrocks/planner/PlanFragment.java`
*   `fe/fe-core/src/main/java/com/starrocks/planner/ExchangeNode.java`

### 3.1. `StatementPlanner`与`Planner`

`StatementPlanner.java`是SQL语句规划的入口，它调用`Planner.java`来完成核心的分布式计划生成工作。`Planner`的入口方法是`createPlanFragments`。

```java
// Planner.java (Simplified)
public class Planner {
    public List<PlanFragment> createPlanFragments(PhysicalPlan plan, ...) {
        // ...
        // The root of the physical plan tree is the starting point
        PlanNode root = plan.getPhysicalPlan();

        // Create the root fragment
        PlanFragment rootFragment = new PlanFragment(...);
        rootFragment.setPlanRoot(root);

        // Recursively traverse the plan tree and create fragments
        createFragment(root, rootFragment, ...);

        // ...
        return fragments;
    }
}
```

### 3.2. 分片的核心逻辑

`Planner`通过自顶向下的方式遍历物理计划树来创建Fragment。其核心逻辑在`createFragment`方法中。

切分的时机发生在遇到会产生数据重分布（Shuffle）的物理算子时。在MPP数据库中，最典型的需要Shuffle的算子就是**Hash Join**。

当`Planner`遍历到一个`PhysicalHashJoinNode`时：
1.  它会为Join的右子树（Build-side）创建一个新的`PlanFragment`。
2.  在这个新的Fragment的根节点，放置一个`ExchangeNode`。这个`ExchangeNode`负责将右子树的数据进行**哈希重分布**（`DataPartition.HASH_PARTITIONED`），分发策略基于Join Key。
3.  Join的左子树（Probe-side）则保留在当前的Fragment中。
4.  原`PhysicalHashJoinNode`的右孩子，被替换成一个指向新创建的Fragment的`ScanNode`（特指`ExchangeNode`），表示它的输入来自于网络。

```java
// Planner.java - createFragment for a JoinNode (Conceptual)
private void createFragment(PlanNode node, PlanFragment currentFragment, ...) {
    if (node instanceof PhysicalHashJoinNode) {
        PhysicalHashJoinNode joinNode = (PhysicalHashJoinNode) node;

        // Right child (build side) forms a new fragment
        PlanFragment buildFragment = new PlanFragment(...);
        // The new fragment's output is hash-partitioned on the join key
        buildFragment.setOutputPartition(DataPartition.hashPartitioned(joinNode.getJoinOnPredicate()));

        // Add an ExchangeNode on top of the build side's plan
        ExchangeNode exchangeNode = new ExchangeNode(..., buildFragment.getPlanRoot());
        buildFragment.setPlanRoot(exchangeNode);

        // Recursively create fragments for the build side
        createFragment(joinNode.getChild(1), buildFragment, ...);

        // The join node's right child is now the exchange node
        joinNode.setChild(1, exchangeNode);
    }

    // Continue traversing for the left child in the current fragment
    createFragment(node.getChild(0), currentFragment, ...);
}
```

除了Join，其他需要数据交换的场景，如聚合（`GROUP BY`）的第一阶段（本地聚合）和第二阶段（全局聚合），也会触发Fragment的切分。

### 3.3. `PlanFragment`与`ExchangeNode`

*   **`PlanFragment`**: 一个Fragment是一个最小的、可并行的调度单元。它包含了一个由物理算子组成的计划树（`planRoot`），以及关于数据如何输入（`dataPartition`）和如何输出（`outputPartition`）的描述。FE会将一个Fragment作为一个整体，调度到多个BE节点上并行执行。

*   **`ExchangeNode`**: 它是Fragment之间的粘合剂。它位于一个Fragment的顶部，负责将该Fragment的执行结果发送给其父Fragment。它也位于一个Fragment的叶子节点（作为`ScanNode`的替代），负责接收来自其子Fragment的数据。

## 4. 分布式计划示例

考虑一个简单的两表Join查询：`SELECT * FROM T1 JOIN T2 ON T1.id = T2.id;`

其分布式计划可能如下所示：

```
Fragment 2 (Root)
  - OlapScanNode(T1)
  - HashJoinNode(on id) <-- Probe Side
      - ExchangeNode (receives from Fragment 0 and 1)

Fragment 1 (Leaf)
  - OlapScanNode(T2)
  - ExchangeNode (sends to Fragment 2, HASH_PARTITIONED on T2.id)

Fragment 0 (Leaf)
  - OlapScanNode(T1)
  - ExchangeNode (sends to Fragment 2, HASH_PARTITIONED on T1.id)
```
*在这个简化的例子中，为了执行Join，T1和T2的数据都需要根据Join Key `id`进行重分布。Planner会创建两个叶子Fragment分别扫描T1和T2，并通过ExchangeNode将数据哈希发送到执行Join的Fragment。*
(Note: A more accurate plan would likely involve one side being broadcast or both being shuffled to the join nodes, the above is a conceptual illustration of fragmentation).

一个更典型的计划是：
```
Fragment 1 (Root)
  - HashJoinNode
    - OlapScanNode(T1)  <-- Probe Side
    - ExchangeNode (receives from Fragment 0) <-- Build Side

Fragment 0 (Leaf)
  - OlapScanNode(T2)
  - ExchangeNode (sends to Fragment 1, HASH_PARTITIONED on T2.id)
```
在这个计划中，`Fragment 0`在多个BE上并行执行，扫描`T2`的部分数据，然后通过网络将数据根据`T2.id`哈希Shuffle给`Fragment 1`。`Fragment 1`也在多个BE上并行执行，它接收来自`Fragment 0`的数据构建哈希表，然后扫描本地的`T1`数据进行探测和连接。

## 5. 总结

计划生成与分片是将一个逻辑上的、单机的最优计划，转变为一个物理上、分布式的可执行蓝图的关键步骤。StarRocks通过在数据交换点（如Join、Aggregate）切分物理计划树，创建出多个并行的`PlanFragment`，并通过`ExchangeNode`将它们连接起来，形成一个执行DAG。这个分布式计划充分利用了MPP架构的并行处理能力，是StarRocks实现高性能查询的基础。
