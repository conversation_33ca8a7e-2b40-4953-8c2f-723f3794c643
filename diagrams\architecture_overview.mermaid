graph TD
    subgraph StarRocks Cluster
        subgraph Frontend (FE)
            direction LR
            Parser[SQL Parser] --> Analy<PERSON>[Analy<PERSON>]
            Analyzer --> Optimizer[Query Optimizer]
            Optimizer --> Planner[Planner]
            Planner --> Coordinator[Coordinator]
            Coordinator -- Thrift/gRPC --> BEs[Backends]
            GlobalMetadata[Global Metadata] -- BDBJE --> FEs[FE Followers]
            FEs -- BDBJE --> GlobalMetadata
        end

        subgraph Backend (BE)
            direction TB
            Coordinator -- Plan Fragments --> FragmentExecutor[Fragment Executor]
            FragmentExecutor --> PipelineDriver[Pipeline Driver]
            PipelineDriver --> Operators[Vectorized Operators]
            Operators -- Read/Write --> StorageEngine[Storage Engine]
            StorageEngine -- CBO Stats --> FE
        end

        subgraph Storage
            direction TB
            StorageEngine --> Tablet[Tablet]
            Tablet --> Segment[Segment Files]
            Segment --> ColumnStore[Columnar Data]
            Segment -- Indexes --> Indexes[Short Key, Bloom Filter, etc.]
        end
    end

    Client[SQL Client] --> FE
    FE --> Client